import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';

/// Manages audio cache for TTS audio files
/// Provides secure app-private storage for cached audio files
class AudioCacheManager {
  static const String _cacheDirectoryName = 'tts_audio_cache';
  static const String _audioFileExtension = '.mp3';
  static const int _maxCacheSizeBytes = 50 * 1024 * 1024; // 50MB max cache size
  
  Directory? _cacheDirectory;
  
  /// Initialize the cache directory
  Future<void> _initializeCacheDirectory() async {
    if (_cacheDirectory != null) return;
    
    try {
      final appDocDir = await getApplicationDocumentsDirectory();
      _cacheDirectory = Directory('${appDocDir.path}/$_cacheDirectoryName');
      
      if (!await _cacheDirectory!.exists()) {
        await _cacheDirectory!.create(recursive: true);
      }
    } catch (e) {
      debugPrint('Error initializing cache directory: $e');
      rethrow;
    }
  }
  
  /// Get cached audio file path for a message ID
  /// Returns null if no cached audio exists
  Future<String?> getCachedAudio(String messageId) async {
    try {
      await _initializeCacheDirectory();
      
      final fileName = _generateFileName(messageId);
      final filePath = '${_cacheDirectory!.path}/$fileName';
      final file = File(filePath);
      
      if (await file.exists()) {
        // Update access time by touching the file
        await file.setLastModified(DateTime.now());
        return filePath;
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting cached audio for message $messageId: $e');
      return null;
    }
  }
  
  /// Cache audio data for a message ID
  /// Stores audio in app-private directory with secure access
  Future<void> cacheAudio(String messageId, Uint8List audioData) async {
    try {
      await _initializeCacheDirectory();
      
      final fileName = _generateFileName(messageId);
      final filePath = '${_cacheDirectory!.path}/$fileName';
      final file = File(filePath);
      
      // Write audio data to file
      await file.writeAsBytes(audioData);
      
      debugPrint('Cached audio for message $messageId (${audioData.length} bytes)');
      
      // Check cache size and cleanup if necessary
      await _manageCacheSize();
      
    } catch (e) {
      debugPrint('Error caching audio for message $messageId: $e');
      rethrow;
    }
  }
  
  /// Delete specific cached audio file for a message ID
  /// Returns true if file was successfully deleted, false if file didn't exist
  Future<bool> deleteAudioFile(String messageId) async {
    try {
      await _initializeCacheDirectory();
      
      final fileName = _generateFileName(messageId);
      final filePath = '${_cacheDirectory!.path}/$fileName';
      final file = File(filePath);
      
      if (await file.exists()) {
        await file.delete();
        debugPrint('Deleted cached audio file for message $messageId');
        return true;
      } else {
        debugPrint('No cached audio file found for message $messageId');
        return false;
      }
      
    } catch (e) {
      debugPrint('Error deleting audio file for message $messageId: $e');
      rethrow;
    }
  }
  
  /// Clear all old cached audio files
  /// Called when new Groq responses arrive to prevent storage bloat
  Future<void> clearOldCache() async {
    try {
      await _initializeCacheDirectory();
      
      final files = await _cacheDirectory!.list().toList();
      int deletedCount = 0;
      int totalDeletedSize = 0;
      
      for (final entity in files) {
        if (entity is File && entity.path.endsWith(_audioFileExtension)) {
          try {
            final stat = await entity.stat();
            await entity.delete();
            deletedCount++;
            totalDeletedSize += stat.size;
          } catch (e) {
            debugPrint('Error deleting cached file ${entity.path}: $e');
          }
        }
      }
      
      debugPrint('Cleared $deletedCount cached audio files (${totalDeletedSize ~/ 1024}KB freed)');
      
    } catch (e) {
      debugPrint('Error clearing old cache: $e');
      rethrow;
    }
  }
  
  /// Clear cache files older than specified duration
  /// Useful for periodic cleanup to manage storage
  Future<void> clearCacheOlderThan(Duration maxAge) async {
    try {
      await _initializeCacheDirectory();
      
      final cutoffTime = DateTime.now().subtract(maxAge);
      final files = await _cacheDirectory!.list().toList();
      int deletedCount = 0;
      int totalDeletedSize = 0;
      
      for (final entity in files) {
        if (entity is File && entity.path.endsWith(_audioFileExtension)) {
          try {
            final stat = await entity.stat();
            if (stat.modified.isBefore(cutoffTime)) {
              await entity.delete();
              deletedCount++;
              totalDeletedSize += stat.size;
            }
          } catch (e) {
            debugPrint('Error processing cached file ${entity.path}: $e');
          }
        }
      }
      
      if (deletedCount > 0) {
        debugPrint('Cleaned up $deletedCount old cached files (${totalDeletedSize ~/ 1024}KB freed)');
      }
      
    } catch (e) {
      debugPrint('Error clearing old cache files: $e');
      rethrow;
    }
  }
  
  /// Generate secure filename for message ID
  String _generateFileName(String messageId) {
    // Use a simple hash-like approach to create consistent filenames
    final sanitized = messageId.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '_');
    return '${sanitized}_${messageId.hashCode.abs()}$_audioFileExtension';
  }
  
  /// Manage cache size to prevent storage bloat
  /// Implements LRU (Least Recently Used) eviction policy
  Future<void> _manageCacheSize() async {
    try {
      final files = await _cacheDirectory!.list().toList();
      final audioFiles = <File>[];
      int totalSize = 0;
      
      // Collect audio files and calculate total size
      for (final entity in files) {
        if (entity is File && entity.path.endsWith(_audioFileExtension)) {
          try {
            final file = entity;
            final stat = await file.stat();
            totalSize += stat.size;
            audioFiles.add(file);
          } catch (e) {
            debugPrint('Error processing file ${entity.path} during cache size check: $e');
            // Continue processing other files even if one fails
          }
        }
      }
      
      debugPrint('Cache size check: ${audioFiles.length} files, ${totalSize ~/ 1024}KB total');
      
      // If cache size exceeds limit, remove oldest files using LRU policy
      if (totalSize > _maxCacheSizeBytes) {
        await _performCacheEviction(audioFiles, totalSize);
      }
      
    } catch (e) {
      debugPrint('Error managing cache size: $e');
      // Don't rethrow to avoid breaking the caching operation
    }
  }
  
  /// Perform cache eviction using LRU (Least Recently Used) policy
  Future<void> _performCacheEviction(List<File> audioFiles, int totalSize) async {
    try {
      // Sort files by last modified time (oldest first) for LRU eviction
      audioFiles.sort((a, b) {
        try {
          final aStat = a.statSync();
          final bStat = b.statSync();
          return aStat.modified.compareTo(bStat.modified);
        } catch (e) {
          debugPrint('Error comparing file stats during sort: $e');
          return 0; // Keep original order if comparison fails
        }
      });
      
      int removedSize = 0;
      int removedCount = 0;
      final targetSize = (_maxCacheSizeBytes * 0.7).round(); // Target 70% of max size
      
      // Remove oldest files until we're under the target size
      for (final file in audioFiles) {
        if (totalSize - removedSize <= targetSize) break;
        
        try {
          final stat = await file.stat();
          final fileSize = stat.size;
          
          await file.delete();
          removedSize += fileSize;
          removedCount++;
          
          debugPrint('Evicted cached file: ${file.path} (${fileSize ~/ 1024}KB)');
          
        } catch (e) {
          debugPrint('Error removing cached file ${file.path}: $e');
          // Continue with next file even if this one fails
        }
      }
      
      if (removedCount > 0) {
        debugPrint('Cache eviction completed: removed $removedCount files (${removedSize ~/ 1024}KB freed)');
      }
      
    } catch (e) {
      debugPrint('Error during cache eviction: $e');
    }
  }
  
  /// Validate cache integrity and remove corrupted files
  Future<void> validateAndCleanCache() async {
    try {
      await _initializeCacheDirectory();
      
      final files = await _cacheDirectory!.list().toList();
      int corruptedCount = 0;
      int totalCorruptedSize = 0;
      
      for (final entity in files) {
        if (entity is File && entity.path.endsWith(_audioFileExtension)) {
          try {
            final file = entity;
            final stat = await file.stat();
            
            // Check if file is empty or suspiciously small (less than 1KB)
            if (stat.size < 1024) {
              debugPrint('Removing suspiciously small cached file: ${file.path} (${stat.size} bytes)');
              await file.delete();
              corruptedCount++;
              totalCorruptedSize += stat.size;
            }
            
            // Additional integrity checks could be added here
            // (e.g., checking file headers, attempting to read first few bytes)
            
          } catch (e) {
            debugPrint('Error validating cached file ${entity.path}: $e');
            // Try to remove the problematic file
            try {
              await entity.delete();
              corruptedCount++;
            } catch (deleteError) {
              debugPrint('Failed to remove corrupted file ${entity.path}: $deleteError');
            }
          }
        }
      }
      
      if (corruptedCount > 0) {
        debugPrint('Cache validation: removed $corruptedCount corrupted files ($totalCorruptedSize bytes)');
      }
      
    } catch (e) {
      debugPrint('Error during cache validation: $e');
      rethrow;
    }
  }
  
  /// Get current cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    try {
      await _initializeCacheDirectory();
      
      final files = await _cacheDirectory!.list().toList();
      int fileCount = 0;
      int totalSize = 0;
      
      for (final entity in files) {
        if (entity is File && entity.path.endsWith(_audioFileExtension)) {
          final stat = await entity.stat();
          totalSize += stat.size;
          fileCount++;
        }
      }
      
      return {
        'fileCount': fileCount,
        'totalSizeBytes': totalSize,
        'totalSizeKB': totalSize ~/ 1024,
        'maxSizeBytes': _maxCacheSizeBytes,
        'cacheDirectory': _cacheDirectory?.path,
      };
      
    } catch (e) {
      debugPrint('Error getting cache stats: $e');
      return {
        'fileCount': 0,
        'totalSizeBytes': 0,
        'totalSizeKB': 0,
        'maxSizeBytes': _maxCacheSizeBytes,
        'error': e.toString(),
      };
    }
  }
}