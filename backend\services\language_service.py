"""
Language processing service using Groq for German corrections and English translations.
Uses your custom prompt template for natural language responses.
"""

import logging
import time
import asyncio
from typing import Dict, Any
from groq import Groq

from config.settings import settings
from config.logging_config import get_logger

logger = get_logger(__name__)

class LanguageServiceError(Exception):
    """Base exception for language service errors."""
    pass

class LanguageService:
    """
    Service for processing text using Groq API with your custom prompt template.
    """
    
    def __init__(self):
        """Initialize the language service."""
        self.groq_client = None
        if settings.groq_api_key:
            self.groq_client = Groq(api_key=settings.groq_api_key)
        else:
            logger.error("Groq API key not configured")
    

    
    async def process_text(self, text: str) -> Dict[str, Any]:
        """
        Process text using German correction prompt.
        
        Args:
            text: The text to process
            
        Returns:
            Dict[str, Any]: The processing result
        """
        if not self.groq_client:
            raise LanguageServiceError("Groq client not initialized")
        
        start_time = time.time()
        request_id = f"req_{int(time.time() * 1000)}"
        
        try:
            logger.debug(f"Processing text (ID: {request_id}, length: {len(text)})")
            
            result_text = await self._process_text_with_groq(text)
            
            processing_time = time.time() - start_time
            
            logger.info(f"Text processing completed (ID: {request_id}) in {processing_time:.3f}s")
            
            return {
                "original_text": text,
                "processed_content": {
                    "response_text": result_text,
                    "type": "correction"
                },
                "processing_time": processing_time,
                "agent_used": "german_correction",
                "request_id": request_id
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Error processing text (ID: {request_id}): {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            return {
                "original_text": text,
                "error": str(e),
                "processing_time": processing_time,
                "request_id": request_id
            }
    
    async def _process_text_with_groq(self, text: str) -> str:
        """
        Process text using the German correction prompt.
        
        Args:
            text: The text to process
            
        Returns:
            str: The complete response from Groq
        """
        try:
            prompt = f"""Consider this phrase:
"{text}"

Write two German versions that are correct and sound natural, pleasant and idiomatic to native German speakers. Call this section: "Correction:". You should not use English words, only German words. Each German version should be enclosed in curly braces {{}}, be in bold, and in bullet points *.

Explain the grammar corrections you made briefly. Call it "Explanation:"

Add an example of a paragraph that contains the phrase. The paragraph should be something that a normal German would say in normal, daily conversation. Call it "Example:" Also with English translation.

Add a related and useful grammar or vocabulary tip with respect to German nouns, adjectives, or verbs in the sentence. Call it "Oma's Tip:".
"""

            # Run Groq API call in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            completion = await loop.run_in_executor(
                None,
                lambda: self.groq_client.chat.completions.create(
                    model="gemma2-9b-it",
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.2,
                    max_completion_tokens=1024,
                    top_p=1,
                    stream=False,
                    stop=None
                )
            )
            
            return completion.choices[0].message.content.strip()
                
        except Exception as e:
            logger.error(f"Error in text processing: {str(e)}")
            return f"Error processing text: {str(e)}"

# Global instance
language_service = LanguageService()