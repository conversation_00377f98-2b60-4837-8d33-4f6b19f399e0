# Design Document

## Overview

This design implements text-to-speech (TTS) functionality for Groq responses using Google Cloud Text-to-Speech API. The system extracts the first sentence from Groq responses (contained within curly braces), provides an audio playback interface with professional UI, and implements efficient caching to minimize API costs. The architecture follows enterprise-grade patterns with proper separation of concerns, error handling, and resource management.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Flutter Frontend] --> B[Message Bubble Widget]
    B --> C[Audio Icon Component]
    C --> D[TTS Audio Service]
    D --> E[Audio Cache Manager]
    D --> F[Backend TTS API]
    F --> G[Google Cloud TTS Service]
    E --> H[Local Audio Storage]
    
    subgraph "Frontend Layer"
        A
        B
        C
        D
        E
        H
    end
    
    subgraph "Backend Layer"
        F
        G
    end
```

### Component Interaction Flow

```mermaid
sequenceDiagram
    participant U as User
    participant MB as Message Bubble
    participant AI as Audio Icon
    participant TTS as TTS Service
    participant Cache as Audio Cache
    participant API as Backend API
    participant GCP as Google Cloud TTS
    
    U->>MB: Groq response received
    MB->>MB: Extract first sentence from {}
    MB->>AI: Show audio icon
    U->>AI: Click audio icon (first time)
    AI->>Cache: Check for cached audio
    Cache-->>AI: Not found
    AI->>TTS: Request TTS
    TTS->>API: POST /api/tts
    API->>GCP: Generate speech
    GCP-->>API: Audio data
    API-->>TTS: Audio response
    TTS->>Cache: Store audio file
    TTS->>AI: Play audio
    AI->>U: Audio playback
    
    U->>AI: Click audio icon (second time)
    AI->>Cache: Check for cached audio
    Cache-->>AI: Found cached audio
    AI->>AI: Play cached audio
    AI->>U: Audio playback
```

## Components and Interfaces

### Frontend Components

#### 1. Enhanced Message Bubble Widget
**Location**: `flutter_frontend/lib/presentation/widgets/chat/message_bubble.dart`

**Responsibilities**:
- Extract first sentence from curly braces in Groq responses
- Display audio icon when sentence is available
- Manage layout adjustments for audio icon placement

**Key Methods**:
```dart
class MessageBubble extends StatelessWidget {
  String? _extractFirstSentenceFromBraces(String text);
  Widget _buildAudioIcon(BuildContext context, String sentence);
  Widget _buildMessageWithAudioIcon(BuildContext context);
}
```

#### 2. Audio Icon Component
**Location**: `flutter_frontend/lib/presentation/widgets/chat/audio_icon_widget.dart`

**Responsibilities**:
- Display audio/speaker icon with state management
- Handle user interactions (tap events)
- Show visual feedback (loading, playing, error states)
- Coordinate with TTS service

**States**:
- `idle`: Default state, ready to play
- `loading`: Requesting TTS from backend
- `playing`: Audio is currently playing
- `error`: Error occurred during TTS or playback

**Key Methods**:
```dart
class AudioIconWidget extends StatefulWidget {
  void _handleTap();
  void _updateState(AudioIconState state);
  Widget _buildIcon(AudioIconState state);
}
```

#### 3. TTS Audio Service
**Location**: `flutter_frontend/lib/data/services/tts_audio_service.dart`

**Responsibilities**:
- Coordinate TTS requests with backend
- Manage audio playback using Flutter's audio players
- Interface with audio cache manager
- Handle audio session management

**Key Methods**:
```dart
class TTSAudioService {
  Future<void> playTTS(String text, String messageId);
  Future<void> stopPlayback();
  Future<bool> _requestTTSFromBackend(String text);
  void _playAudioFile(String filePath);
}
```

#### 4. Audio Cache Manager
**Location**: `flutter_frontend/lib/data/services/audio_cache_manager.dart`

**Responsibilities**:
- Store and retrieve cached audio files
- Clean up old audio files when new responses arrive
- Manage storage space and file lifecycle

**Key Methods**:
```dart
class AudioCacheManager {
  Future<String?> getCachedAudio(String messageId);
  Future<void> cacheAudio(String messageId, Uint8List audioData);
  Future<void> clearOldCache();
  Future<void> deleteAudioFile(String messageId);
}
```

### Backend Components

#### 1. TTS API Endpoint
**Location**: `backend/services/tts_service.py`

**Responsibilities**:
- Handle TTS requests from frontend
- Interface with Google Cloud Text-to-Speech API
- Implement proper error handling and logging
- Return audio data in appropriate format

**API Endpoint**:
```python
@app.post("/api/tts")
async def generate_speech(request: TTSRequest) -> TTSResponse
```

**Key Methods**:
```python
class TTSService:
    async def generate_speech(self, text: str) -> bytes;
    def _validate_text_input(self, text: str) -> bool;
    def _configure_voice_settings(self) -> VoiceSelectionParams;
    def _configure_audio_config(self) -> AudioConfig;
```

#### 2. Google Cloud TTS Integration
**Location**: `backend/services/google_tts_client.py`

**Responsibilities**:
- Direct integration with Google Cloud Text-to-Speech API
- Handle authentication and API key management
- Implement retry logic and error handling
- Configure voice parameters according to requirements

**Configuration**:
- Voice: `de-DE-Chirp3-HD-Aoede`
- Speed: `0.9`
- Audio format: `MP3` (optimized for mobile)
- Sample rate: `24000` Hz

## Data Models

### Frontend Models

#### TTSRequest Model
```dart
class TTSRequest {
  final String text;
  final String messageId;
  final String voiceConfig;
  
  TTSRequest({
    required this.text,
    required this.messageId,
    this.voiceConfig = 'de-DE-Chirp3-HD-Aoede',
  });
}
```

#### AudioCacheEntry Model
```dart
class AudioCacheEntry {
  final String messageId;
  final String filePath;
  final DateTime createdAt;
  final int fileSizeBytes;
  
  AudioCacheEntry({
    required this.messageId,
    required this.filePath,
    required this.createdAt,
    required this.fileSizeBytes,
  });
}
```

### Backend Models

#### TTSRequest Model
```python
class TTSRequest(BaseModel):
    text: str = Field(..., min_length=1, max_length=500)
    message_id: str = Field(..., min_length=1)
    voice_config: str = Field(default="de-DE-Chirp3-HD-Aoede")
```

#### TTSResponse Model
```python
class TTSResponse(BaseModel):
    audio_data: bytes
    content_type: str = "audio/mpeg"
    duration_seconds: float
    processing_time: float
```

## Error Handling

### Frontend Error Handling

#### TTS Service Errors
- **Network Errors**: Display retry button, implement exponential backoff
- **Backend Errors**: Show user-friendly error message, log technical details
- **Audio Playback Errors**: Provide fallback options, clear error states
- **Cache Errors**: Gracefully degrade to direct API calls (with user consent)

#### Error States for Audio Icon
- **Loading Timeout**: Show error icon after 10 seconds
- **Playback Failure**: Display error state with retry option
- **Cache Corruption**: Clear cache and retry with fresh request

### Backend Error Handling

#### Google Cloud TTS Errors
- **Authentication Errors**: Log securely, return generic error to client
- **Quota Exceeded**: Return specific error code, implement rate limiting
- **Invalid Text**: Validate input, return descriptive error messages
- **Service Unavailable**: Implement circuit breaker pattern

#### Error Response Format
```python
class TTSErrorResponse(BaseModel):
    error_code: str
    error_message: str
    retry_after: Optional[int] = None
    correlation_id: str
```

## Testing Strategy

### Frontend Testing

#### Unit Tests
- **Text Extraction**: Test curly brace sentence extraction logic
- **Cache Manager**: Test cache storage, retrieval, and cleanup
- **Audio Service**: Mock backend calls, test state management
- **Audio Icon**: Test state transitions and user interactions

#### Widget Tests
- **Message Bubble**: Test audio icon positioning and layout
- **Audio Icon**: Test visual states and tap handling
- **Integration**: Test complete TTS flow with mocked services

#### Integration Tests
- **End-to-End TTS Flow**: Test complete user journey
- **Cache Behavior**: Test cache hit/miss scenarios
- **Error Scenarios**: Test error handling and recovery

### Backend Testing

#### Unit Tests
- **TTS Service**: Test Google Cloud API integration
- **Input Validation**: Test text validation and sanitization
- **Error Handling**: Test various error scenarios
- **Audio Processing**: Test audio format and quality

#### API Tests
- **Endpoint Testing**: Test TTS API endpoint with various inputs
- **Authentication**: Test API key validation
- **Rate Limiting**: Test request throttling
- **Performance**: Test response times and throughput

#### Integration Tests
- **Google Cloud Integration**: Test actual TTS API calls
- **Error Recovery**: Test service resilience
- **Load Testing**: Test under concurrent requests

### Performance Testing

#### Frontend Performance
- **Audio Playback Latency**: Measure time from tap to audio start
- **Cache Performance**: Test cache hit rates and retrieval speed
- **Memory Usage**: Monitor audio file memory consumption
- **UI Responsiveness**: Ensure smooth animations during loading

#### Backend Performance
- **TTS Response Time**: Target < 2 seconds for typical sentences
- **Concurrent Requests**: Test multiple simultaneous TTS requests
- **Memory Management**: Monitor memory usage during audio processing
- **API Rate Limits**: Test Google Cloud TTS quota management

## Security Considerations

### Frontend Security
- **Audio File Storage**: Use app-private directories only
- **Input Validation**: Sanitize text before sending to backend
- **Cache Security**: Encrypt cached audio files if containing sensitive data
- **Network Security**: Use HTTPS for all API communications

### Backend Security
- **API Key Management**: Use Google Secret Manager for TTS API keys
- **Input Sanitization**: Validate and sanitize all text inputs
- **Rate Limiting**: Implement per-user rate limiting to prevent abuse
- **Audit Logging**: Log all TTS requests for monitoring and debugging

### Google Cloud TTS Security
- **Authentication**: Use service account with minimal required permissions
- **Network Security**: Restrict API access to backend services only
- **Data Privacy**: Ensure text data is not logged or stored by Google
- **Compliance**: Follow Google Cloud security best practices

## Deployment Considerations

### Frontend Deployment
- **Audio Permissions**: Ensure proper audio playback permissions
- **Storage Permissions**: Configure app-private storage access
- **Platform Differences**: Handle iOS/Android audio session differences
- **App Store Guidelines**: Comply with audio feature requirements

### Backend Deployment
- **Environment Variables**: Configure Google Cloud credentials securely
- **Service Account**: Deploy with appropriate TTS API permissions
- **Monitoring**: Set up alerts for TTS API usage and errors
- **Scaling**: Configure auto-scaling for TTS request handling

### Production Monitoring
- **TTS Usage Metrics**: Monitor API call volume and costs
- **Error Rates**: Track TTS failures and error patterns
- **Performance Metrics**: Monitor response times and user satisfaction
- **Cost Optimization**: Implement usage alerts and budget controls