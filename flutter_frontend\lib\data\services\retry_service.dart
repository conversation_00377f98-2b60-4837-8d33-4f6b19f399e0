import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';

/// Service for handling retry mechanisms with exponential backoff
class RetryService {
  static final RetryService _instance = RetryService._();
  static RetryService get instance => _instance;
  
  RetryService._();
  
  /// Execute a function with retry logic and exponential backoff
  Future<T> executeWithRetry<T>({
    required Future<T> Function() operation,
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffMultiplier = 2.0,
    Duration maxDelay = const Duration(seconds: 30),
    bool Function(dynamic error)? shouldRetry,
    void Function(int attempt, dynamic error)? onRetry,
  }) async {
    int attempt = 0;
    Duration currentDelay = initialDelay;
    
    while (attempt <= maxRetries) {
      try {
        final result = await operation();
        return result;
      } catch (error) {
        attempt++;
        
        if (kDebugMode) {
          print('Retry attempt $attempt failed: $error');
        }
        
        // Check if we should retry this error
        if (shouldRetry != null && !shouldRetry(error)) {
          rethrow;
        }
        
        // If this was the last attempt, rethrow the error
        if (attempt > maxRetries) {
          rethrow;
        }
        
        // Call retry callback
        onRetry?.call(attempt, error);
        
        // Wait before retrying with exponential backoff
        await Future.delayed(currentDelay);
        
        // Calculate next delay with jitter to avoid thundering herd
        final jitter = Random().nextDouble() * 0.1; // 0-10% jitter
        currentDelay = Duration(
          milliseconds: (currentDelay.inMilliseconds * backoffMultiplier * (1 + jitter)).round(),
        );
        
        // Cap the delay at maxDelay
        if (currentDelay > maxDelay) {
          currentDelay = maxDelay;
        }
      }
    }
    
    throw StateError('This should never be reached');
  }
  
  /// Execute with retry for network operations
  Future<T> executeNetworkOperation<T>({
    required Future<T> Function() operation,
    int maxRetries = 3,
    void Function(int attempt, dynamic error)? onRetry,
  }) async {
    return executeWithRetry<T>(
      operation: operation,
      maxRetries: maxRetries,
      initialDelay: const Duration(seconds: 1),
      backoffMultiplier: 2.0,
      maxDelay: const Duration(seconds: 10),
      shouldRetry: (error) => _isRetryableNetworkError(error),
      onRetry: onRetry,
    );
  }
  
  /// Execute with retry for WebSocket operations
  Future<T> executeWebSocketOperation<T>({
    required Future<T> Function() operation,
    int maxRetries = 5,
    void Function(int attempt, dynamic error)? onRetry,
  }) async {
    return executeWithRetry<T>(
      operation: operation,
      maxRetries: maxRetries,
      initialDelay: const Duration(milliseconds: 500),
      backoffMultiplier: 1.5,
      maxDelay: const Duration(seconds: 5),
      shouldRetry: (error) => _isRetryableWebSocketError(error),
      onRetry: onRetry,
    );
  }
  
  /// Execute with retry for audio operations
  Future<T> executeAudioOperation<T>({
    required Future<T> Function() operation,
    int maxRetries = 2,
    void Function(int attempt, dynamic error)? onRetry,
  }) async {
    return executeWithRetry<T>(
      operation: operation,
      maxRetries: maxRetries,
      initialDelay: const Duration(milliseconds: 200),
      backoffMultiplier: 1.5,
      maxDelay: const Duration(seconds: 2),
      shouldRetry: (error) => _isRetryableAudioError(error),
      onRetry: onRetry,
    );
  }
  
  /// Check if a network error is retryable
  bool _isRetryableNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // Retryable network errors
    if (errorString.contains('timeout') ||
        errorString.contains('connection refused') ||
        errorString.contains('network unreachable') ||
        errorString.contains('temporary failure') ||
        errorString.contains('socket exception') ||
        errorString.contains('handshake exception')) {
      return true;
    }
    
    // HTTP status codes that are retryable
    if (errorString.contains('500') || // Internal Server Error
        errorString.contains('502') || // Bad Gateway
        errorString.contains('503') || // Service Unavailable
        errorString.contains('504') || // Gateway Timeout
        errorString.contains('429')) {  // Too Many Requests
      return true;
    }
    
    return false;
  }
  
  /// Check if a WebSocket error is retryable
  bool _isRetryableWebSocketError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // Retryable WebSocket errors
    if (errorString.contains('connection closed') ||
        errorString.contains('connection failed') ||
        errorString.contains('websocket') ||
        errorString.contains('network') ||
        errorString.contains('timeout')) {
      return true;
    }
    
    return _isRetryableNetworkError(error);
  }
  
  /// Check if an audio error is retryable
  bool _isRetryableAudioError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // Retryable audio errors
    if (errorString.contains('audio session') ||
        errorString.contains('recording failed') ||
        errorString.contains('microphone busy') ||
        errorString.contains('audio focus')) {
      return true;
    }
    
    // Non-retryable audio errors
    if (errorString.contains('permission') ||
        errorString.contains('not supported') ||
        errorString.contains('hardware')) {
      return false;
    }
    
    return true; // Default to retryable for audio operations
  }
  
  /// Create a retry configuration for different operation types
  RetryConfig createConfig({
    required RetryType type,
    int? maxRetries,
    Duration? initialDelay,
    double? backoffMultiplier,
    Duration? maxDelay,
  }) {
    switch (type) {
      case RetryType.network:
        return RetryConfig(
          maxRetries: maxRetries ?? 3,
          initialDelay: initialDelay ?? const Duration(seconds: 1),
          backoffMultiplier: backoffMultiplier ?? 2.0,
          maxDelay: maxDelay ?? const Duration(seconds: 10),
          shouldRetry: _isRetryableNetworkError,
        );
      
      case RetryType.websocket:
        return RetryConfig(
          maxRetries: maxRetries ?? 5,
          initialDelay: initialDelay ?? const Duration(milliseconds: 500),
          backoffMultiplier: backoffMultiplier ?? 1.5,
          maxDelay: maxDelay ?? const Duration(seconds: 5),
          shouldRetry: _isRetryableWebSocketError,
        );
      
      case RetryType.audio:
        return RetryConfig(
          maxRetries: maxRetries ?? 2,
          initialDelay: initialDelay ?? const Duration(milliseconds: 200),
          backoffMultiplier: backoffMultiplier ?? 1.5,
          maxDelay: maxDelay ?? const Duration(seconds: 2),
          shouldRetry: _isRetryableAudioError,
        );
      
      case RetryType.custom:
        return RetryConfig(
          maxRetries: maxRetries ?? 3,
          initialDelay: initialDelay ?? const Duration(seconds: 1),
          backoffMultiplier: backoffMultiplier ?? 2.0,
          maxDelay: maxDelay ?? const Duration(seconds: 30),
          shouldRetry: (error) => true, // Retry all errors by default
        );
    }
  }
  
  /// Execute with custom retry configuration
  Future<T> executeWithConfig<T>({
    required Future<T> Function() operation,
    required RetryConfig config,
    void Function(int attempt, dynamic error)? onRetry,
  }) async {
    return executeWithRetry<T>(
      operation: operation,
      maxRetries: config.maxRetries,
      initialDelay: config.initialDelay,
      backoffMultiplier: config.backoffMultiplier,
      maxDelay: config.maxDelay,
      shouldRetry: config.shouldRetry,
      onRetry: onRetry,
    );
  }
}

/// Retry configuration class
class RetryConfig {
  final int maxRetries;
  final Duration initialDelay;
  final double backoffMultiplier;
  final Duration maxDelay;
  final bool Function(dynamic error) shouldRetry;
  
  const RetryConfig({
    required this.maxRetries,
    required this.initialDelay,
    required this.backoffMultiplier,
    required this.maxDelay,
    required this.shouldRetry,
  });
}

/// Types of retry operations
enum RetryType {
  network,
  websocket,
  audio,
  custom,
}

/// Retry result with attempt information
class RetryResult<T> {
  final T result;
  final int attempts;
  final Duration totalTime;
  final List<dynamic> errors;

  const RetryResult({
    required this.result,
    required this.attempts,
    required this.totalTime,
    required this.errors,
  });

  bool get wasRetried => attempts > 1;
  bool get hadErrors => errors.isNotEmpty;
}
