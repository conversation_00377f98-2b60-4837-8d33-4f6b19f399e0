import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/colors.dart';
import '../constants/text_styles.dart';

/// App theme configuration matching the Expo version
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: Colors.grey,
      fontFamily: AppTextStyles.fontFamily,
      scaffoldBackgroundColor: AppColors.slate950,
      useMaterial3: true,
      
      // AppBar theme
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        titleTextStyle: AppTextStyles.headerTitle,
        iconTheme: IconThemeData(color: AppColors.lightText),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      
      // Text theme
      textTheme: const TextTheme(
        displayLarge: AppTextStyles.headerTitle,
        displayMedium: AppTextStyles.modalTitle,
        displaySmall: AppTextStyles.modalSubtitle,
        headlineLarge: AppTextStyles.headerTitle,
        headlineMedium: AppTextStyles.modalTitle,
        headlineSmall: AppTextStyles.modalSubtitle,
        titleLarge: AppTextStyles.headerTitle,
        titleMedium: AppTextStyles.modalTitle,
        titleSmall: AppTextStyles.modalSubtitle,
        bodyLarge: AppTextStyles.messageText,
        bodyMedium: AppTextStyles.messageText,
        bodySmall: AppTextStyles.captionText,
        labelLarge: AppTextStyles.buttonText,
        labelMedium: AppTextStyles.smallButtonText,
        labelSmall: AppTextStyles.captionText,
      ),
      
      // Button themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.slate700,
          foregroundColor: AppColors.lightText,
          textStyle: AppTextStyles.buttonText,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.lightText,
          textStyle: AppTextStyles.buttonText,
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.lightText,
          textStyle: AppTextStyles.buttonText,
          side: const BorderSide(color: AppColors.slate600),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      
      // Dialog theme
      dialogTheme: DialogThemeData(
        backgroundColor: AppColors.slate800,
        titleTextStyle: AppTextStyles.modalTitle,
        contentTextStyle: AppTextStyles.messageText.copyWith(
          color: AppColors.lightText,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 8,
      ),
      
      // Bottom sheet theme
      bottomSheetTheme: const BottomSheetThemeData(
        backgroundColor: AppColors.slate800,
        modalBackgroundColor: AppColors.slate800,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
      ),
      
      // Card theme
      cardTheme: CardThemeData(
        color: AppColors.slate800,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      
      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.slate800,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.slate600),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.slate600),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.infoBlue),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.errorRed),
        ),
        labelStyle: AppTextStyles.messageText.copyWith(color: AppColors.lightText),
        hintStyle: AppTextStyles.messageText.copyWith(color: AppColors.disabledText),
      ),
      
      // Switch theme
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.infoBlue;
          }
          return AppColors.slate600;
        }),
        trackColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.infoBlue.withOpacity(0.5);
          }
          return AppColors.slate700;
        }),
      ),
      
      // Color scheme
      colorScheme: const ColorScheme.dark(
        brightness: Brightness.dark,
        primary: AppColors.slate700,
        onPrimary: AppColors.lightText,
        secondary: AppColors.slate600,
        onSecondary: AppColors.lightText,
        tertiary: AppColors.infoBlue,
        onTertiary: AppColors.white,
        error: AppColors.errorRed,
        onError: AppColors.white,
        background: AppColors.slate950,
        onBackground: AppColors.lightText,
        surface: AppColors.slate800,
        onSurface: AppColors.lightText,
        surfaceVariant: AppColors.slate700,
        onSurfaceVariant: AppColors.lightText,
        outline: AppColors.slate600,
        shadow: AppColors.shadowDark,
      ),
      
      // Icon theme
      iconTheme: const IconThemeData(
        color: AppColors.lightText,
        size: 24,
      ),
      
      // Divider theme
      dividerTheme: const DividerThemeData(
        color: AppColors.slate600,
        thickness: 1,
      ),
    );
  }
  
  // Private constructor to prevent instantiation
  const AppTheme._();
}