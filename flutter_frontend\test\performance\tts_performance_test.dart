import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:deutschkorrekt_flutter/data/services/audio_cache_manager.dart';
import 'package:deutschkorrekt_flutter/data/services/tts_audio_service.dart';
import 'package:deutschkorrekt_flutter/core/utils/sentence_extractor.dart';
import 'package:http/http.dart' as http;

import 'tts_performance_test.mocks.dart';

// Generate mocks for performance testing
@GenerateMocks([http.Client, AudioCacheManager])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('TTS Performance Tests', () {
    late MockClient mockHttpClient;
    late MockAudioCacheManager mockCacheManager;
    late TTSAudioService ttsService;
    
    setUp(() {
      mockHttpClient = MockClient();
      mockCacheManager = MockAudioCacheManager();
      ttsService = TTSAudioService(
        httpClient: mockHttpClient,
        cacheManager: mockCacheManager,
      );
    });
    
    tearDown(() {
      ttsService.dispose();
    });
    
    group('Cache Performance', () {
      test('should handle large cache operations efficiently', () async {
        final stopwatch = Stopwatch()..start();
        
        // Mock cache stats for large cache
        when(mockCacheManager.getCacheStats()).thenAnswer((_) async => {
          'fileCount': 1000,
          'totalSizeBytes': 500 * 1024 * 1024, // 500MB
          'totalSizeKB': 500 * 1024,
        });
        
        final stats = await ttsService.getServiceStats();
        
        stopwatch.stop();
        
        expect(stats['cacheStats'], isNotNull);
        expect(stopwatch.elapsedMilliseconds, lessThan(100)); // Should be fast
      });
      
      test('should cache multiple audio files concurrently', () async {
        final stopwatch = Stopwatch()..start();
        
        // Mock concurrent cache operations
        final futures = <Future>[];
        for (int i = 0; i < 10; i++) {
          final audioData = Uint8List.fromList(List.generate(1024, (j) => j % 256));
          when(mockCacheManager.cacheAudio('message_$i', audioData))
              .thenAnswer((_) async {});
          
          futures.add(Future.delayed(
            Duration(milliseconds: i * 10),
            () => mockCacheManager.cacheAudio('message_$i', audioData),
          ));
        }
        
        await Future.wait(futures);
        
        stopwatch.stop();
        
        expect(stopwatch.elapsedMilliseconds, lessThan(500)); // Should complete quickly
        
        // Verify all cache operations were called
        for (int i = 0; i < 10; i++) {
          verify(mockCacheManager.cacheAudio('message_$i', any)).called(1);
        }
      });
    });
  });
}