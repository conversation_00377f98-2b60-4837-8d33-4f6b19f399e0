#!/usr/bin/env python3
"""
Test runner for TTS backend tests.
Runs TTS-specific tests without dependency conflicts.
"""

import sys
import subprocess
import os

def run_tts_tests():
    """Run all TTS-related tests."""
    
    # Change to backend directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    print("🧪 Running TTS Backend Tests")
    print("=" * 50)
    
    # Test files to run
    test_files = [
        "tests/test_tts_service.py",
        "tests/test_google_tts_client.py",
        # Skip API tests due to dependency issues
        # "tests/test_tts_api.py",
        # Skip integration tests (require Google Cloud credentials)
        # "tests/test_tts_integration.py",
    ]
    
    total_passed = 0
    total_failed = 0
    
    for test_file in test_files:
        print(f"\n📋 Running {test_file}")
        print("-" * 40)
        
        try:
            result = subprocess.run([
                sys.executable, "-m", "pytest", 
                test_file, 
                "-v", 
                "--tb=short"
            ], capture_output=True, text=True)
            
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
            
            # Parse results
            if result.returncode == 0:
                print(f"✅ {test_file} - ALL TESTS PASSED")
            else:
                print(f"❌ {test_file} - SOME TESTS FAILED")
                
        except Exception as e:
            print(f"❌ Error running {test_file}: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 TTS Test Suite Complete")
    
    # Run a summary
    try:
        print("\n📊 Test Summary:")
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/test_tts_service.py",
            "tests/test_google_tts_client.py",
            "--tb=no",
            "-q"
        ], capture_output=True, text=True)
        
        print(result.stdout)
        
    except Exception as e:
        print(f"Error generating summary: {e}")

if __name__ == "__main__":
    run_tts_tests()