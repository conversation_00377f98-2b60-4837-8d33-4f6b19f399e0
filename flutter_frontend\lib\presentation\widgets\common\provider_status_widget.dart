import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/settings_provider.dart';
import '../../providers/profile_provider.dart';
import '../../providers/audio_provider.dart';
import '../../providers/chat_provider.dart';
import '../../../core/utils/provider_initializer.dart';
import '../../../core/utils/provider_error_handler.dart';
import '../../../core/constants/colors.dart';

/// Debug widget to show provider status and health
class ProviderStatusWidget extends StatelessWidget {
  const ProviderStatusWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer4<SettingsProvider, ProfileProvider, AudioProvider, ChatProvider>(
      builder: (context, settings, profile, audio, chat, child) {
        final stats = ProviderInitializer.getStats(
          settingsProvider: settings,
          profileProvider: profile,
          audioProvider: audio,
          chatProvider: chat,
        );
        
        final systemHealth = ProviderErrorHandler.getSystemHealth();
        
        return Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Icon(
                      _getHealthIcon(systemHealth),
                      color: _getHealthColor(systemHealth),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'System Health: ${systemHealth.toString().split('.').last}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildProviderStatus('Settings', settings.isInitialized, 'settings'),
                _buildProviderStatus('Profile', profile.isInitialized, 'profile'),
                _buildProviderStatus('Audio', audio.isInitialized, 'audio'),
                _buildProviderStatus('Chat', chat.state.isInitialized, 'chat'),
                const SizedBox(height: 16),
                Text(
                  'Statistics:',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text('Messages: ${chat.messages.length}'),
                Text('Recording: ${audio.isRecording ? "Active" : "Inactive"}'),
                Text('Connected: ${chat.isConnected ? "Yes" : "No"}'),
                Text('Audio Permission: ${audio.hasPermission ? "Granted" : "Denied"}'),
                const SizedBox(height: 16),
                if (ProviderErrorHandler.getErrorStats()['totalErrors'] > 0)
                  _buildErrorSummary(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildProviderStatus(String name, bool isInitialized, String providerId) {
    final health = ProviderErrorHandler.getProviderHealth(providerId);
    final retryCount = ProviderErrorHandler.getRetryCount(providerId);
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isInitialized ? Icons.check_circle : Icons.error,
            color: isInitialized ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text('$name Provider'),
          ),
          if (retryCount > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.orange,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                'Retries: $retryCount',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                ),
              ),
            ),
          const SizedBox(width: 8),
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: _getHealthColor(health),
              shape: BoxShape.circle,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorSummary() {
    final errorStats = ProviderErrorHandler.getErrorStats();
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Error Summary:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text('Total Errors: ${errorStats['totalErrors']}'),
          Text('Affected Providers: ${errorStats['totalProviders']}'),
        ],
      ),
    );
  }

  IconData _getHealthIcon(dynamic health) {
    final healthStr = health.toString().split('.').last;
    switch (healthStr) {
      case 'healthy':
        return Icons.health_and_safety;
      case 'warning':
        return Icons.warning;
      case 'degraded':
        return Icons.error_outline;
      case 'failed':
        return Icons.error;
      case 'critical':
        return Icons.dangerous;
      default:
        return Icons.help;
    }
  }

  Color _getHealthColor(dynamic health) {
    final healthStr = health.toString().split('.').last;
    switch (healthStr) {
      case 'healthy':
        return Colors.green;
      case 'warning':
        return Colors.orange;
      case 'degraded':
        return Colors.deepOrange;
      case 'failed':
        return Colors.red;
      case 'critical':
        return Colors.red.shade900;
      default:
        return Colors.grey;
    }
  }
}

/// Floating action button for showing provider status
class ProviderStatusFAB extends StatelessWidget {
  const ProviderStatusFAB({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      mini: true,
      backgroundColor: AppColors.slate700,
      onPressed: () {
        showDialog(
          context: context,
          builder: (context) => Dialog(
            child: SingleChildScrollView(
              child: ProviderStatusWidget(),
            ),
          ),
        );
      },
      child: const Icon(
        Icons.info_outline,
        color: Colors.white,
      ),
    );
  }
}