import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/gradients.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/app_error.dart';

/// Enhanced error dialog with user-friendly messages and retry mechanisms
class EnhancedErrorDialog extends StatefulWidget {
  final AppError error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showRetryButton;
  final bool showDetailsButton;
  final String? customTitle;
  final String? customMessage;
  final IconData? customIcon;
  
  const EnhancedErrorDialog({
    super.key,
    required this.error,
    this.onRetry,
    this.onDismiss,
    this.showRetryButton = true,
    this.showDetailsButton = true,
    this.customTitle,
    this.customMessage,
    this.customIcon,
  });

  @override
  State<EnhancedErrorDialog> createState() => _EnhancedErrorDialogState();
}

class _EnhancedErrorDialogState extends State<EnhancedErrorDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  bool _showDetails = false;
  bool _isRetrying = false;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: AlertDialog(
              backgroundColor: AppColors.slate800,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: BorderSide(
                  color: AppColors.slate600.withOpacity(0.3),
                  width: 1,
                ),
              ),
              title: _buildTitle(),
              content: _buildContent(),
              actions: _buildActions(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTitle() {
    final icon = widget.customIcon ?? _getErrorIcon();
    final title = widget.customTitle ?? _getErrorTitle();
    
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getErrorColor().withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: _getErrorColor(),
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: AppTextStyles.heading3.copyWith(
              color: AppColors.lightText,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        
        // Main error message
        Text(
          widget.customMessage ?? _getUserFriendlyMessage(),
          style: AppTextStyles.bodyText1.copyWith(
            color: AppColors.lightText.withOpacity(0.9),
            height: 1.4,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Suggested actions
        if (_getSuggestedActions().isNotEmpty) ...[
          Text(
            'Try these steps:',
            style: AppTextStyles.bodyText2.copyWith(
              color: AppColors.lightText.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          ..._getSuggestedActions().map((action) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '• ',
                  style: AppTextStyles.bodyText2.copyWith(
                    color: AppColors.primaryBlue,
                  ),
                ),
                Expanded(
                  child: Text(
                    action,
                    style: AppTextStyles.bodyText2.copyWith(
                      color: AppColors.lightText.withOpacity(0.8),
                    ),
                  ),
                ),
              ],
            ),
          )),
          const SizedBox(height: 16),
        ],
        
        // Details section
        if (widget.showDetailsButton) ...[
          GestureDetector(
            onTap: () {
              setState(() {
                _showDetails = !_showDetails;
              });
              HapticFeedback.selectionClick();
            },
            child: Row(
              children: [
                Icon(
                  _showDetails ? Icons.expand_less : Icons.expand_more,
                  color: AppColors.primaryBlue,
                  size: 20,
                ),
                const SizedBox(width: 4),
                Text(
                  _showDetails ? 'Hide Details' : 'Show Details',
                  style: AppTextStyles.bodyText2.copyWith(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          
          if (_showDetails) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.slate900.withOpacity(0.5),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.slate600.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Error Type: ${widget.error.type.toString().split('.').last}',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.lightText.withOpacity(0.7),
                      fontFamily: 'monospace',
                    ),
                  ),
                  if (widget.error.details != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      'Details: ${widget.error.details}',
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.lightText.withOpacity(0.7),
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                  if (widget.error.timestamp != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      'Time: ${widget.error.timestamp!.toLocal().toString()}',
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.lightText.withOpacity(0.7),
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ],
    );
  }

  Widget _buildActions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Dismiss button
        TextButton(
          onPressed: () {
            widget.onDismiss?.call();
            Navigator.of(context).pop();
          },
          child: Text(
            'Dismiss',
            style: AppTextStyles.button.copyWith(
              color: AppColors.lightText.withOpacity(0.7),
            ),
          ),
        ),
        
        const SizedBox(width: 8),
        
        // Retry button
        if (widget.showRetryButton && widget.onRetry != null)
          ElevatedButton(
            onPressed: _isRetrying ? null : _handleRetry,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: AppColors.lightText,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isRetrying
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'Retry',
                    style: AppTextStyles.button.copyWith(
                      color: AppColors.lightText,
                    ),
                  ),
          ),
      ],
    );
  }

  Future<void> _handleRetry() async {
    setState(() {
      _isRetrying = true;
    });
    
    try {
      await widget.onRetry?.call();
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      // Handle retry failure
      if (mounted) {
        setState(() {
          _isRetrying = false;
        });
      }
    }
  }

  IconData _getErrorIcon() {
    switch (widget.error.type) {
      case AppErrorType.microphonePermissionDenied:
        return Icons.mic_off;
      case AppErrorType.audioRecordingFailed:
        return Icons.record_voice_over;
      case AppErrorType.websocketConnectionFailed:
        return Icons.wifi_off;
      case AppErrorType.websocketMessageParsingFailed:
        return Icons.error_outline;
      case AppErrorType.networkError:
        return Icons.signal_wifi_off;
      case AppErrorType.serverError:
        return Icons.cloud_off;
      case AppErrorType.unknown:
      default:
        return Icons.warning;
    }
  }

  Color _getErrorColor() {
    switch (widget.error.type) {
      case AppErrorType.microphonePermissionDenied:
        return AppColors.warningOrange;
      case AppErrorType.audioRecordingFailed:
        return AppColors.errorRed;
      case AppErrorType.websocketConnectionFailed:
      case AppErrorType.networkError:
        return AppColors.primaryBlue;
      case AppErrorType.websocketMessageParsingFailed:
      case AppErrorType.serverError:
        return AppColors.errorRed;
      case AppErrorType.unknown:
      default:
        return AppColors.warningOrange;
    }
  }

  String _getErrorTitle() {
    switch (widget.error.type) {
      case AppErrorType.microphonePermissionDenied:
        return 'Microphone Access Required';
      case AppErrorType.audioRecordingFailed:
        return 'Recording Problem';
      case AppErrorType.websocketConnectionFailed:
        return 'Connection Problem';
      case AppErrorType.websocketMessageParsingFailed:
        return 'Communication Error';
      case AppErrorType.networkError:
        return 'Network Problem';
      case AppErrorType.serverError:
        return 'Server Problem';
      case AppErrorType.unknown:
      default:
        return 'Something Went Wrong';
    }
  }

  String _getUserFriendlyMessage() {
    switch (widget.error.type) {
      case AppErrorType.microphonePermissionDenied:
        return 'DeutschKorrekt needs microphone access to help you practice German pronunciation. Please grant permission in your device settings.';
      case AppErrorType.audioRecordingFailed:
        return 'We couldn\'t record your audio. This might be due to another app using the microphone or a temporary system issue.';
      case AppErrorType.websocketConnectionFailed:
        return 'We\'re having trouble connecting to our servers. Please check your internet connection and try again.';
      case AppErrorType.websocketMessageParsingFailed:
        return 'There was a problem processing the server response. This is usually temporary.';
      case AppErrorType.networkError:
        return 'Please check your internet connection and try again. Make sure you\'re connected to Wi-Fi or mobile data.';
      case AppErrorType.serverError:
        return 'Our servers are experiencing issues. We\'re working to fix this as quickly as possible.';
      case AppErrorType.unknown:
      default:
        return 'An unexpected error occurred. Please try again, and if the problem persists, restart the app.';
    }
  }

  List<String> _getSuggestedActions() {
    switch (widget.error.type) {
      case AppErrorType.microphonePermissionDenied:
        return [
          'Go to Settings > Privacy > Microphone',
          'Find DeutschKorrekt and enable microphone access',
          'Restart the app after granting permission',
        ];
      case AppErrorType.audioRecordingFailed:
        return [
          'Close other apps that might be using the microphone',
          'Check if your microphone is working in other apps',
          'Restart the app and try again',
        ];
      case AppErrorType.websocketConnectionFailed:
      case AppErrorType.networkError:
        return [
          'Check your internet connection',
          'Try switching between Wi-Fi and mobile data',
          'Move closer to your Wi-Fi router if using Wi-Fi',
        ];
      case AppErrorType.websocketMessageParsingFailed:
      case AppErrorType.serverError:
        return [
          'Wait a moment and try again',
          'Check if the app needs an update',
          'Restart the app if the problem continues',
        ];
      case AppErrorType.unknown:
      default:
        return [
          'Try the action again',
          'Restart the app',
          'Check your internet connection',
        ];
    }
  }

  /// Show enhanced error dialog
  static Future<void> show({
    required BuildContext context,
    required AppError error,
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
    bool showRetryButton = true,
    bool showDetailsButton = true,
    String? customTitle,
    String? customMessage,
    IconData? customIcon,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => EnhancedErrorDialog(
        error: error,
        onRetry: onRetry,
        onDismiss: onDismiss,
        showRetryButton: showRetryButton,
        showDetailsButton: showDetailsButton,
        customTitle: customTitle,
        customMessage: customMessage,
        customIcon: customIcon,
      ),
    );
  }
}
