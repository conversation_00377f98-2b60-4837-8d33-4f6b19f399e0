import { useState, useRef, useCallback, useEffect } from 'react';

interface CorrectionResult {
  original_text: string;
  corrected_text: string;
  suggestions: string[];
  explanations: string[];
  processing_time: number;
}

interface TranslationResult {
  original_text: string;
  translations: string[];
  processing_time: number;
}

interface GroqResponse {
  original_text: string;
  response_text: string;
  processing_time: number;
}

interface UseAudioStreamingProps {
  backendUrl?: string;
  onPartialTranscript?: (text: string) => void;
  onFinalTranscript?: (text: string) => void;
  onCorrection?: (result: CorrectionResult) => void;
  onTranslation?: (result: TranslationResult) => void;
  onGroqResponse?: (result: GroqResponse) => void;
  onError?: (error: string) => void;
  onTimeout?: () => void;
  onInfo?: (message: string) => void;
}

enum StreamingState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  STREAMING = 'streaming',
  PROCESSING = 'processing',
  ERROR = 'error'
}

const CONNECTION_TIMEOUT = 10000;
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;

// Deepgram Audio Format Constants (Official Compliance)
const DEEPGRAM_SAMPLE_RATE = 16000;      // Required sample rate
const DEEPGRAM_CHANNELS = 1;             // Single-channel required

// ScriptProcessor requires buffer sizes to be powers of 2 (256, 512, 1024, etc.)
// For Deepgram, we can send audio in real-time without specific chunk size requirements
const SCRIPT_PROCESSOR_BUFFER_SIZE = 1024; // Must be power of 2
const DEEPGRAM_CHUNK_SAMPLES = SCRIPT_PROCESSOR_BUFFER_SIZE; // Use processor buffer size
const DEEPGRAM_CHUNK_BYTES = DEEPGRAM_CHUNK_SAMPLES * 2; // PCM16 bytes

/**
 * Professional Audio Streaming Hook
 * Handles WebSocket connection and real-time audio streaming
 * Compliant with Deepgram audio format requirements:
 * - PCM16 encoding
 * - 16kHz sample rate
 * - Single-channel
 * - Real-time streaming chunks
 */
export function useAudioStreaming({
  backendUrl = 'wss://deutschkorrekt-backend-645996191396.europe-west3.run.app',
  onPartialTranscript,
  onFinalTranscript,
  onCorrection,
  onTranslation,
  onGroqResponse,
  onError,
  onTimeout,
  onInfo,
}: UseAudioStreamingProps) {
  const [state, setState] = useState<StreamingState>(StreamingState.DISCONNECTED);
  const [partialText, setPartialText] = useState('');
  const [finalText, setFinalText] = useState('');
  const [retryCount, setRetryCount] = useState(0);
  
  const wsRef = useRef<WebSocket | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const connectionPromiseRef = useRef<Promise<void> | null>(null);

  /**
   * Handle incoming WebSocket messages
   */
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message = JSON.parse(event.data);
      console.log('📨 Received message:', {
        type: message.message_type,
        data: message.data,
        fullMessage: message
      });
      
      switch (message.message_type) {
        case 'partial_transcript':
          // Handle partial transcripts (ongoing turn)
          console.log('🎯 Partial transcript received:', message.data.text);
          setPartialText(message.data.text);
          onPartialTranscript?.(message.data.text);
          break;

        case 'final_transcript':
          // Handle final transcripts (end of turn)
          console.log('🎯 Final transcript received:', message.data.text);
          console.log('🎯 Auto-ended by Deepgram:', message.data.auto_ended);
          
          setFinalText(message.data.text);
          setPartialText('');
          setState(StreamingState.PROCESSING);
          onFinalTranscript?.(message.data.text);
          
          // If this was auto-ended by Deepgram, stop streaming and reset state
          if (message.data.auto_ended === true) {
            console.log('🛑 Auto-stopping due to Deepgram endpointing');
            stopStreaming();
            // Disconnect to prevent duplicate connections
            setTimeout(() => {
              disconnect();
            }, 1000);
          }
          break;

        case 'transcript':
          // Handle complete transcript (session finalization)
          console.log('🎯 Complete transcript received:', message.data.text);
          console.log('🎯 Is final:', message.data.is_final);

          setPartialText(''); // Clear partial text
          onFinalTranscript?.(message.data.text);
          break;
          
        case 'correction':
          setState(StreamingState.CONNECTED);
          onCorrection?.(message.data);
          break;
          
        case 'translation':
          setState(StreamingState.CONNECTED);
          onTranslation?.(message.data);
          break;
          
        case 'processing':
          console.log('⚡ Processing with Groq...');
          setState(StreamingState.PROCESSING);
          break;
          
        case 'groq_response':
          console.log('🤖 Groq response received:', message.data);
          setState(StreamingState.CONNECTED);
          onGroqResponse?.(message.data);
          break;
          
        case 'timeout':
          stopStreaming();
          onTimeout?.();
          break;
          
        case 'error':
          setState(StreamingState.ERROR);
          onError?.(message.data.message);
          break;
          
        case 'info':
          onInfo?.(message.data.message);
          break;
          
        case 'session_started':
          console.log('🎯 Session started:', message.data.session_id);
          break;
          
        default:
          console.log('Unknown message:', message.message_type);
      }
    } catch (error) {
      console.error('Failed to parse message:', error);
    }
  }, [onPartialTranscript, onFinalTranscript, onCorrection, onTranslation, onError, onTimeout, onInfo]);

  /**
   * Connect to WebSocket with retry logic
   */
  const connect = useCallback(async (): Promise<void> => {
    // Return existing connection promise if connecting
    if (connectionPromiseRef.current) {
      return connectionPromiseRef.current;
    }

    // If already connected, return immediately
    if (state === StreamingState.CONNECTED) {
      return Promise.resolve();
    }

    const connectAttempt = async (attempt: number): Promise<void> => {
      setState(StreamingState.CONNECTING);
      
      return new Promise((resolve, reject) => {
        const ws = new WebSocket(`${backendUrl}/stt`);
        
        const timeout = setTimeout(() => {
          ws.close();
          reject(new Error('Connection timeout'));
        }, CONNECTION_TIMEOUT);
        
        ws.onopen = () => {
          clearTimeout(timeout);
          console.log('✅ WebSocket connected');
          setState(StreamingState.CONNECTED);
          setRetryCount(0);
          resolve();
        };
        
        ws.onmessage = handleMessage;
        
        ws.onclose = (event) => {
          clearTimeout(timeout);
          console.log('WebSocket disconnected:', event.code, event.reason);
          setState(StreamingState.DISCONNECTED);
          cleanup();
        };
        
        ws.onerror = (error) => {
          clearTimeout(timeout);
          console.error('WebSocket error:', error);
          setState(StreamingState.ERROR);
          reject(new Error('WebSocket connection failed'));
        };
        
        wsRef.current = ws;
      });
    };

    // Create connection promise with retry logic
    const connectionPromise = async (): Promise<void> => {
      for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {
        try {
          await connectAttempt(attempt);
          return;
        } catch (error) {
          console.error(`Connection attempt ${attempt + 1} failed:`, error);
          setRetryCount(attempt + 1);
          
          if (attempt < MAX_RETRIES - 1) {
            await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * (attempt + 1)));
          } else {
            throw new Error(`Failed to connect after ${MAX_RETRIES} attempts`);
          }
        }
      }
    };

    connectionPromiseRef.current = connectionPromise();
    
    try {
      await connectionPromiseRef.current;
    } finally {
      connectionPromiseRef.current = null;
    }
  }, [backendUrl, handleMessage, state]);

  /**
   * Start audio streaming with connection verification
   */
  const startStreaming = useCallback(async (): Promise<void> => {
    try {
      // Ensure connection is established first
      if (state !== StreamingState.CONNECTED) {
        console.log('Not connected, establishing connection...');
        await connect();
      }

      // Double-check connection state
      if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
        throw new Error('WebSocket not ready for streaming');
      }

      // Get microphone access with Deepgram-compliant settings
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: DEEPGRAM_SAMPLE_RATE,    // Required by Deepgram
          channelCount: DEEPGRAM_CHANNELS,     // Single-channel required
          echoCancellation: true,
          noiseSuppression: true,
        }
      });

      streamRef.current = stream;

      // Use AudioContext for PCM16 format (Deepgram compliant)
      const audioContext = new AudioContext({
        sampleRate: DEEPGRAM_SAMPLE_RATE
      });

      const source = audioContext.createMediaStreamSource(stream);

      // Buffer to accumulate audio samples to reach target chunk size
      let audioBuffer: Float32Array = new Float32Array(0);

      // Create AudioWorkletNode (modern replacement for ScriptProcessorNode)
      let processor: AudioWorkletNode;

      try {
        // Try to use AudioWorkletNode (modern approach)
        await audioContext.audioWorklet.addModule(
          URL.createObjectURL(new Blob([`
            class AudioProcessor extends AudioWorkletProcessor {
              process(inputs, outputs, parameters) {
                const input = inputs[0];
                if (input && input[0]) {
                  this.port.postMessage({
                    type: 'audioData',
                    data: input[0]
                  });
                }
                return true;
              }
            }
            registerProcessor('audio-processor', AudioProcessor);
          `], { type: 'application/javascript' }))
        );

        processor = new AudioWorkletNode(audioContext, 'audio-processor');

        processor.port.onmessage = (event) => {
          if (event.data.type === 'audioData' && wsRef.current?.readyState === WebSocket.OPEN) {
            const inputData = event.data.data;

            // Accumulate samples in buffer
            const newBuffer = new Float32Array(audioBuffer.length + inputData.length);
            newBuffer.set(audioBuffer);
            newBuffer.set(inputData, audioBuffer.length);
            audioBuffer = newBuffer;

            // Send audio data when we have enough samples (real-time streaming for Deepgram)
            while (audioBuffer.length >= DEEPGRAM_CHUNK_SAMPLES) {
              // Extract samples for Deepgram
              const chunkSamples = audioBuffer.slice(0, DEEPGRAM_CHUNK_SAMPLES);

              // Convert float32 to PCM16 (Deepgram requirement)
              const pcm16Buffer = new Int16Array(DEEPGRAM_CHUNK_SAMPLES);
              let maxAmplitude = 0;
              let nonZeroSamples = 0;

              for (let i = 0; i < DEEPGRAM_CHUNK_SAMPLES; i++) {
                // Apply gain boost for quiet audio (amplify by 3x)
                const amplifiedSample = chunkSamples[i] * 3.0;

                // Convert from [-1, 1] to [-32768, 32767] with clipping
                const sample = Math.max(-32768, Math.min(32767, amplifiedSample * 32767));
                pcm16Buffer[i] = sample;

                // Track audio quality
                const amplitude = Math.abs(sample);
                if (amplitude > maxAmplitude) maxAmplitude = amplitude;
                if (amplitude > 0) nonZeroSamples++;
              }

              // Send as raw bytes (backend will forward to Deepgram)
              const audioBytes = new Uint8Array(pcm16Buffer.buffer);
              // Removed all audio quality logs to clean up console

              // Verify we're sending expected bytes
              if (audioBytes.length !== DEEPGRAM_CHUNK_BYTES) {
                console.error(`❌ Wrong chunk size! Expected ${DEEPGRAM_CHUNK_BYTES} bytes, got ${audioBytes.length} bytes`);
              }

              // Removed audio quality warnings to clean up console

              wsRef.current.send(audioBytes);

              // Remove processed samples from buffer
              audioBuffer = audioBuffer.slice(DEEPGRAM_CHUNK_SAMPLES);
            }
          }
        };

        source.connect(processor);
        processor.connect(audioContext.destination);

      } catch (workletError) {
        console.warn('AudioWorkletNode not supported, falling back to ScriptProcessorNode:', workletError);

        // Fallback to ScriptProcessorNode for older browsers
        const scriptProcessor = audioContext.createScriptProcessor(SCRIPT_PROCESSOR_BUFFER_SIZE, 1, 1);

        scriptProcessor.onaudioprocess = (event) => {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            const inputBuffer = event.inputBuffer;
            const inputData = inputBuffer.getChannelData(0);

            // Accumulate samples in buffer
            const newBuffer = new Float32Array(audioBuffer.length + inputData.length);
            newBuffer.set(audioBuffer);
            newBuffer.set(inputData, audioBuffer.length);
            audioBuffer = newBuffer;

            // Send audio data when we have enough samples (real-time streaming for Deepgram)
            while (audioBuffer.length >= DEEPGRAM_CHUNK_SAMPLES) {
              // Extract samples for Deepgram
              const chunkSamples = audioBuffer.slice(0, DEEPGRAM_CHUNK_SAMPLES);

              // Convert float32 to PCM16 (Deepgram requirement)
              const pcm16Buffer = new Int16Array(DEEPGRAM_CHUNK_SAMPLES);
              for (let i = 0; i < DEEPGRAM_CHUNK_SAMPLES; i++) {
                // Convert from [-1, 1] to [-32768, 32767]
                pcm16Buffer[i] = Math.max(-32768, Math.min(32767, chunkSamples[i] * 32767));
              }

              // Send as raw bytes (backend will forward to Deepgram)
              const audioBytes = new Uint8Array(pcm16Buffer.buffer);
              console.log(`🎵 Sending PCM16 audio chunk: ${audioBytes.length} bytes (${DEEPGRAM_CHUNK_SAMPLES} samples)`);

              // Verify we're sending expected bytes
              if (audioBytes.length !== DEEPGRAM_CHUNK_BYTES) {
                console.error(`❌ Wrong chunk size! Expected ${DEEPGRAM_CHUNK_BYTES} bytes, got ${audioBytes.length} bytes`);
              }

              wsRef.current.send(audioBytes);

              // Remove processed samples from buffer
              audioBuffer = audioBuffer.slice(DEEPGRAM_CHUNK_SAMPLES);
            }
          }
        };

        source.connect(scriptProcessor);
        scriptProcessor.connect(audioContext.destination);
        processor = scriptProcessor as any;
      }

      // Store references for cleanup
      mediaRecorderRef.current = { audioContext, source, processor, audioBuffer } as any;
      
      setState(StreamingState.STREAMING);
      console.log('🎤 Audio streaming started');
      
    } catch (error) {
      console.error('Failed to start streaming:', error);
      setState(StreamingState.ERROR);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      onError?.(errorMessage);
      throw error;
    }
  }, [state, connect, onError]);

  /**
   * Stop audio streaming
   */
  const stopStreaming = useCallback(() => {
    // Clean up AudioContext and processors
    if (mediaRecorderRef.current) {
      const { audioContext, source, processor } = mediaRecorderRef.current as any;

      if (processor) {
        // Handle both AudioWorkletNode and ScriptProcessorNode
        if (processor.port) {
          processor.port.close();
        }
        processor.disconnect();
      }
      if (source) {
        source.disconnect();
      }
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    mediaRecorderRef.current = null;
    setState(StreamingState.CONNECTED);
    console.log('🛑 Audio streaming stopped');
  }, []);

  /**
   * Disconnect and cleanup
   */
  const disconnect = useCallback(() => {
    stopStreaming();
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    // Cancel any pending connection attempts
    connectionPromiseRef.current = null;
    
    setState(StreamingState.DISCONNECTED);
    setPartialText('');
    setFinalText('');
    setRetryCount(0);
  }, [stopStreaming]);

  /**
   * Cleanup resources
   */
  const cleanup = useCallback(() => {
    if (mediaRecorderRef.current) {
      const { audioContext, source, processor } = mediaRecorderRef.current as any;

      if (processor) {
        // Handle both AudioWorkletNode and ScriptProcessorNode
        if (processor.port) {
          processor.port.close();
        }
        processor.disconnect();
      }
      if (source) {
        source.disconnect();
      }
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }
      mediaRecorderRef.current = null;
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [cleanup]);

  return {
    // State
    isConnected: state === StreamingState.CONNECTED || state === StreamingState.STREAMING || state === StreamingState.PROCESSING,
    isStreaming: state === StreamingState.STREAMING,
    isProcessing: state === StreamingState.PROCESSING,
    isConnecting: state === StreamingState.CONNECTING,
    partialText,
    finalText,
    retryCount,
    
    // Actions
    connect,
    disconnect,
    startStreaming,
    // stopStreaming removed - only auto-stop via Deepgram
  };
}