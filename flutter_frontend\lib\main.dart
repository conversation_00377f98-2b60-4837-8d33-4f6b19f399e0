import 'package:flutter/material.dart';
import 'core/constants/colors.dart';
import 'presentation/screens/chat_screen.dart';

/// DeutschKorrekt - German Speech Correction App
/// Professional Flutter implementation matching the Expo version
void main() {
  runApp(const DeutschKorrektApp());
}

class DeutschKorrektApp extends StatelessWidget {
  const DeutschKorrektApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'DeutschKorrekt',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: AppColors.primaryBlue,
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
        fontFamily: 'Inter',
      ),
      home: const ChatScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _counter = 0;

  void _incrementCounter() {
    setState(() {
      _counter++;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.slate900,
      appBar: AppBar(
        backgroundColor: AppColors.slate800,
        foregroundColor: AppColors.lightText,
        title: const Text('DeutschKorrekt'),
        elevation: 0,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Icon(
              Icons.mic,
              size: 100,
              color: AppColors.primaryBlue,
            ),
            const SizedBox(height: 20),
            const Text(
              'DeutschKorrekt',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: AppColors.lightText,
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              'German Speech Correction App',
              style: TextStyle(
                fontSize: 18,
                color: AppColors.lightText,
              ),
            ),
            const SizedBox(height: 30),
            const Text(
              '🎉 Successfully Running on Android! 🎉',
              style: TextStyle(
                fontSize: 20,
                color: AppColors.successGreen,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'Production-Ready Flutter App',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.lightText,
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 40),
            Text(
              'Tap count: $_counter',
              style: const TextStyle(
                fontSize: 18,
                color: AppColors.lightText,
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        tooltip: 'Test Interaction',
        child: const Icon(Icons.add),
      ),
    );
  }
}
