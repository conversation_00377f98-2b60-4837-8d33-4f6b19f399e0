import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'app.dart';
import 'presentation/providers/settings_provider.dart';
import 'presentation/providers/profile_provider.dart';
import 'presentation/providers/audio_provider.dart';
import 'presentation/providers/chat_provider.dart';
import 'presentation/providers/auth_provider.dart';
import 'presentation/providers/enhanced_profile_provider.dart';

/// DeutschKorrekt - German Speech Correction App
/// Professional Flutter implementation with Supabase authentication
void main() {
  runApp(const DeutschKorrektApp());
}

class DeutschKorrektApp extends StatelessWidget {
  const DeutschKorrektApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Authentication provider (highest priority)
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        
        // Enhanced profile provider for database integration
        ChangeNotifierProvider(create: (_) => EnhancedProfileProvider()),
        
        // Existing providers
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
        ChangeNotifierProvider(create: (_) => ProfileProvider()),
        ChangeNotifierProvider(create: (_) => AudioProvider()),
        ChangeNotifierProvider(create: (_) => ChatProvider()),
      ],
      child: const DeutschKorrektAppContent(),
    );
  }
}
class DeutschKorrektAppContent extends StatelessWidget {
  const DeutschKorrektAppContent({super.key});

  @override
  Widget build(BuildContext context) {
    return const AppInitializer();
  }
}