import 'package:flutter/material.dart';
import '../../../core/constants/text_styles.dart';

/// Widget that renders text with **bold** markers, matching Expo FormattedText component
class FormattedText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextStyle? boldStyle;
  final TextAlign textAlign;
  final int? maxLines;
  final TextOverflow overflow;
  final bool softWrap;
  
  const FormattedText(
    this.text, {
    super.key,
    this.style,
    this.boldStyle,
    this.textAlign = TextAlign.start,
    this.maxLines,
    this.overflow = TextOverflow.clip,
    this.softWrap = true,
  });

  @override
  Widget build(BuildContext context) {
    final defaultStyle = style ?? AppTextStyles.messageText;
    final defaultBoldStyle = boldStyle ?? defaultStyle.copyWith(
      fontWeight: FontWeight.bold,
    );
    
    final spans = _parseTextWithBoldMarkers(text, defaultStyle, defaultBoldStyle);
    
    return RichText(
      text: TextSpan(children: spans),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
      softWrap: softWrap,
    );
  }
  
  /// Parse text and create TextSpan list with bold formatting
  List<TextSpan> _parseTextWithBoldMarkers(
    String text,
    TextStyle normalStyle,
    TextStyle boldStyle,
  ) {
    final List<TextSpan> spans = [];
    final RegExp boldRegex = RegExp(r'\*\*(.*?)\*\*');
    
    int lastMatchEnd = 0;
    
    for (final Match match in boldRegex.allMatches(text)) {
      // Add normal text before the bold text
      if (match.start > lastMatchEnd) {
        final normalText = text.substring(lastMatchEnd, match.start);
        if (normalText.isNotEmpty) {
          spans.add(TextSpan(
            text: normalText,
            style: normalStyle,
          ));
        }
      }
      
      // Add bold text (without the ** markers)
      final boldText = match.group(1) ?? '';
      if (boldText.isNotEmpty) {
        spans.add(TextSpan(
          text: boldText,
          style: boldStyle,
        ));
      }
      
      lastMatchEnd = match.end;
    }
    
    // Add remaining normal text after the last bold text
    if (lastMatchEnd < text.length) {
      final remainingText = text.substring(lastMatchEnd);
      if (remainingText.isNotEmpty) {
        spans.add(TextSpan(
          text: remainingText,
          style: normalStyle,
        ));
      }
    }
    
    // If no bold markers were found, return the entire text as normal
    if (spans.isEmpty) {
      spans.add(TextSpan(
        text: text,
        style: normalStyle,
      ));
    }
    
    return spans;
  }
}

/// Extension methods for FormattedText to provide convenient constructors
extension FormattedTextExtensions on FormattedText {
  /// Create FormattedText with user message styling
  static FormattedText userMessage(
    String text, {
    Key? key,
    TextAlign textAlign = TextAlign.start,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
  }) {
    return FormattedText(
      text,
      key: key,
      style: AppTextStyles.userMessageText,
      boldStyle: AppTextStyles.userMessageText.copyWith(
        fontWeight: FontWeight.bold,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
  
  /// Create FormattedText with AI message styling
  static FormattedText aiMessage(
    String text, {
    Key? key,
    TextAlign textAlign = TextAlign.start,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
  }) {
    return FormattedText(
      text,
      key: key,
      style: AppTextStyles.aiMessageText,
      boldStyle: AppTextStyles.aiMessageText.copyWith(
        fontWeight: FontWeight.bold,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
  
  /// Create FormattedText with welcome message styling
  static FormattedText welcomeMessage(
    String text, {
    Key? key,
    TextAlign textAlign = TextAlign.start,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
  }) {
    return FormattedText(
      text,
      key: key,
      style: AppTextStyles.welcomeText,
      boldStyle: AppTextStyles.welcomeText.copyWith(
        fontWeight: FontWeight.bold,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
  
  /// Create FormattedText with correction original text styling
  static FormattedText correctionOriginal(
    String text, {
    Key? key,
    TextAlign textAlign = TextAlign.start,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
  }) {
    return FormattedText(
      text,
      key: key,
      style: AppTextStyles.correctionOriginal,
      boldStyle: AppTextStyles.correctionOriginal.copyWith(
        fontWeight: FontWeight.bold,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
  
  /// Create FormattedText with correction corrected text styling
  static FormattedText correctionCorrected(
    String text, {
    Key? key,
    TextAlign textAlign = TextAlign.start,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
  }) {
    return FormattedText(
      text,
      key: key,
      style: AppTextStyles.correctionCorrected,
      boldStyle: AppTextStyles.correctionCorrected.copyWith(
        fontWeight: FontWeight.bold,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
  
  /// Create FormattedText with suggestion text styling
  static FormattedText suggestion(
    String text, {
    Key? key,
    TextAlign textAlign = TextAlign.start,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
  }) {
    return FormattedText(
      text,
      key: key,
      style: AppTextStyles.suggestionText,
      boldStyle: AppTextStyles.suggestionText.copyWith(
        fontWeight: FontWeight.bold,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
  
  /// Create FormattedText with explanation text styling
  static FormattedText explanation(
    String text, {
    Key? key,
    TextAlign textAlign = TextAlign.start,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
  }) {
    return FormattedText(
      text,
      key: key,
      style: AppTextStyles.explanationText,
      boldStyle: AppTextStyles.explanationText.copyWith(
        fontWeight: FontWeight.bold,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// Helper class for testing FormattedText parsing
class FormattedTextParser {
  /// Test if text contains bold markers
  static bool containsBoldMarkers(String text) {
    return RegExp(r'\*\*(.*?)\*\*').hasMatch(text);
  }
  
  /// Extract all bold text from a string
  static List<String> extractBoldText(String text) {
    final RegExp boldRegex = RegExp(r'\*\*(.*?)\*\*');
    return boldRegex.allMatches(text)
        .map((match) => match.group(1) ?? '')
        .where((text) => text.isNotEmpty)
        .toList();
  }
  
  /// Remove all bold markers from text
  static String removeBoldMarkers(String text) {
    return text.replaceAll(RegExp(r'\*\*(.*?)\*\*'), r'$1');
  }
  
  /// Count the number of bold segments in text
  static int countBoldSegments(String text) {
    return RegExp(r'\*\*(.*?)\*\*').allMatches(text).length;
  }
  
  /// Validate that bold markers are properly paired
  static bool validateBoldMarkers(String text) {
    final markers = '**'.allMatches(text).length;
    return markers % 2 == 0; // Even number of markers means properly paired
  }
  
  /// Get text segments with their formatting information
  static List<TextSegment> getTextSegments(String text) {
    final List<TextSegment> segments = [];
    final RegExp boldRegex = RegExp(r'\*\*(.*?)\*\*');
    
    int lastMatchEnd = 0;
    
    for (final Match match in boldRegex.allMatches(text)) {
      // Add normal text before the bold text
      if (match.start > lastMatchEnd) {
        final normalText = text.substring(lastMatchEnd, match.start);
        if (normalText.isNotEmpty) {
          segments.add(TextSegment(normalText, false));
        }
      }
      
      // Add bold text
      final boldText = match.group(1) ?? '';
      if (boldText.isNotEmpty) {
        segments.add(TextSegment(boldText, true));
      }
      
      lastMatchEnd = match.end;
    }
    
    // Add remaining normal text
    if (lastMatchEnd < text.length) {
      final remainingText = text.substring(lastMatchEnd);
      if (remainingText.isNotEmpty) {
        segments.add(TextSegment(remainingText, false));
      }
    }
    
    // If no bold markers were found, return the entire text as normal
    if (segments.isEmpty) {
      segments.add(TextSegment(text, false));
    }
    
    return segments;
  }
}

/// Represents a segment of text with formatting information
class TextSegment {
  final String text;
  final bool isBold;
  
  const TextSegment(this.text, this.isBold);
  
  @override
  String toString() => 'TextSegment(text: "$text", isBold: $isBold)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TextSegment && 
           other.text == text && 
           other.isBold == isBold;
  }
  
  @override
  int get hashCode => Object.hash(text, isBold);
}