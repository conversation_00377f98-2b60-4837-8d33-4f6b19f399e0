steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/deutschkorrekt-backend:latest', '.']

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/deutschkorrekt-backend:latest']

  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'deutschkorrekt-backend'
      - '--image=gcr.io/$PROJECT_ID/deutschkorrekt-backend:latest'
      - '--region=europe-west3'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--memory=1Gi'
      - '--cpu=2'
      - '--cpu-boost'
      - '--min-instances=1'
      - '--max-instances=15'
      - '--concurrency=50'
      - '--timeout=300s'
      - '--execution-environment=gen2'
      - '--session-affinity'
      - '--set-env-vars=GOOGLE_PROJECT_ID=$PROJECT_ID,USE_SECRET_MANAGER=True'
      - '--set-secrets=DEEPGRAM_API_KEY=DEEPGRAM_API_KEY:latest,GROQ_API_KEY=GROQ_API_KEY:latest'

images:
  - 'gcr.io/$PROJECT_ID/deutschkorrekt-backend:latest'

options:
  logging: CLOUD_LOGGING_ONLY