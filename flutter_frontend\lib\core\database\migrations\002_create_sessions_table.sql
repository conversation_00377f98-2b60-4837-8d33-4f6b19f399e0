-- Create Sessions table for tracking user interactions
CREATE TABLE IF NOT EXISTS sessions (
  session_id SERIAL PRIMARY KEY,
  email TEXT NOT NULL REFERENCES users(email) ON DELETE CASCADE,
  message TEXT NOT NULL,
  response TEXT NOT NULL,
  datetime TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_sessions_email ON sessions(email);
CREATE INDEX IF NOT EXISTS idx_sessions_datetime ON sessions(datetime);
CREATE INDEX IF NOT EXISTS idx_sessions_email_datetime ON sessions(email, datetime);

-- Add constraint to ensure message and response are not empty
ALTER TABLE sessions ADD CONSTRAINT check_message_not_empty CHECK (LENGTH(TRIM(message)) > 0);
ALTER TABLE sessions ADD CONSTRAINT check_response_not_empty CHECK (LENGTH(TRIM(response)) > 0);