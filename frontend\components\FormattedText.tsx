import React from 'react';
import { Text, TextStyle } from 'react-native';

interface FormattedTextProps {
  text: string;
  style?: TextStyle | TextStyle[];
  boldStyle?: TextStyle;
}

/**
 * Component that renders text with **bold** formatting
 * Parses text between ** markers and renders them as bold
 */
export const FormattedText: React.FC<FormattedTextProps> = ({ 
  text, 
  style = {}, 
  boldStyle = {} 
}) => {
  // Parse text and split by ** markers
  const parseText = (inputText: string) => {
    const parts = inputText.split('**');
    const elements: React.ReactNode[] = [];
    
    parts.forEach((part, index) => {
      if (index % 2 === 0) {
        // Even indices are regular text
        if (part) {
          elements.push(
            <Text key={index} style={style}>
              {part}
            </Text>
          );
        }
      } else {
        // Odd indices are bold text (between ** markers)
        if (part) {
          elements.push(
            <Text key={index} style={[style, { fontWeight: 'bold' }, boldStyle].flat()}>
              {part}
            </Text>
          );
        }
      }
    });
    
    return elements;
  };

  return (
    <Text style={style}>
      {parseText(text)}
    </Text>
  );
};