import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../utils/error_handler.dart';

/// Analytics service for tracking user behavior and app performance
class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  final List<AnalyticsEvent> _eventQueue = [];
  final Map<String, dynamic> _userProperties = {};
  final Map<String, Stopwatch> _timers = {};
  Timer? _flushTimer;
  bool _isInitialized = false;

  /// Initialize analytics service
  Future<void> initialize({
    String? userId,
    Map<String, dynamic>? userProperties,
  }) async {
    try {
      if (userId != null) {
        _userProperties['user_id'] = userId;
      }
      
      if (userProperties != null) {
        _userProperties.addAll(userProperties);
      }

      // Add device and app information
      _userProperties.addAll({
        'platform': defaultTargetPlatform.name,
        'app_version': '1.0.0', // This would come from package info
        'timestamp': DateTime.now().toIso8601String(),
      });

      // Start periodic flush
      _startPeriodicFlush();
      
      _isInitialized = true;
      
      // Track initialization
      trackEvent('analytics_initialized', {
        'user_id': userId,
        'properties_count': _userProperties.length,
      });
    } catch (e) {
      ErrorHandler.logError('Analytics initialization', e);
    }
  }

  /// Track an event
  void trackEvent(String eventName, [Map<String, dynamic>? properties]) {
    if (!_isInitialized) {
      print('Analytics not initialized. Event "$eventName" not tracked.');
      return;
    }

    try {
      final event = AnalyticsEvent(
        name: eventName,
        properties: {
          ..._userProperties,
          ...?properties,
          'timestamp': DateTime.now().toIso8601String(),
        },
        timestamp: DateTime.now(),
      );

      _eventQueue.add(event);
      
      // Flush immediately for critical events
      if (_isCriticalEvent(eventName)) {
        _flushEvents();
      }
    } catch (e) {
      ErrorHandler.logError('Track event', e);
    }
  }

  /// Track user authentication events
  void trackAuth(String action, {Map<String, dynamic>? properties}) {
    trackEvent('auth_$action', {
      'action': action,
      ...?properties,
    });
  }

  /// Track user profile events
  void trackProfile(String action, {Map<String, dynamic>? properties}) {
    trackEvent('profile_$action', {
      'action': action,
      ...?properties,
    });
  }

  /// Track credit-related events
  void trackCredit(String action, {Map<String, dynamic>? properties}) {
    trackEvent('credit_$action', {
      'action': action,
      ...?properties,
    });
  }

  /// Track session events
  void trackSession(String action, {Map<String, dynamic>? properties}) {
    trackEvent('session_$action', {
      'action': action,
      ...?properties,
    });
  }

  /// Track error events
  void trackError(String error, {
    String? context,
    Map<String, dynamic>? properties,
  }) {
    trackEvent('error_occurred', {
      'error': error,
      'context': context,
      'severity': 'error',
      ...?properties,
    });
  }

  /// Track performance metrics
  void trackPerformance(String metric, double value, {
    String? unit,
    Map<String, dynamic>? properties,
  }) {
    trackEvent('performance_metric', {
      'metric': metric,
      'value': value,
      'unit': unit,
      ...?properties,
    });
  }

  /// Start timing an operation
  void startTimer(String timerName) {
    _timers[timerName] = Stopwatch()..start();
  }

  /// Stop timing and track the duration
  void stopTimer(String timerName, {Map<String, dynamic>? properties}) {
    final stopwatch = _timers.remove(timerName);
    if (stopwatch != null) {
      stopwatch.stop();
      trackPerformance(
        timerName,
        stopwatch.elapsedMilliseconds.toDouble(),
        unit: 'milliseconds',
        properties: properties,
      );
    }
  }

  /// Set user properties
  void setUserProperties(Map<String, dynamic> properties) {
    _userProperties.addAll(properties);
    
    trackEvent('user_properties_updated', {
      'properties_count': properties.length,
    });
  }

  /// Set user ID
  void setUserId(String userId) {
    _userProperties['user_id'] = userId;
    
    trackEvent('user_id_set', {
      'user_id': userId,
    });
  }

  /// Track screen view
  void trackScreenView(String screenName, {Map<String, dynamic>? properties}) {
    trackEvent('screen_view', {
      'screen_name': screenName,
      ...?properties,
    });
  }

  /// Track user interaction
  void trackInteraction(String element, String action, {
    Map<String, dynamic>? properties,
  }) {
    trackEvent('user_interaction', {
      'element': element,
      'action': action,
      ...?properties,
    });
  }

  /// Track app lifecycle events
  void trackAppLifecycle(String event) {
    trackEvent('app_lifecycle', {
      'event': event,
    });
  }

  /// Get analytics summary
  Map<String, dynamic> getAnalyticsSummary() {
    return {
      'is_initialized': _isInitialized,
      'queued_events': _eventQueue.length,
      'user_properties': _userProperties.length,
      'active_timers': _timers.length,
      'last_flush': _flushTimer?.isActive ?? false,
    };
  }

  /// Flush events immediately
  Future<void> _flushEvents() async {
    if (_eventQueue.isEmpty) return;

    try {
      final eventsToFlush = List<AnalyticsEvent>.from(_eventQueue);
      _eventQueue.clear();

      // In a real implementation, this would send events to an analytics service
      // For now, we'll just log them
      if (kDebugMode) {
        print('📊 Analytics: Flushing ${eventsToFlush.length} events');
        for (final event in eventsToFlush) {
          print('  - ${event.name}: ${event.properties}');
        }
      }

      // Here you would send to your analytics service:
      // await _sendToAnalyticsService(eventsToFlush);
      
    } catch (e) {
      ErrorHandler.logError('Flush analytics events', e);
      // Re-add events to queue if flush failed
      // _eventQueue.addAll(eventsToFlush);
    }
  }

  /// Start periodic flush timer
  void _startPeriodicFlush() {
    _flushTimer?.cancel();
    _flushTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _flushEvents();
    });
  }

  /// Check if event is critical and should be flushed immediately
  bool _isCriticalEvent(String eventName) {
    const criticalEvents = [
      'error_occurred',
      'auth_login',
      'auth_logout',
      'auth_signup',
      'credit_exhausted',
      'session_failed',
    ];
    
    return criticalEvents.contains(eventName);
  }

  /// Dispose analytics service
  void dispose() {
    _flushTimer?.cancel();
    _flushEvents(); // Final flush
    _eventQueue.clear();
    _userProperties.clear();
    _timers.clear();
    _isInitialized = false;
  }
}

/// Analytics event model
class AnalyticsEvent {
  final String name;
  final Map<String, dynamic> properties;
  final DateTime timestamp;

  const AnalyticsEvent({
    required this.name,
    required this.properties,
    required this.timestamp,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'properties': properties,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create from JSON
  factory AnalyticsEvent.fromJson(Map<String, dynamic> json) {
    return AnalyticsEvent(
      name: json['name'] as String,
      properties: json['properties'] as Map<String, dynamic>,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

/// Analytics mixin for easy tracking in widgets
mixin AnalyticsMixin {
  final AnalyticsService _analytics = AnalyticsService();

  void trackEvent(String eventName, [Map<String, dynamic>? properties]) {
    _analytics.trackEvent(eventName, properties);
  }

  void trackScreenView(String screenName, [Map<String, dynamic>? properties]) {
    _analytics.trackScreenView(screenName, properties);
  }

  void trackInteraction(String element, String action, [Map<String, dynamic>? properties]) {
    _analytics.trackInteraction(element, action, properties);
  }

  void trackError(String error, {String? context, Map<String, dynamic>? properties}) {
    _analytics.trackError(error, context: context, properties: properties);
  }

  void startTimer(String timerName) {
    _analytics.startTimer(timerName);
  }

  void stopTimer(String timerName, [Map<String, dynamic>? properties]) {
    _analytics.stopTimer(timerName, properties: properties);
  }
}