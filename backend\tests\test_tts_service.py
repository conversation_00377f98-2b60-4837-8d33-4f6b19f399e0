"""
Unit tests for TTS Service.
Tests the TTSService class with comprehensive coverage of:
- Text validation and sanitization
- Speech generation with various inputs
- Error handling and categorization
- Integration with Google TTS client
- Health check functionality
- Service information retrieval
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from services.tts_service import TTSService


class TestTTSService:
    """Test suite for TTSService class."""

    @pytest.fixture
    def mock_google_tts_client(self):
        """Mock Google TTS client."""
        mock_client = Mock()
        
        # Mock successful response
        async def mock_generate_speech(text: str):
            return {
                "audio_data": b"fake_audio_data",
                "content_type": "audio/mpeg",
                "duration_seconds": 2.5,
                "processing_time": 0.5
            }
        
        mock_client.generate_speech = AsyncMock(side_effect=mock_generate_speech)
        mock_client.get_client_info.return_value = {
            "voice_name": "de-DE-Chirp3-HD-Aoede",
            "client_initialized": True
        }
        
        return mock_client

    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        with patch('services.tts_service.settings') as mock_settings:
            mock_settings.tts_max_text_length = 500
            yield mock_settings

    @pytest.fixture
    def tts_service(self, mock_google_tts_client, mock_settings):
        """Create TTSService instance with mocked dependencies."""
        with patch('services.tts_service.google_tts_client', mock_google_tts_client):
            return TTSService()

    @pytest.mark.asyncio
    async def test_generate_speech_success(self, tts_service):
        """Test successful speech generation."""
        result = await tts_service.generate_speech("Hallo, das ist ein Test.", "test-request-123")
        
        assert result["success"] is True
        assert result["audio_data"] == b"fake_audio_data"
        assert result["content_type"] == "audio/mpeg"
        assert result["duration_seconds"] == 2.5
        assert result["processing_time"] > 0
        assert result["original_text"] == "Hallo, das ist ein Test."
        assert result["sanitized_text"] == "Hallo, das ist ein Test."

    @pytest.mark.asyncio
    async def test_generate_speech_empty_text(self, tts_service):
        """Test speech generation with empty text."""
        result = await tts_service.generate_speech("", "test-request-123")
        
        assert result["success"] is False
        assert result["error_code"] == "INVALID_INPUT"
        assert "empty" in result["error_message"].lower()
        assert result["processing_time"] >= 0

    @pytest.mark.asyncio
    async def test_generate_speech_whitespace_only(self, tts_service):
        """Test speech generation with whitespace-only text."""
        result = await tts_service.generate_speech("   \n\t   ", "test-request-123")
        
        assert result["success"] is False
        assert result["error_code"] == "INVALID_INPUT"
        assert "empty" in result["error_message"].lower()

    @pytest.mark.asyncio
    async def test_generate_speech_text_too_long(self, tts_service):
        """Test speech generation with text exceeding maximum length."""
        long_text = "x" * 501  # Exceeds max_text_length of 500
        result = await tts_service.generate_speech(long_text, "test-request-123")
        
        assert result["success"] is False
        assert result["error_code"] == "INVALID_INPUT"
        assert "too long" in result["error_message"].lower()

    @pytest.mark.asyncio
    async def test_generate_speech_non_string_input(self, tts_service):
        """Test speech generation with non-string input."""
        result = await tts_service.generate_speech(123, "test-request-123")
        
        assert result["success"] is False
        # Non-string input causes an internal error during validation
        assert result["error_code"] == "INTERNAL_ERROR"
        assert "TTS generation failed" in result["error_message"]

    @pytest.mark.asyncio
    async def test_generate_speech_google_tts_value_error(self, tts_service):
        """Test handling of ValueError from Google TTS client."""
        tts_service.google_tts.generate_speech.side_effect = ValueError("Invalid input")
        
        result = await tts_service.generate_speech("Test text", "test-request-123")
        
        assert result["success"] is False
        assert result["error_code"] == "INVALID_INPUT"
        assert "Invalid input" in result["error_message"]

    @pytest.mark.asyncio
    async def test_generate_speech_quota_exceeded_error(self, tts_service):
        """Test handling of quota exceeded error."""
        tts_service.google_tts.generate_speech.side_effect = Exception("TTS API quota exceeded")
        
        result = await tts_service.generate_speech("Test text", "test-request-123")
        
        assert result["success"] is False
        assert result["error_code"] == "QUOTA_EXCEEDED"
        assert "quota exceeded" in result["error_message"].lower()

    @pytest.mark.asyncio
    async def test_generate_speech_permission_denied_error(self, tts_service):
        """Test handling of permission denied error."""
        tts_service.google_tts.generate_speech.side_effect = Exception("TTS API permission denied")
        
        result = await tts_service.generate_speech("Test text", "test-request-123")
        
        assert result["success"] is False
        assert result["error_code"] == "PERMISSION_DENIED"
        assert "permission denied" in result["error_message"].lower()

    @pytest.mark.asyncio
    async def test_generate_speech_service_unavailable_error(self, tts_service):
        """Test handling of service unavailable error."""
        tts_service.google_tts.generate_speech.side_effect = Exception("TTS service unavailable")
        
        result = await tts_service.generate_speech("Test text", "test-request-123")
        
        assert result["success"] is False
        assert result["error_code"] == "SERVICE_UNAVAILABLE"
        assert "service unavailable" in result["error_message"].lower()

    @pytest.mark.asyncio
    async def test_generate_speech_generic_error(self, tts_service):
        """Test handling of generic errors."""
        tts_service.google_tts.generate_speech.side_effect = Exception("Unexpected error")
        
        result = await tts_service.generate_speech("Test text", "test-request-123")
        
        assert result["success"] is False
        assert result["error_code"] == "INTERNAL_ERROR"
        assert "Unexpected error" in result["error_message"]

    def test_validate_and_sanitize_text_valid(self, tts_service):
        """Test text validation and sanitization with valid input."""
        result = tts_service._validate_and_sanitize_text("Valid text")
        
        assert result["valid"] is True
        assert result["sanitized_text"] == "Valid text"
        assert result["original_length"] == 10
        assert result["sanitized_length"] == 10

    def test_validate_and_sanitize_text_empty(self, tts_service):
        """Test text validation with empty input."""
        result = tts_service._validate_and_sanitize_text("")
        
        assert result["valid"] is False
        assert "non-empty string" in result["error"]

    def test_validate_and_sanitize_text_none(self, tts_service):
        """Test text validation with None input."""
        result = tts_service._validate_and_sanitize_text(None)
        
        assert result["valid"] is False
        assert "non-empty string" in result["error"]

    def test_validate_and_sanitize_text_whitespace_only(self, tts_service):
        """Test text validation with whitespace-only input."""
        result = tts_service._validate_and_sanitize_text("   \n\t   ")
        
        assert result["valid"] is False
        assert "empty or only whitespace" in result["error"]

    def test_validate_and_sanitize_text_too_long(self, tts_service):
        """Test text validation with text too long."""
        long_text = "x" * 501
        result = tts_service._validate_and_sanitize_text(long_text)
        
        assert result["valid"] is False
        assert "too long" in result["error"]
        assert "501 characters" in result["error"]
        assert "maximum: 500" in result["error"]

    def test_validate_and_sanitize_text_with_html(self, tts_service):
        """Test text sanitization with HTML tags."""
        html_text = "This is <b>bold</b> and <i>italic</i> text."
        result = tts_service._validate_and_sanitize_text(html_text)
        
        assert result["valid"] is True
        assert result["sanitized_text"] == "This is bold and italic text."

    def test_validate_and_sanitize_text_with_excessive_whitespace(self, tts_service):
        """Test text sanitization with excessive whitespace."""
        messy_text = "This   has    too     much\n\n\nwhitespace."
        result = tts_service._validate_and_sanitize_text(messy_text)
        
        assert result["valid"] is True
        assert result["sanitized_text"] == "This has too much whitespace."

    def test_validate_and_sanitize_text_with_control_characters(self, tts_service):
        """Test text sanitization with control characters."""
        text_with_control = "Text with\x00control\x07characters\x1F."
        result = tts_service._validate_and_sanitize_text(text_with_control)
        
        assert result["valid"] is True
        assert "\x00" not in result["sanitized_text"]
        assert "\x07" not in result["sanitized_text"]
        assert "\x1F" not in result["sanitized_text"]

    def test_validate_and_sanitize_text_with_quotes(self, tts_service):
        """Test text sanitization with various quote types."""
        text_with_quotes = "Text with \"smart quotes\" and 'apostrophes'."
        result = tts_service._validate_and_sanitize_text(text_with_quotes)
        
        assert result["valid"] is True
        assert '"smart quotes"' in result["sanitized_text"]
        assert "'apostrophes'" in result["sanitized_text"]

    def test_validate_and_sanitize_text_with_excessive_punctuation(self, tts_service):
        """Test text sanitization with excessive punctuation."""
        text_with_punct = "This is amazing!!! Really??? Yes... definitely...."
        result = tts_service._validate_and_sanitize_text(text_with_punct)
        
        assert result["valid"] is True
        assert "!!!" not in result["sanitized_text"]
        assert "???" not in result["sanitized_text"]
        assert "...." not in result["sanitized_text"]
        assert "!" in result["sanitized_text"]
        assert "?" in result["sanitized_text"]
        assert "..." in result["sanitized_text"]

    def test_validate_and_sanitize_text_becomes_empty_after_sanitization(self, tts_service):
        """Test text that becomes empty after sanitization."""
        html_only_text = "<div><span></span></div>"
        result = tts_service._validate_and_sanitize_text(html_only_text)
        
        assert result["valid"] is False
        assert "no valid content after sanitization" in result["error"]

    def test_sanitize_text_content_normal_text(self, tts_service):
        """Test text sanitization with normal text."""
        text = "This is normal text."
        result = tts_service._sanitize_text_content(text)
        
        assert result == "This is normal text."

    def test_sanitize_text_content_with_html_tags(self, tts_service):
        """Test text sanitization removes HTML tags."""
        html_text = "This is <b>bold</b> and <i>italic</i> text."
        result = tts_service._sanitize_text_content(html_text)
        
        assert result == "This is bold and italic text."
        assert "<b>" not in result
        assert "</b>" not in result

    def test_sanitize_text_content_error_handling(self, tts_service):
        """Test text sanitization error handling."""
        # Mock re.sub to raise an exception
        with patch('services.tts_service.re.sub', side_effect=Exception("Regex error")):
            result = tts_service._sanitize_text_content("Test text")
            # Should return original text if sanitization fails
            assert result == "Test text"

    def test_get_service_info_success(self, tts_service):
        """Test getting service information successfully."""
        info = tts_service.get_service_info()
        
        assert info["service_name"] == "TTS Service"
        assert info["max_text_length"] == 500
        assert "google_tts_client" in info
        assert "features" in info
        assert len(info["features"]) > 0

    def test_get_service_info_error(self, tts_service):
        """Test getting service information with error."""
        tts_service.google_tts.get_client_info.side_effect = Exception("Client error")
        
        info = tts_service.get_service_info()
        
        assert info["service_name"] == "TTS Service"
        assert "status" in info
        assert "error" in info
        assert "Client error" in info["error"]

    @pytest.mark.asyncio
    async def test_health_check_success(self, tts_service):
        """Test successful health check."""
        result = await tts_service.health_check()
        
        assert result["healthy"] is True
        assert result["response_time"] > 0
        assert result["audio_size"] == len(b"fake_audio_data")
        assert "operational" in result["message"]

    @pytest.mark.asyncio
    async def test_health_check_failure(self, tts_service):
        """Test health check failure."""
        # Mock generate_speech to return failure
        async def mock_failed_generate_speech(text: str, request_id: str = None):
            return {
                "success": False,
                "error_message": "TTS service is down",
                "error_code": "SERVICE_UNAVAILABLE"
            }
        
        with patch.object(tts_service, 'generate_speech', side_effect=mock_failed_generate_speech):
            result = await tts_service.health_check()
            
            assert result["healthy"] is False
            assert result["response_time"] > 0
            assert "TTS service is down" in result["error"]
            assert result["error_code"] == "SERVICE_UNAVAILABLE"

    @pytest.mark.asyncio
    async def test_health_check_exception(self, tts_service):
        """Test health check with exception."""
        with patch.object(tts_service, 'generate_speech', side_effect=Exception("Unexpected error")):
            result = await tts_service.health_check()
            
            assert result["healthy"] is False
            assert result["response_time"] > 0
            assert "Health check failed" in result["error"]
            assert "Unexpected error" in result["error"]

    @pytest.mark.asyncio
    async def test_generate_speech_without_request_id(self, tts_service):
        """Test speech generation without request ID."""
        result = await tts_service.generate_speech("Test text")
        
        assert result["success"] is True
        assert result["audio_data"] == b"fake_audio_data"

    @pytest.mark.asyncio
    async def test_generate_speech_with_german_text(self, tts_service):
        """Test speech generation with German text containing special characters."""
        german_text = "Äpfel, Öl und Übergänge sind schön in München."
        result = await tts_service.generate_speech(german_text, "german-test-123")
        
        assert result["success"] is True
        assert result["original_text"] == german_text
        assert result["sanitized_text"] == german_text  # Should remain unchanged

    def test_validation_error_handling_exception(self, tts_service):
        """Test validation error handling when validation itself raises exception."""
        # Mock _sanitize_text_content to raise an exception
        with patch.object(tts_service, '_sanitize_text_content', side_effect=Exception("Sanitization error")):
            result = tts_service._validate_and_sanitize_text("Test text")
            
            assert result["valid"] is False
            assert "Text validation failed" in result["error"]
            assert "Sanitization error" in result["error"]

    @pytest.mark.asyncio
    async def test_generate_speech_timing_accuracy(self, tts_service):
        """Test that processing time is accurately measured."""
        # Add delay to Google TTS client
        async def slow_generate_speech(text: str):
            import asyncio
            await asyncio.sleep(0.1)  # 100ms delay
            return {
                "audio_data": b"fake_audio_data",
                "content_type": "audio/mpeg",
                "duration_seconds": 2.5,
                "processing_time": 0.5
            }
        
        tts_service.google_tts.generate_speech = AsyncMock(side_effect=slow_generate_speech)
        
        result = await tts_service.generate_speech("Test text", "timing-test")
        
        assert result["success"] is True
        # Processing time should include the delay
        assert result["processing_time"] >= 0.1

    @pytest.mark.asyncio
    async def test_generate_speech_preserves_original_and_sanitized_text(self, tts_service):
        """Test that both original and sanitized text are preserved in response."""
        original_text = "This is <b>bold</b> text with   extra   spaces."
        result = await tts_service.generate_speech(original_text, "preserve-test")
        
        assert result["success"] is True
        assert result["original_text"] == original_text
        assert result["sanitized_text"] == "This is bold text with extra spaces."
        assert result["original_text"] != result["sanitized_text"]