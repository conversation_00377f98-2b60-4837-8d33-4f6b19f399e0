/// Audio configuration model matching Expo version settings
class AudioConfig {
  final int sampleRate;
  final int channels;
  final int chunkSize;
  final String encoding;
  final int bitDepth;
  final bool enableNoiseReduction;
  final bool enableEchoCancellation;
  final double gainLevel;
  
  const AudioConfig({
    this.sampleRate = 16000,
    this.channels = 1,
    this.chunkSize = 1024,
    this.encoding = 'PCM16',
    this.bitDepth = 16,
    this.enableNoiseReduction = false,
    this.enableEchoCancellation = false,
    this.gainLevel = 1.0,
  });
  
  /// Create default configuration matching Expo version
  factory AudioConfig.defaultConfig() {
    return const AudioConfig(
      sampleRate: 16000,  // 16kHz as per Expo version
      channels: 1,        // Mono as per Expo version
      chunkSize: 1024,    // 1024 samples as per Expo version
      encoding: 'PCM16',  // PCM16 format as per Expo version
      bitDepth: 16,
      enableNoiseReduction: false,
      enableEchoCancellation: false,
      gainLevel: 1.0,
    );
  }
  
  /// Create high quality configuration
  factory AudioConfig.highQuality() {
    return const AudioConfig(
      sampleRate: 44100,
      channels: 2,
      chunkSize: 2048,
      encoding: 'PCM16',
      bitDepth: 16,
      enableNoiseReduction: true,
      enableEchoCancellation: true,
      gainLevel: 1.2,
    );
  }
  
  /// Create from JSON
  factory AudioConfig.fromJson(Map<String, dynamic> json) {
    return AudioConfig(
      sampleRate: json['sample_rate'] ?? 16000,
      channels: json['channels'] ?? 1,
      chunkSize: json['chunk_size'] ?? 1024,
      encoding: json['encoding'] ?? 'PCM16',
      bitDepth: json['bit_depth'] ?? 16,
      enableNoiseReduction: json['enable_noise_reduction'] ?? false,
      enableEchoCancellation: json['enable_echo_cancellation'] ?? false,
      gainLevel: (json['gain_level'] ?? 1.0).toDouble(),
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'sample_rate': sampleRate,
      'channels': channels,
      'chunk_size': chunkSize,
      'encoding': encoding,
      'bit_depth': bitDepth,
      'enable_noise_reduction': enableNoiseReduction,
      'enable_echo_cancellation': enableEchoCancellation,
      'gain_level': gainLevel,
    };
  }
  
  /// Calculate bytes per sample
  int get bytesPerSample => (bitDepth / 8).round() * channels;
  
  /// Calculate bytes per chunk
  int get bytesPerChunk => chunkSize * bytesPerSample;
  
  /// Calculate duration per chunk in milliseconds
  double get chunkDurationMs => (chunkSize / sampleRate) * 1000;
  
  /// Check if configuration is valid
  bool get isValid {
    return sampleRate > 0 &&
           channels > 0 &&
           chunkSize > 0 &&
           bitDepth > 0 &&
           gainLevel >= 0.0 &&
           gainLevel <= 2.0;
  }
  
  /// Create a copy with updated properties
  AudioConfig copyWith({
    int? sampleRate,
    int? channels,
    int? chunkSize,
    String? encoding,
    int? bitDepth,
    bool? enableNoiseReduction,
    bool? enableEchoCancellation,
    double? gainLevel,
  }) {
    return AudioConfig(
      sampleRate: sampleRate ?? this.sampleRate,
      channels: channels ?? this.channels,
      chunkSize: chunkSize ?? this.chunkSize,
      encoding: encoding ?? this.encoding,
      bitDepth: bitDepth ?? this.bitDepth,
      enableNoiseReduction: enableNoiseReduction ?? this.enableNoiseReduction,
      enableEchoCancellation: enableEchoCancellation ?? this.enableEchoCancellation,
      gainLevel: gainLevel ?? this.gainLevel,
    );
  }
  
  @override
  String toString() {
    return 'AudioConfig(sampleRate: $sampleRate, channels: $channels, chunkSize: $chunkSize, encoding: $encoding)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AudioConfig &&
           other.sampleRate == sampleRate &&
           other.channels == channels &&
           other.chunkSize == chunkSize &&
           other.encoding == encoding &&
           other.bitDepth == bitDepth &&
           other.enableNoiseReduction == enableNoiseReduction &&
           other.enableEchoCancellation == enableEchoCancellation &&
           other.gainLevel == gainLevel;
  }
  
  @override
  int get hashCode {
    return Object.hash(
      sampleRate,
      channels,
      chunkSize,
      encoding,
      bitDepth,
      enableNoiseReduction,
      enableEchoCancellation,
      gainLevel,
    );
  }
}