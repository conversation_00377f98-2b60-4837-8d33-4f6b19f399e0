import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/app_error.dart';
import 'audio_focus_service.dart';

/// Service for managing app lifecycle events and their impact on audio recording
class AppLifecycleService with WidgetsBindingObserver {
  static AppLifecycleService? _instance;
  
  /// Singleton instance
  static AppLifecycleService get instance {
    _instance ??= AppLifecycleService._();
    return _instance!;
  }
  
  StreamController<AppLifecycleState>? _lifecycleController;
  StreamController<AppError>? _errorController;
  final AudioFocusService _audioFocusService = AudioFocusService.instance;

  AppLifecycleState _currentState = AppLifecycleState.resumed;
  bool _isInitialized = false;
  bool _isDisposed = false;
  
  // Callbacks for lifecycle events
  VoidCallback? _onAppPaused;
  VoidCallback? _onAppResumed;
  VoidCallback? _onAppDetached;
  VoidCallback? _onAudioFocusLost;
  VoidCallback? _onAudioFocusGained;
  
  AppLifecycleService._();
  
  /// Stream of app lifecycle state changes
  Stream<AppLifecycleState> get lifecycleStream => 
      _lifecycleController?.stream ?? const Stream.empty();
  
  /// Stream of audio focus changes (delegated to AudioFocusService)
  Stream<bool> get audioFocusStream => _audioFocusService.audioFocusStream;
  
  /// Stream of lifecycle-related errors
  Stream<AppError> get errorStream => 
      _errorController?.stream ?? const Stream.empty();
  
  /// Current app lifecycle state
  AppLifecycleState get currentState => _currentState;
  
  /// Whether the app currently has audio focus (delegated to AudioFocusService)
  bool get hasAudioFocus => _audioFocusService.hasAudioFocus;
  
  /// Whether the service is initialized
  bool get isInitialized => _isInitialized;
  
  /// Initialize the lifecycle service
  Future<void> initialize() async {
    if (_isDisposed) {
      throw StateError('AppLifecycleService has been disposed');
    }
    
    if (_isInitialized) return;
    
    _lifecycleController = StreamController<AppLifecycleState>.broadcast();
    _errorController = StreamController<AppError>.broadcast();

    // Initialize audio focus service
    await _audioFocusService.initialize();

    // Register as lifecycle observer
    WidgetsBinding.instance.addObserver(this);

    _isInitialized = true;
    print('AppLifecycleService initialized');
  }
  
  /// Set callback for when app is paused
  void setOnAppPaused(VoidCallback callback) {
    _onAppPaused = callback;
  }
  
  /// Set callback for when app is resumed
  void setOnAppResumed(VoidCallback callback) {
    _onAppResumed = callback;
  }
  
  /// Set callback for when app is detached
  void setOnAppDetached(VoidCallback callback) {
    _onAppDetached = callback;
  }
  
  /// Set callback for when audio focus is lost
  void setOnAudioFocusLost(VoidCallback callback) {
    _onAudioFocusLost = callback;
  }
  
  /// Set callback for when audio focus is gained
  void setOnAudioFocusGained(VoidCallback callback) {
    _onAudioFocusGained = callback;
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    if (_isDisposed) return;
    
    final previousState = _currentState;
    _currentState = state;
    
    _lifecycleController?.add(state);
    
    print('App lifecycle changed: $previousState -> $state');
    
    try {
      switch (state) {
        case AppLifecycleState.paused:
          _handleAppPaused();
          break;
        case AppLifecycleState.resumed:
          _handleAppResumed();
          break;
        case AppLifecycleState.detached:
          _handleAppDetached();
          break;
        case AppLifecycleState.inactive:
          _handleAppInactive();
          break;
        case AppLifecycleState.hidden:
          _handleAppHidden();
          break;
      }
    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Error handling lifecycle state change: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
    }
  }
  
  /// Handle app paused state
  void _handleAppPaused() {
    print('App paused - handling audio recording state');

    // Release audio focus when app is paused
    _audioFocusService.releaseAudioFocus();

    // Call registered callback
    _onAppPaused?.call();
  }

  /// Handle app resumed state
  void _handleAppResumed() {
    print('App resumed - restoring audio recording state');

    // Don't automatically regain audio focus - let the recording service decide
    // when it needs to request focus again

    // Call registered callback
    _onAppResumed?.call();
  }

  /// Handle app detached state
  void _handleAppDetached() {
    print('App detached - cleaning up audio resources');

    // Release audio focus when app is detached
    _audioFocusService.releaseAudioFocus();

    // Perform emergency cleanup for app termination
    _performEmergencyCleanup();

    // Call registered callback
    _onAppDetached?.call();
  }

  /// Perform emergency cleanup when app is being terminated
  void _performEmergencyCleanup() {
    try {
      // This is called when the app is being terminated
      // We need to clean up resources synchronously
      print('Performing emergency cleanup for app termination');

      // Note: We can't use async operations here as the app is terminating
      // The audio focus service and other services should handle their own cleanup

    } catch (e) {
      print('Error during emergency cleanup: $e');
    }
  }
  
  /// Handle app inactive state
  void _handleAppInactive() {
    print('App inactive - preparing for potential pause');
    // App is transitioning, prepare for potential pause
  }
  
  /// Handle app hidden state
  void _handleAppHidden() {
    print('App hidden - handling background state');
    // Similar to paused but app is hidden
    _audioFocusService.releaseAudioFocus();
  }

  /// Request audio focus (delegated to AudioFocusService)
  Future<bool> requestAudioFocus() async {
    return await _audioFocusService.requestAudioFocus();
  }

  /// Release audio focus (delegated to AudioFocusService)
  Future<void> releaseAudioFocus() async {
    await _audioFocusService.releaseAudioFocus();
  }
  
  /// Check if app is in foreground
  bool get isInForeground {
    return _currentState == AppLifecycleState.resumed;
  }
  
  /// Check if app is in background
  bool get isInBackground {
    return _currentState == AppLifecycleState.paused || 
           _currentState == AppLifecycleState.hidden;
  }
  
  /// Check if app can record audio (foreground and has focus)
  bool get canRecordAudio {
    return isInForeground && _audioFocusService.hasAudioFocus;
  }
  
  /// Dispose the service and clean up resources
  Future<void> dispose() async {
    if (_isDisposed) return;
    
    print('Disposing AppLifecycleService');
    
    // Remove lifecycle observer
    WidgetsBinding.instance.removeObserver(this);
    
    // Dispose audio focus service
    await _audioFocusService.dispose();

    // Close streams
    await _lifecycleController?.close();
    await _errorController?.close();

    // Clear callbacks
    _onAppPaused = null;
    _onAppResumed = null;
    _onAppDetached = null;
    _onAudioFocusLost = null;
    _onAudioFocusGained = null;
    
    _isDisposed = true;
    _isInitialized = false;
    
    print('AppLifecycleService disposed');
  }
}
