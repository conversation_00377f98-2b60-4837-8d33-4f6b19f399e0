import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:deutschkorrekt_flutter/presentation/screens/auth_screen.dart';
import 'package:deutschkorrekt_flutter/presentation/providers/auth_provider.dart';

void main() {
  group('AuthScreen', () {
    late AuthProvider mockAuthProvider;

    setUp(() {
      mockAuthProvider = AuthProvider();
    });

    tearDown(() {
      mockAuthProvider.dispose();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: const AuthScreen(),
        ),
      );
    }

    testWidgets('should display app logo and title', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('DeutschKorrekt'), findsOneWidget);
      expect(find.text('Welcome back!'), findsOneWidget);
    });

    testWidgets('should display login form by default', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Sign In'), findsOneWidget);
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.text('Confirm Password'), findsNothing);
    });

    testWidgets('should toggle to signup mode', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find and tap the toggle button
      await tester.tap(find.text('Sign up'));
      await tester.pumpAndSettle();

      expect(find.text('Sign Up'), findsOneWidget);
      expect(find.text('Join us today!'), findsOneWidget);
      expect(find.text('Confirm Password'), findsOneWidget);
    });

    testWidgets('should show forgot password dialog', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Tap forgot password button
      await tester.tap(find.text('Forgot your password?'));
      await tester.pumpAndSettle();

      expect(find.text('Reset Password'), findsOneWidget);
      expect(find.text('Enter your email address'), findsOneWidget);
    });

    testWidgets('should display Google sign in button', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Sign in with Google'), findsOneWidget);
    });

    testWidgets('should show terms and conditions in signup mode', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Switch to signup mode
      await tester.tap(find.text('Sign up'));
      await tester.pumpAndSettle();

      expect(find.text('By signing up, you agree to our Terms of Service and Privacy Policy'), findsOneWidget);
    });

    testWidgets('should validate email field', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Enter invalid email
      await tester.enterText(find.byType(TextFormField).first, 'invalid-email');
      
      // Tap submit button
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();

      // Should show validation error
      expect(find.text('Please enter a valid email address'), findsOneWidget);
    });

    testWidgets('should validate password field', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Enter valid email but short password
      await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(1), '123');
      
      // Tap submit button
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();

      // Should show validation error for password
      expect(find.text('Password must be at least 8 characters long'), findsOneWidget);
    });

    testWidgets('should show password requirements in signup mode', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Switch to signup mode
      await tester.tap(find.text('Sign up'));
      await tester.pumpAndSettle();

      expect(find.text('Password requirements:'), findsOneWidget);
      expect(find.text('At least 8 characters'), findsOneWidget);
      expect(find.text('Contains letters'), findsOneWidget);
      expect(find.text('Contains numbers'), findsOneWidget);
      expect(find.text('Contains special characters'), findsOneWidget);
    });

    testWidgets('should validate password confirmation in signup mode', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Switch to signup mode
      await tester.tap(find.text('Sign up'));
      await tester.pumpAndSettle();

      // Enter different passwords
      await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(1), 'password123');
      await tester.enterText(find.byType(TextFormField).at(2), 'different123');
      
      // Tap submit button
      await tester.tap(find.text('Sign Up'));
      await tester.pumpAndSettle();

      expect(find.text('Passwords do not match'), findsOneWidget);
    });

    testWidgets('should toggle password visibility', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find password field
      final passwordField = find.byType(TextFormField).at(1);
      
      // Enter password
      await tester.enterText(passwordField, 'password123');
      
      // Find and tap visibility toggle
      await tester.tap(find.byIcon(Icons.visibility));
      await tester.pumpAndSettle();

      // Icon should change to visibility_off
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });
  });
}