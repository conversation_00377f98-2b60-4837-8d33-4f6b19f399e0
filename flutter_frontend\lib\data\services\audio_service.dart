import 'dart:async';
import 'dart:typed_data';
import 'package:record/record.dart';
import 'package:flutter/widgets.dart';
import '../models/audio_config.dart';
import '../models/app_error.dart';
import 'permissions_service.dart';
import 'app_lifecycle_service.dart';
import 'realtime_audio_service.dart';

/// Audio service for recording and streaming audio matching Expo version
class AudioService {
  static AudioService? _instance;
  
  /// Singleton instance
  static AudioService get instance {
    _instance ??= AudioService._();
    return _instance!;
  }
  
  final AudioRecorder _recorder = AudioRecorder();
  final PermissionsService _permissionsService = PermissionsService.instance;
  final AppLifecycleService _lifecycleService = AppLifecycleService.instance;
  final RealtimeAudioService _realtimeAudioService = RealtimeAudioService.instance;
  
  StreamController<Uint8List>? _audioStreamController;
  StreamController<AppError>? _errorStreamController;
  StreamController<bool>? _recordingStateController;
  
  StreamSubscription<bool>? _audioFocusSubscription;
  StreamSubscription<AppLifecycleState>? _lifecycleSubscription;
  
  AudioConfig _config = AudioConfig.defaultConfig();
  bool _isRecording = false;
  bool _isInitialized = false;
  bool _isDisposed = false;
  bool _wasRecordingBeforePause = false;
  Timer? _audioStreamTimer;
  
  AudioService._();
  
  /// Stream of audio data chunks
  Stream<Uint8List> get audioStream => _audioStreamController?.stream ?? const Stream.empty();
  
  /// Stream of audio service errors
  Stream<AppError> get errorStream => _errorStreamController?.stream ?? const Stream.empty();
  
  /// Stream of recording state changes
  Stream<bool> get recordingStateStream => _recordingStateController?.stream ?? const Stream.empty();
  
  /// Check if currently recording
  bool get isRecording => _isRecording;
  
  /// Get current audio configuration
  AudioConfig get config => _config;
  
  /// Check if service is initialized
  bool get isInitialized => _isInitialized;
  
  /// Initialize the audio service
  Future<void> initialize({AudioConfig? config}) async {
    if (_isDisposed) {
      throw StateError('AudioService has been disposed');
    }

    if (_isInitialized) return;

    _config = config ?? AudioConfig.defaultConfig();

    _audioStreamController = StreamController<Uint8List>.broadcast();
    _errorStreamController = StreamController<AppError>.broadcast();
    _recordingStateController = StreamController<bool>.broadcast();

    // Initialize real-time audio service
    await _realtimeAudioService.initialize(config: _config);

    // Initialize lifecycle service
    await _lifecycleService.initialize();

    // Set up lifecycle callbacks
    _setupLifecycleCallbacks();

    // Set up real-time audio streaming
    _setupRealtimeAudioStreaming();

    _isInitialized = true;
    print('AudioService initialized with config: $_config');
  }
  
  /// Request microphone permissions
  Future<bool> requestPermissions() async {
    try {
      return await _permissionsService.requestMicrophonePermissionWithHandling();
    } catch (e) {
      final error = AppError.microphonePermissionDenied(
        details: 'Failed to request microphone permissions: $e',
      );
      _errorStreamController?.add(error);
      return false;
    }
  }
  
  /// Check if has microphone permissions
  Future<bool> hasPermissions() async {
    return await _permissionsService.hasMicrophonePermission();
  }
  
  /// Start audio recording with real-time streaming
  Future<void> startRecording() async {
    if (_isDisposed) {
      throw StateError('AudioService has been disposed');
    }

    if (!_isInitialized) {
      await initialize();
    }

    if (_isRecording) {
      print('Already recording, ignoring start request');
      return;
    }

    try {
      // Check permissions
      if (!await hasPermissions()) {
        final hasPermission = await requestPermissions();
        if (!hasPermission) {
          final error = AppError.microphonePermissionDenied();
          _errorStreamController?.add(error);
          return;
        }
      }

      // Start real-time audio recording using the real-time service
      await _realtimeAudioService.startRecording();

      print('Real-time audio recording started');

    } catch (e) {
      _isRecording = false;
      _recordingStateController?.add(false);

      final error = AppError.audioRecordingFailed(
        details: 'Failed to start real-time audio recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorStreamController?.add(error);
    }
  }
  

  
  /// Stop audio recording
  Future<void> stopRecording() async {
    if (!_isRecording) {
      print('Not recording, ignoring stop request');
      return;
    }

    try {
      await _realtimeAudioService.stopRecording();
      print('Real-time audio recording stopped');

    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Failed to stop real-time audio recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorStreamController?.add(error);
    }
  }

  /// Pause audio recording
  Future<void> pauseRecording() async {
    if (!_isRecording) return;

    try {
      await _realtimeAudioService.pauseRecording();
      print('Real-time audio recording paused');
    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Failed to pause real-time audio recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorStreamController?.add(error);
    }
  }

  /// Resume audio recording
  Future<void> resumeRecording() async {
    if (!_isRecording) return;

    try {
      await _realtimeAudioService.resumeRecording();
      print('Real-time audio recording resumed');
    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Failed to resume real-time audio recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorStreamController?.add(error);
    }
  }
  
  /// Update audio configuration
  void updateConfig(AudioConfig newConfig) {
    if (_isRecording) {
      print('Cannot update config while recording');
      return;
    }
    
    _config = newConfig;
    print('Audio config updated: $_config');
  }
  
  /// Get current recording amplitude (if supported)
  Future<double> getAmplitude() async {
    try {
      final amplitude = await _recorder.getAmplitude();
      return amplitude.current;
    } catch (e) {
      return 0.0;
    }
  }
  
  /// Check if recording is supported on this device
  Future<bool> isRecordingSupported() async {
    try {
      return await _recorder.hasPermission();
    } catch (e) {
      return false;
    }
  }
  
  /// Get available audio encoders
  Future<List<AudioEncoder>> getAvailableEncoders() async {
    // Return encoders that match our requirements
    return [AudioEncoder.pcm16bits];
  }
  
  /// Convert audio data to PCM16 format (matching Expo version)
  Uint8List convertToPCM16(Uint8List audioData) {
    // This is a placeholder implementation
    // In a real scenario, you might need to convert from other formats to PCM16
    return audioData;
  }
  
  /// Get audio service statistics
  Map<String, dynamic> getStats() {
    return {
      'isRecording': _isRecording,
      'isInitialized': _isInitialized,
      'config': _config.toJson(),
      'hasPermissions': _permissionsService.hasMicrophonePermission(),
    };
  }
  
  /// Set up lifecycle callbacks for proper audio handling
  void _setupLifecycleCallbacks() {
    _lifecycleService.setOnAppPaused(() {
      if (_isRecording) {
        _wasRecordingBeforePause = true;
        pauseRecording();
      }
    });

    _lifecycleService.setOnAppResumed(() {
      if (_wasRecordingBeforePause) {
        _wasRecordingBeforePause = false;
        resumeRecording();
      }
    });

    _audioFocusSubscription = _lifecycleService.audioFocusStream.listen((hasFocus) {
      if (!hasFocus && _isRecording) {
        pauseRecording();
      }
    });

    _lifecycleSubscription = _lifecycleService.lifecycleStream.listen((state) {
      if (state == AppLifecycleState.detached && _isRecording) {
        stopRecording();
      }
    });
  }

  /// Set up real-time audio streaming from the real-time service
  void _setupRealtimeAudioStreaming() {
    // Forward audio data from real-time service to our stream
    _realtimeAudioService.audioStream.listen(
      (audioData) {
        _audioStreamController?.add(audioData);
      },
      onError: (error) {
        _errorStreamController?.add(error is AppError ? error : AppError.audioRecordingFailed(
          details: 'Real-time audio streaming error: $error',
          originalException: error is Exception ? error : Exception(error.toString()),
        ));
      },
    );

    // Forward recording state changes
    _realtimeAudioService.recordingStateStream.listen(
      (isRecording) {
        _isRecording = isRecording;
        _recordingStateController?.add(isRecording);
      },
    );

    // Forward errors from real-time service
    _realtimeAudioService.errorStream.listen(
      (error) {
        _errorStreamController?.add(error);
      },
    );
  }

  /// Dispose the service and clean up resources
  Future<void> dispose() async {
    if (_isDisposed) return;

    _isDisposed = true;

    if (_isRecording) {
      await stopRecording();
    }

    await _realtimeAudioService.dispose();
    await _recorder.dispose();

    await _audioFocusSubscription?.cancel();
    await _lifecycleSubscription?.cancel();

    await _audioStreamController?.close();
    await _errorStreamController?.close();
    await _recordingStateController?.close();

    _audioStreamController = null;
    _errorStreamController = null;
    _recordingStateController = null;

    _isInitialized = false;
  }
}