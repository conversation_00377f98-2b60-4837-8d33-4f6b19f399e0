import 'package:flutter/foundation.dart';
import '../../data/models/app_error.dart';

/// Centralized error handling for providers
class ProviderErrorHandler {
  static final Map<String, List<AppError>> _errorHistory = {};
  static final Map<String, int> _retryCount = {};
  static const int maxRetries = 3;
  
  /// Handle provider errors with retry logic
  static Future<bool> handleProviderError({
    required String providerId,
    required AppError error,
    required Future<void> Function() retryAction,
  }) async {
    // Add to error history
    _errorHistory.putIfAbsent(providerId, () => []).add(error);
    
    // Keep only last 10 errors per provider
    if (_errorHistory[providerId]!.length > 10) {
      _errorHistory[providerId]!.removeAt(0);
    }
    
    // Check retry count
    final currentRetries = _retryCount[providerId] ?? 0;
    
    if (currentRetries < maxRetries) {
      _retryCount[providerId] = currentRetries + 1;
      
      if (kDebugMode) {
        print('Retrying $providerId (attempt ${currentRetries + 1}/$maxRetries)');
      }
      
      try {
        // Wait before retry with exponential backoff
        await Future.delayed(Duration(milliseconds: 500 * (currentRetries + 1)));
        await retryAction();
        
        // Reset retry count on success
        _retryCount[providerId] = 0;
        return true;
        
      } catch (e) {
        if (kDebugMode) {
          print('Retry failed for $providerId: $e');
        }
        return false;
      }
    } else {
      if (kDebugMode) {
        print('Max retries exceeded for $providerId');
      }
      return false;
    }
  }
  
  /// Get error history for a provider
  static List<AppError> getErrorHistory(String providerId) {
    return List.unmodifiable(_errorHistory[providerId] ?? []);
  }
  
  /// Get retry count for a provider
  static int getRetryCount(String providerId) {
    return _retryCount[providerId] ?? 0;
  }
  
  /// Reset error state for a provider
  static void resetProvider(String providerId) {
    _errorHistory.remove(providerId);
    _retryCount.remove(providerId);
  }
  
  /// Reset all provider error states
  static void resetAll() {
    _errorHistory.clear();
    _retryCount.clear();
  }
  
  /// Check if provider has critical errors
  static bool hasCriticalErrors(String providerId) {
    final errors = _errorHistory[providerId] ?? [];
    return errors.any((error) => 
      error.type == AppErrorType.microphonePermissionDenied ||
      error.type == AppErrorType.websocketConnectionFailed
    );
  }
  
  /// Get provider health status
  static ProviderHealthStatus getProviderHealth(String providerId) {
    final errors = _errorHistory[providerId] ?? [];
    final retries = _retryCount[providerId] ?? 0;
    
    if (errors.isEmpty) {
      return ProviderHealthStatus.healthy;
    }
    
    if (hasCriticalErrors(providerId)) {
      return ProviderHealthStatus.critical;
    }
    
    if (retries >= maxRetries) {
      return ProviderHealthStatus.failed;
    }
    
    if (errors.length > 5) {
      return ProviderHealthStatus.degraded;
    }
    
    return ProviderHealthStatus.warning;
  }
  
  /// Get overall system health
  static SystemHealthStatus getSystemHealth() {
    final providerIds = ['settings', 'profile', 'audio', 'chat'];
    final healthStatuses = providerIds.map(getProviderHealth).toList();
    
    if (healthStatuses.any((status) => status == ProviderHealthStatus.critical)) {
      return SystemHealthStatus.critical;
    }
    
    if (healthStatuses.any((status) => status == ProviderHealthStatus.failed)) {
      return SystemHealthStatus.failed;
    }
    
    if (healthStatuses.any((status) => status == ProviderHealthStatus.degraded)) {
      return SystemHealthStatus.degraded;
    }
    
    if (healthStatuses.any((status) => status == ProviderHealthStatus.warning)) {
      return SystemHealthStatus.warning;
    }
    
    return SystemHealthStatus.healthy;
  }
  
  /// Get comprehensive error statistics
  static Map<String, dynamic> getErrorStats() {
    return {
      'totalProviders': _errorHistory.keys.length,
      'totalErrors': _errorHistory.values.fold(0, (sum, errors) => sum + errors.length),
      'providerStats': _errorHistory.map((providerId, errors) => MapEntry(
        providerId,
        {
          'errorCount': errors.length,
          'retryCount': _retryCount[providerId] ?? 0,
          'health': getProviderHealth(providerId).toString(),
          'lastError': errors.isNotEmpty ? errors.last.toString() : null,
        },
      )),
      'systemHealth': getSystemHealth().toString(),
    };
  }
}

/// Provider health status enumeration
enum ProviderHealthStatus {
  healthy,
  warning,
  degraded,
  failed,
  critical,
}

/// System health status enumeration
enum SystemHealthStatus {
  healthy,
  warning,
  degraded,
  failed,
  critical,
}