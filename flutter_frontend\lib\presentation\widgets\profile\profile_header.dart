import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/enhanced_profile_provider.dart';

/// Profile header widget showing user avatar and basic information
class ProfileHeader extends StatelessWidget {
  const ProfileHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthProvider, EnhancedProfileProvider>(
      builder: (context, authProvider, profileProvider, child) {
        final user = authProvider.currentUser;
        final profile = profileProvider.userProfile;
        
        return Column(
          children: [
            // Avatar
            _buildAvatar(user?.email),
            
            const SizedBox(height: 16),
            
            // Email
            Text(
              user?.email ?? 'No email',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                fontFamily: 'Inter',
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 8),
            
            // Plan badge
            _buildPlanBadge(profile?.plan ?? 'Unknown'),
            
            const SizedBox(height: 12),
            
            // Join date and days since joined
            if (profile != null) ...[
              Text(
                'Member since ${_formatDate(profile.dateJoined)}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontFamily: 'Inter',
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '${profileProvider.getDaysSinceJoined()} days with us',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontFamily: 'Inter',
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buildAvatar(String? email) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: [
            _getAvatarColor(email ?? ''),
            _getAvatarColor(email ?? '').withOpacity(0.7),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: Text(
          _getInitials(email ?? ''),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 32,
            fontWeight: FontWeight.bold,
            fontFamily: 'Inter',
          ),
        ),
      ),
    );
  }

  Widget _buildPlanBadge(String plan) {
    Color planColor;
    IconData planIcon;
    
    switch (plan.toLowerCase()) {
      case 'trial':
        planColor = Colors.grey;
        planIcon = Icons.schedule;
        break;
      case 'basic':
        planColor = Colors.blue;
        planIcon = Icons.star_border;
        break;
      case 'premium':
        planColor = Colors.green;
        planIcon = Icons.star;
        break;
      case 'enterprise':
        planColor = Colors.purple;
        planIcon = Icons.business;
        break;
      default:
        planColor = Colors.grey;
        planIcon = Icons.help_outline;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: planColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: planColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            planIcon,
            size: 16,
            color: planColor,
          ),
          const SizedBox(width: 6),
          Text(
            plan,
            style: TextStyle(
              color: planColor,
              fontSize: 12,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
            ),
          ),
        ],
      ),
    );
  }

  String _getInitials(String email) {
    if (email.isEmpty) return '?';
    
    final parts = email.split('@');
    if (parts.isEmpty) return '?';
    
    final name = parts[0];
    if (name.length >= 2) {
      return name.substring(0, 2).toUpperCase();
    } else if (name.length == 1) {
      return name.toUpperCase();
    } else {
      return '?';
    }
  }

  Color _getAvatarColor(String email) {
    // Generate a consistent color based on email
    final hash = email.hashCode;
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
      Colors.cyan,
    ];
    
    return colors[hash.abs() % colors.length];
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }
}