import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Bell, Volume2, Globe, Shield, CircleHelp as HelpCircle, LogOut, ChevronRight, Mic } from 'lucide-react-native';

export default function SettingsScreen() {
  const [notifications, setNotifications] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [autoCorrect, setAutoCorrect] = useState(true);
  const [voiceRecording, setVoiceRecording] = useState(true);

  const SettingItem = ({ 
    icon: Icon, 
    title, 
    subtitle, 
    onPress, 
    showSwitch = false, 
    switchValue = false, 
    onSwitchChange 
  }: {
    icon: any;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    showSwitch?: boolean;
    switchValue?: boolean;
    onSwitchChange?: (value: boolean) => void;
  }) => (
    <TouchableOpacity 
      style={styles.settingItem} 
      onPress={onPress}
      disabled={showSwitch}
    >
      <View style={styles.settingIcon}>
        <Icon size={24} color="#FFD700" />
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      {showSwitch ? (
        <Switch
          value={switchValue}
          onValueChange={onSwitchChange}
          trackColor={{ false: '#d1d5db', true: '#2563eb' }}
          thumbColor={switchValue ? '#fff' : '#ccc'}
        />
      ) : (
        <ChevronRight size={20} color="#9ca3af" />
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.gradient}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Einstellungen</Text>
          </View>

          {/* App Settings */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>App-Einstellungen</Text>
            
            <SettingItem
              icon={Bell}
              title="Benachrichtigungen"
              subtitle="Erinnerungen für tägliche Übungen"
              showSwitch={true}
              switchValue={notifications}
              onSwitchChange={setNotifications}
            />
            
            <SettingItem
              icon={Volume2}
              title="Ton"
              subtitle="Audiofeedback aktivieren"
              showSwitch={true}
              switchValue={soundEnabled}
              onSwitchChange={setSoundEnabled}
            />
            
            <SettingItem
              icon={Mic}
              title="Sprachaufnahme"
              subtitle="Automatische Audiokorrektur"
              showSwitch={true}
              switchValue={voiceRecording}
              onSwitchChange={setVoiceRecording}
            />
          </View>

          {/* Learning Settings */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Lerneinstellungen</Text>
            
            <SettingItem
              icon={Globe}
              title="Sprache"
              subtitle="Deutsch (Deutschland)"
              onPress={() => {}}
            />
            
            <SettingItem
              icon={Shield}
              title="Auto-Korrektur"
              subtitle="Sofortige Grammatikkorrektur"
              showSwitch={true}
              switchValue={autoCorrect}
              onSwitchChange={setAutoCorrect}
            />
          </View>

          {/* Support */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Support</Text>
            
            <SettingItem
              icon={HelpCircle}
              title="Hilfe & FAQ"
              subtitle="Häufig gestellte Fragen"
              onPress={() => {}}
            />
            
            <SettingItem
              icon={Shield}
              title="Datenschutz"
              subtitle="Datenschutzerklärung lesen"
              onPress={() => {}}
            />
          </View>

          {/* Account */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Konto</Text>
            
            <TouchableOpacity style={styles.logoutButton}>
              <LogOut size={24} color="#dc2626" />
              <Text style={styles.logoutText}>Abmelden</Text>
            </TouchableOpacity>
          </View>

          {/* App Info */}
          <View style={styles.appInfo}>
            <Text style={styles.appName}>DeutschKorrekt.ai</Text>
            <Text style={styles.appVersion}>Version 1.0.0</Text>
            <Text style={styles.appCopyright}>© 2024 DeutschKorrekt.ai</Text>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    backgroundColor: '#ffffff',
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#2563eb',
  },
  section: {
    margin: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  settingIcon: {
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef2f2',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  logoutText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#dc2626',
    marginLeft: 16,
  },
  appInfo: {
    alignItems: 'center',
    padding: 20,
    marginTop: 20,
  },
  appName: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  appVersion: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 8,
  },
  appCopyright: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9ca3af',
  },
});