import 'package:supabase_flutter/supabase_flutter.dart';
import 'environment.dart';

/// Supabase configuration for the DeutschKorrekt app
class SupabaseConfig {
  /// Initialize Supabase client with environment-specific configuration
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: Environment.supabaseUrl,
      anonKey: Environment.supabaseAnonKey,
      debug: Environment.enableDebugLogging,
    );
  }
  
  /// Get the Supabase client instance
  static SupabaseClient get client => Supabase.instance.client;
  
  /// Check if Supabase is initialized
  static bool get isInitialized {
    try {
      return Supabase.instance.client != null;
    } catch (e) {
      return false;
    }
  }
  
  /// Get current user
  static User? get currentUser => client.auth.currentUser;
  
  /// Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;
}