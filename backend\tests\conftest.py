"""
Pytest configuration and fixtures for TTS backend tests.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any
from google.cloud import texttospeech
from google.api_core import exceptions as gcp_exceptions

from services.google_tts_client import GoogleTTSClient
from services.tts_service import TTSService
from config.settings import Settings


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_settings():
    """Mock settings for testing."""
    settings = Mock(spec=Settings)
    settings.tts_voice_name = "de-DE-Chirp3-HD-Aoede"
    settings.tts_speech_speed = 0.9
    settings.tts_audio_format = "MP3"
    settings.tts_sample_rate = 24000
    settings.tts_max_text_length = 500
    return settings


@pytest.fixture
def mock_google_tts_response():
    """Mock Google TTS API response."""
    mock_response = Mock()
    mock_response.audio_content = b"fake_audio_data_mp3_content"
    return mock_response


@pytest.fixture
def mock_google_tts_client():
    """Mock Google Cloud TTS client."""
    mock_client = Mock(spec=texttospeech.TextToSpeechClient)
    mock_client.synthesize_speech = Mock(return_value=Mock())
    return mock_client


@pytest.fixture
def sample_tts_texts():
    """Sample texts for TTS testing."""
    return {
        "simple": "Hallo, das ist ein Test.",
        "long": "Das ist ein längerer Text für die Sprachsynthese. " * 10,
        "with_punctuation": "Hallo! Wie geht es dir? Das ist großartig...",
        "with_html": "Das ist <b>fett</b> und <i>kursiv</i> Text.",
        "with_special_chars": "Text mit Sonderzeichen: äöü ß € @ #",
        "empty": "",
        "whitespace_only": "   \n\t   ",
        "too_long": "x" * 1000,
        "german_sentence": "Die deutsche Sprache ist sehr interessant und komplex.",
        "numbers_and_symbols": "Der Preis beträgt 19,99€ für 100% Qualität!"
    }


@pytest.fixture
def expected_tts_results():
    """Expected results for TTS operations."""
    return {
        "success_response": {
            "success": True,
            "audio_data": b"fake_audio_data_mp3_content",
            "content_type": "audio/mpeg",
            "duration_seconds": 2.5,
            "processing_time": 0.5,
            "original_text": "Hallo, das ist ein Test.",
            "sanitized_text": "Hallo, das ist ein Test."
        },
        "validation_error": {
            "success": False,
            "error_message": "Text cannot be empty or only whitespace",
            "error_code": "INVALID_INPUT",
            "processing_time": 0.0
        },
        "quota_error": {
            "success": False,
            "error_message": "TTS generation failed: TTS API quota exceeded",
            "error_code": "QUOTA_EXCEEDED",
            "processing_time": 0.5
        }
    }


@pytest.fixture
def mock_google_tts_client_instance():
    """Mock GoogleTTSClient instance with all methods."""
    mock_client = Mock(spec=GoogleTTSClient)
    
    # Mock successful response
    async def mock_generate_speech(text: str) -> Dict[str, Any]:
        return {
            "audio_data": b"fake_audio_data_mp3_content",
            "content_type": "audio/mpeg",
            "duration_seconds": 2.5,
            "processing_time": 0.5
        }
    
    mock_client.generate_speech = AsyncMock(side_effect=mock_generate_speech)
    mock_client.get_client_info.return_value = {
        "voice_name": "de-DE-Chirp3-HD-Aoede",
        "language_code": "de-DE",
        "speech_speed": 0.9,
        "audio_format": "MP3",
        "sample_rate": 24000,
        "max_text_length": 500,
        "client_initialized": True
    }
    
    return mock_client


@pytest.fixture
def mock_tts_service_instance():
    """Mock TTSService instance with all methods."""
    mock_service = Mock(spec=TTSService)
    
    # Mock successful response
    async def mock_generate_speech(text: str, request_id: str = None) -> Dict[str, Any]:
        return {
            "success": True,
            "audio_data": b"fake_audio_data_mp3_content",
            "content_type": "audio/mpeg",
            "duration_seconds": 2.5,
            "processing_time": 0.5,
            "original_text": text,
            "sanitized_text": text
        }
    
    mock_service.generate_speech = AsyncMock(side_effect=mock_generate_speech)
    mock_service.get_service_info.return_value = {
        "service_name": "TTS Service",
        "max_text_length": 500,
        "google_tts_client": {},
        "features": []
    }
    
    async def mock_health_check() -> Dict[str, Any]:
        return {
            "healthy": True,
            "response_time": 0.5,
            "audio_size": 1024,
            "message": "TTS service is operational"
        }
    
    mock_service.health_check = AsyncMock(side_effect=mock_health_check)
    
    return mock_service


@pytest.fixture
def gcp_exceptions_fixture():
    """Fixture providing Google Cloud Platform exceptions for testing."""
    return {
        "invalid_argument": gcp_exceptions.InvalidArgument("Invalid argument"),
        "permission_denied": gcp_exceptions.PermissionDenied("Permission denied"),
        "resource_exhausted": gcp_exceptions.ResourceExhausted("Quota exceeded"),
        "service_unavailable": gcp_exceptions.ServiceUnavailable("Service unavailable"),
        "generic_error": Exception("Generic error")
    }


@pytest.fixture
def mock_correlation_id():
    """Mock correlation ID for request tracking."""
    return "test-correlation-123"


@pytest.fixture
def mock_fastapi_request():
    """Mock FastAPI request object."""
    mock_request = Mock()
    mock_request.headers = {"X-Correlation-ID": "test-correlation-123"}
    return mock_request