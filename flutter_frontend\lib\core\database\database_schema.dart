import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

/// Database schema management for DeutschKorrekt app
class DatabaseSchema {
  static final SupabaseClient _client = SupabaseConfig.client;
  
  /// SQL for creating users table
  static const String _createUsersTable = '''
    CREATE TABLE IF NOT EXISTS users (
      email TEXT PRIMARY KEY,
      plan TEXT NOT NULL DEFAULT 'Trial',
      date_joined TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      date_plan TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      max_credits INTEGER NOT NULL DEFAULT 20,
      current_credits INTEGER NOT NULL DEFAULT 20,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS check_max_credits_positive CHECK (max_credits >= 0);
    ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS check_current_credits_non_negative CHECK (current_credits >= 0);
    ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS check_current_credits_not_exceed_max CHECK (current_credits <= max_credits);
    ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS check_valid_plan CHECK (plan IN ('Trial', 'Basic', 'Premium', 'Enterprise'));
    
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_plan ON users(plan);
  ''';
  
  /// SQL for creating sessions table
  static const String _createSessionsTable = '''
    CREATE TABLE IF NOT EXISTS sessions (
      session_id SERIAL PRIMARY KEY,
      email TEXT NOT NULL REFERENCES users(email) ON DELETE CASCADE,
      message TEXT NOT NULL,
      response TEXT NOT NULL,
      datetime TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    CREATE INDEX IF NOT EXISTS idx_sessions_email ON sessions(email);
    CREATE INDEX IF NOT EXISTS idx_sessions_datetime ON sessions(datetime);
    CREATE INDEX IF NOT EXISTS idx_sessions_email_datetime ON sessions(email, datetime);
    
    ALTER TABLE sessions ADD CONSTRAINT IF NOT EXISTS check_message_not_empty CHECK (LENGTH(TRIM(message)) > 0);
    ALTER TABLE sessions ADD CONSTRAINT IF NOT EXISTS check_response_not_empty CHECK (LENGTH(TRIM(response)) > 0);
  ''';
  
  /// Check if database schema exists
  static Future<bool> schemaExists() async {
    try {
      // Check if users table exists
      final result = await _client
          .from('users')
          .select('email')
          .limit(1);
      
      return true; // If no exception, table exists
    } catch (e) {
      return false;
    }
  }
  
  /// Initialize database schema (Note: This would typically be done via Supabase dashboard)
  static Future<void> initializeSchema() async {
    try {
      // Note: In a real implementation, these would be run via Supabase migrations
      // This is here for documentation purposes
      print('Database schema should be initialized via Supabase dashboard');
      print('Run the following SQL commands in your Supabase SQL editor:');
      print('1. Create users table:');
      print(_createUsersTable);
      print('\n2. Create sessions table:');
      print(_createSessionsTable);
      print('\n3. Set up RLS policies via Supabase dashboard');
    } catch (e) {
      throw Exception('Failed to initialize database schema: $e');
    }
  }
  
  /// Verify database schema is properly set up
  static Future<bool> verifySchema() async {
    try {
      // Test users table access
      await _client.from('users').select('email').limit(1);
      
      // Test sessions table access
      await _client.from('sessions').select('session_id').limit(1);
      
      return true;
    } catch (e) {
      print('Schema verification failed: $e');
      return false;
    }
  }
}