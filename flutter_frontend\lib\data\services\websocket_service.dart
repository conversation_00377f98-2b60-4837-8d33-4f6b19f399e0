import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import '../models/websocket_message.dart';
import '../models/app_error.dart';
import '../../core/utils/websocket_utils.dart';
import 'websocket_connection_pool.dart';

/// WebSocket service for backend communication matching Expo version
class WebSocketService {
  static const String backendUrl = 'wss://deutschkorrekt-backend-645996191396.europe-west3.run.app/stt';
  static const int maxRetryAttempts = 3;
  static const Duration initialRetryDelay = Duration(seconds: 1);
  static const Duration maxRetryDelay = Duration(seconds: 10);
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration pingInterval = Duration(seconds: 30);
  
  WebSocketChannel? _channel;
  final WebSocketConnectionPool _connectionPool = WebSocketConnectionPool.instance;
  StreamController<WebSocketMessage>? _messageController;
  StreamController<AppError>? _errorController;
  StreamController<bool>? _connectionController;
  
  Timer? _pingTimer;
  Timer? _reconnectTimer;
  
  int _retryCount = 0;
  bool _isConnecting = false;
  bool _isDisposed = false;
  bool _shouldReconnect = true;
  
  /// Stream of incoming WebSocket messages
  Stream<WebSocketMessage> get messageStream => _messageController?.stream ?? const Stream.empty();
  
  /// Stream of connection errors
  Stream<AppError> get errorStream => _errorController?.stream ?? const Stream.empty();
  
  /// Stream of connection status changes
  Stream<bool> get connectionStream => _connectionController?.stream ?? const Stream.empty();
  
  /// Check if WebSocket is currently connected
  bool get isConnected => _channel != null && _channel!.closeCode == null;
  
  /// Check if currently connecting
  bool get isConnecting => _isConnecting;
  
  /// Initialize the WebSocket service
  WebSocketService() {
    _messageController = StreamController<WebSocketMessage>.broadcast();
    _errorController = StreamController<AppError>.broadcast();
    _connectionController = StreamController<bool>.broadcast();
  }
  
  /// Connect to the WebSocket server
  Future<void> connect() async {
    if (_isDisposed) {
      throw StateError('WebSocketService has been disposed');
    }
    
    if (isConnected || _isConnecting) {
      return;
    }
    
    _isConnecting = true;
    _shouldReconnect = true;
    
    try {
      await _attemptConnection();
    } catch (e) {
      _isConnecting = false;
      final error = AppError.websocketConnectionFailed(
        details: 'Failed to establish WebSocket connection: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
      
      if (_shouldReconnect && _retryCount < maxRetryAttempts) {
        _scheduleReconnect();
      }
    }
  }
  
  /// Attempt to establish WebSocket connection
  Future<void> _attemptConnection() async {
    try {
      _channel = WebSocketChannel.connect(
        Uri.parse(backendUrl),
        protocols: ['websocket'],
      );
      
      // Wait for connection to be established with timeout
      await _channel!.ready.timeout(connectionTimeout);
      
      _isConnecting = false;
      _retryCount = 0;
      _connectionController?.add(true);
      
      // Start listening to messages
      _listenToMessages();
      
      // Start ping timer to keep connection alive
      _startPingTimer();
      
      print('WebSocket connected successfully');
      
    } catch (e) {
      _isConnecting = false;
      await _cleanup();
      rethrow;
    }
  }
  
  /// Listen to incoming WebSocket messages
  void _listenToMessages() {
    _channel?.stream.listen(
      (data) {
        try {
          if (data is String) {
            final jsonData = json.decode(data) as Map<String, dynamic>;
            final message = WebSocketMessage.fromJson(jsonData);
            _messageController?.add(message);
          } else if (data is List<int>) {
            // Handle binary data if needed
            print('Received binary data: ${data.length} bytes');
          }
        } catch (e) {
          final error = AppError.backendError(
            details: 'Failed to parse WebSocket message: $e',
            originalException: e is Exception ? e : Exception(e.toString()),
          );
          _errorController?.add(error);
        }
      },
      onError: (error) {
        print('WebSocket error: $error');
        _connectionController?.add(false);
        
        final appError = AppError.websocketConnectionFailed(
          details: 'WebSocket connection error: $error',
          originalException: error is Exception ? error : Exception(error.toString()),
        );
        _errorController?.add(appError);
        
        if (_shouldReconnect && _retryCount < maxRetryAttempts) {
          _scheduleReconnect();
        }
      },
      onDone: () {
        print('WebSocket connection closed');
        _connectionController?.add(false);
        _cleanup();
        
        if (_shouldReconnect && _retryCount < maxRetryAttempts) {
          _scheduleReconnect();
        }
      },
    );
  }
  
  /// Send audio data to the server
  void sendAudioData(Uint8List audioData) {
    if (!isConnected) {
      final error = AppError.websocketConnectionFailed(
        details: 'Cannot send audio data: WebSocket not connected',
      );
      _errorController?.add(error);
      return;
    }
    
    try {
      _channel?.sink.add(audioData);
    } catch (e) {
      final error = AppError.backendError(
        details: 'Failed to send audio data: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
    }
  }
  
  /// Send text message to the server
  void sendMessage(Map<String, dynamic> message) {
    if (!isConnected) {
      final error = AppError.websocketConnectionFailed(
        details: 'Cannot send message: WebSocket not connected',
      );
      _errorController?.add(error);
      return;
    }
    
    try {
      final jsonString = json.encode(message);
      _channel?.sink.add(jsonString);
    } catch (e) {
      final error = AppError.backendError(
        details: 'Failed to send message: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
    }
  }
  
  /// Start session with the server
  void startSession() {
    sendMessage({
      'type': 'start_session',
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  /// End session with the server
  void endSession() {
    sendMessage({
      'type': 'end_session',
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  /// Start ping timer to keep connection alive
  void _startPingTimer() {
    _pingTimer?.cancel();
    _pingTimer = Timer.periodic(pingInterval, (timer) {
      if (isConnected) {
        sendMessage({
          'type': 'ping',
          'timestamp': DateTime.now().toIso8601String(),
        });
      } else {
        timer.cancel();
      }
    });
  }
  
  /// Schedule reconnection attempt with exponential backoff
  void _scheduleReconnect() {
    if (_isDisposed || !_shouldReconnect) return;
    
    _retryCount++;
    final delay = Duration(
      milliseconds: (initialRetryDelay.inMilliseconds * 
                    (1 << (_retryCount - 1))).clamp(
        initialRetryDelay.inMilliseconds,
        maxRetryDelay.inMilliseconds,
      ),
    );
    
    print('Scheduling reconnect attempt $_retryCount in ${delay.inSeconds}s');
    
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(delay, () {
      if (!_isDisposed && _shouldReconnect) {
        connect();
      }
    });
  }
  
  /// Disconnect from the WebSocket server
  Future<void> disconnect() async {
    _shouldReconnect = false;
    await _cleanup();
    _connectionController?.add(false);
  }
  
  /// Clean up resources
  Future<void> _cleanup() async {
    _pingTimer?.cancel();
    _reconnectTimer?.cancel();
    
    if (_channel != null) {
      try {
        await _channel!.sink.close(status.normalClosure);
      } catch (e) {
        print('Error closing WebSocket: $e');
      }
      _channel = null;
    }
  }
  
  /// Reset retry count (useful after successful operations)
  void resetRetryCount() {
    _retryCount = 0;
  }
  
  /// Force reconnection
  Future<void> reconnect() async {
    await disconnect();
    _retryCount = 0;
    await connect();
  }
  
  /// Dispose the service and clean up all resources
  Future<void> dispose() async {
    if (_isDisposed) return;
    
    _isDisposed = true;
    _shouldReconnect = false;
    
    await _cleanup();
    
    await _messageController?.close();
    await _errorController?.close();
    await _connectionController?.close();
    
    _messageController = null;
    _errorController = null;
    _connectionController = null;
  }
  
  /// Get connection statistics
  Map<String, dynamic> getConnectionStats() {
    return {
      'isConnected': isConnected,
      'isConnecting': isConnecting,
      'retryCount': _retryCount,
      'maxRetryAttempts': maxRetryAttempts,
      'backendUrl': backendUrl,
    };
  }
}