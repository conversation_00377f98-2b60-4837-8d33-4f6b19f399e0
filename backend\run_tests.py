#!/usr/bin/env python3
"""
Test runner for TTS backend tests.
This script runs the comprehensive test suite for the TTS functionality.
"""

import sys
import os
import subprocess
import argparse

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_tests(test_type="all", verbose=False):
    """
    Run TTS tests based on the specified type.
    
    Args:
        test_type (str): Type of tests to run ("unit", "api", "integration", "all")
        verbose (bool): Whether to run tests in verbose mode
    """
    
    # Base pytest command
    cmd = [sys.executable, "-m", "pytest"]
    
    if verbose:
        cmd.append("-v")
    
    # Add coverage if available
    try:
        import coverage
        cmd.extend(["--cov=services", "--cov-report=term-missing"])
    except ImportError:
        pass
    
    # Determine which tests to run
    if test_type == "unit":
        cmd.extend([
            "tests/test_google_tts_client.py",
            "tests/test_tts_service.py"
        ])
    elif test_type == "api":
        cmd.append("tests/test_tts_api.py")
    elif test_type == "integration":
        cmd.extend([
            "tests/test_tts_integration.py",
            "-m", "integration"
        ])
    elif test_type == "all":
        cmd.extend([
            "tests/test_google_tts_client.py",
            "tests/test_tts_service.py",
            "tests/test_tts_api.py"
        ])
    else:
        print(f"Unknown test type: {test_type}")
        return 1
    
    print(f"Running {test_type} tests...")
    print(f"Command: {' '.join(cmd)}")
    
    # Run the tests
    try:
        result = subprocess.run(cmd, cwd=os.path.dirname(os.path.abspath(__file__)))
        return result.returncode
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return 1
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1

def main():
    """Main function to parse arguments and run tests."""
    parser = argparse.ArgumentParser(description="Run TTS backend tests")
    parser.add_argument(
        "--type", 
        choices=["unit", "api", "integration", "all"],
        default="all",
        help="Type of tests to run (default: all)"
    )
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Run tests in verbose mode"
    )
    
    args = parser.parse_args()
    
    # Check if required dependencies are available
    try:
        import pytest
        import google.cloud.texttospeech
        import fastapi
    except ImportError as e:
        print(f"Missing required dependency: {e}")
        print("Please install required packages:")
        print("pip install pytest pytest-asyncio google-cloud-texttospeech fastapi httpx")
        return 1
    
    return run_tests(args.type, args.verbose)

if __name__ == "__main__":
    sys.exit(main())