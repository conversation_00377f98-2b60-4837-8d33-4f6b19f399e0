/// Utility class for extracting sentences from Groq responses
/// 
/// This class provides functionality to extract the first sentence from text
/// contained within curly braces in Groq responses, as required for TTS functionality.
class SentenceExtractor {
  /// Regular expression to match text within curly braces
  /// This regex handles nested braces by matching everything between the first { and last }
  static final RegExp _curlyBraceRegex = RegExp(r'\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}');
  
  /// Public accessor for the curly brace regex pattern
  /// Used for consistent text processing across the application
  static RegExp get curlyBraceRegex => _curlyBraceRegex;
  
  /// Regular expression to match sentence endings
  static final RegExp _sentenceEndRegex = RegExp(r'[.!?]+');
  
  /// Extracts all content from text within curly braces
  /// 
  /// This method searches for text within curly braces and returns all
  /// the content found within the first pair of braces.
  /// 
  /// Parameters:
  /// - [text]: The input text to search for curly braces content
  /// 
  /// Returns:
  /// - String: All content within braces if found and valid
  /// - null: If no curly braces found or content is empty
  /// 
  /// Example:
  /// ```dart
  /// String response = "Here is the answer: {Hello world! This is a test.}";
  /// String? content = SentenceExtractor.extractFirstSentenceFromBraces(response);
  /// // Returns: "Hello world! This is a test."
  /// ```
  static String? extractFirstSentenceFromBraces(String text) {
    if (text.isEmpty) {
      return null;
    }

    // Find the first opening brace
    final openIndex = text.indexOf('{');
    if (openIndex == -1) {
      return null;
    }
    
    // Find the matching closing brace, handling nested braces
    int braceCount = 0;
    int closeIndex = -1;
    
    for (int i = openIndex; i < text.length; i++) {
      if (text[i] == '{') {
        braceCount++;
      } else if (text[i] == '}') {
        braceCount--;
        if (braceCount == 0) {
          closeIndex = i;
          break;
        }
      }
    }
    
    if (closeIndex == -1) {
      return null;
    }

    // Extract ALL content between braces
    final bracesContent = text.substring(openIndex + 1, closeIndex);

    if (bracesContent.trim().isEmpty) {
      return null;
    }

    // Return all content within braces, not just first sentence
    return bracesContent.trim();
  }
  
  /// Extracts the first sentence from the given text
  /// 
  /// A sentence is considered to end with punctuation marks (., !, or ?)
  /// followed by optional whitespace.
  /// 
  /// Parameters:
  /// - [text]: The text to extract the first sentence from
  /// 
  /// Returns:
  /// - String: The first sentence if found and valid
  /// - null: If no sentence found or text is empty
  static String? _extractFirstSentence(String text) {
    if (text.isEmpty) {
      return null;
    }
    
    // Find the first sentence ending
    final match = _sentenceEndRegex.firstMatch(text);
    
    if (match == null) {
      // No sentence ending found, check if the entire text could be a sentence
      // (sometimes responses might not have proper punctuation)
      final trimmed = text.trim();
      return trimmed.isNotEmpty ? trimmed : null;
    }
    
    // Extract text up to and including the sentence ending
    final sentenceEnd = match.end;
    final sentence = text.substring(0, sentenceEnd).trim();
    
    return sentence.isNotEmpty ? sentence : null;
  }
  
  /// Validates if the extracted sentence is suitable for TTS
  /// 
  /// This method performs additional validation to ensure the sentence
  /// is appropriate for text-to-speech conversion.
  /// 
  /// Parameters:
  /// - [sentence]: The sentence to validate
  /// 
  /// Returns:
  /// - bool: true if the sentence is valid for TTS, false otherwise
  static bool isValidForTTS(String? sentence) {
    if (sentence == null || sentence.trim().isEmpty) {
      return false;
    }
    
    final trimmed = sentence.trim();
    
    // Check minimum length (at least 2 characters)
    if (trimmed.length < 2) {
      return false;
    }
    
    // Check if it contains at least one letter (not just punctuation/numbers)
    if (!RegExp(r'[a-zA-ZäöüßÄÖÜ]').hasMatch(trimmed)) {
      return false;
    }
    
    // Check maximum reasonable length for TTS (500 characters as per backend limit)
    if (trimmed.length > 500) {
      return false;
    }
    
    return true;
  }
  
  /// Formats text for display by replacing curly brace sections with all content
  /// 
  /// This method replaces each curly brace section with all the content
  /// from within that section. This provides clean display text without braces.
  /// 
  /// Parameters:
  /// - [originalText]: The original text containing curly braces
  /// 
  /// Returns:
  /// - String: The formatted text with brace sections replaced by all content
  /// 
  /// Example:
  /// ```dart
  /// String original = "Answer: {Hello world! This is a test.} More text.";
  /// String display = SentenceExtractor.formatForDisplay(original);
  /// // Returns: "Answer: Hello world! This is a test. More text."
  /// ```
  static String formatForDisplay(String originalText) {
    if (originalText.isEmpty) {
      return originalText;
    }
    
    // Replace curly braces with all content inside them
    return originalText.replaceAllMapped(
      _curlyBraceRegex,
      (match) {
        final content = match.group(1);
        return content?.trim() ?? '';
      },
    ).trim();
  }
}