import 'package:flutter/foundation.dart';
import '../../data/repositories/user_repository.dart';
import '../../data/models/user_profile.dart';

/// Enhanced profile provider with database integration
class EnhancedProfileProvider extends ChangeNotifier {
  final UserRepository _userRepository = UserRepository();
  
  UserProfile? _userProfile;
  bool _isLoading = false;
  String? _errorMessage;
  bool _isInitialized = false;

  // Getters
  UserProfile? get userProfile => _userProfile;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isInitialized => _isInitialized;
  
  // Convenience getters
  String? get email => _userProfile?.email;
  String get plan => _userProfile?.plan ?? 'Trial';
  int get currentCredits => _userProfile?.currentCredits ?? 0;
  int get maxCredits => _userProfile?.maxCredits ?? 20;
  bool get hasCreditsAvailable => _userProfile?.hasCreditsAvailable ?? false;
  DateTime? get nextRefreshDate => _userProfile?.nextRefreshDate;
  bool get needsRefresh => _userProfile?.needsRefresh ?? false;
  double get creditsUsagePercentage => _userProfile?.creditsUsagePercentage ?? 0.0;
  double get remainingCreditsPercentage => _userProfile?.remainingCreditsPercentage ?? 0.0;

  /// Initialize the profile provider
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _isInitialized = true;
    notifyListeners();
  }

  /// Load user profile from database
  Future<void> loadUserProfile(String email) async {
    _setLoading(true);
    _clearError();

    try {
      final profile = await _userRepository.getUserProfile(email);
      
      if (profile != null) {
        _userProfile = profile;
        
        // Check if credits need refresh
        if (profile.needsRefresh) {
          await refreshCreditsIfNeeded();
        }
      } else {
        // Create new profile if it doesn't exist
        await createUserProfile(email);
      }
    } catch (e) {
      _setError('Failed to load user profile: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Create a new user profile
  Future<void> createUserProfile(String email) async {
    _setLoading(true);
    _clearError();

    try {
      final profile = await _userRepository.createUserProfile(email);
      _userProfile = profile;
    } catch (e) {
      _setError('Failed to create user profile: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update user profile
  Future<void> updateProfile(UserProfile profile) async {
    _setLoading(true);
    _clearError();

    try {
      final updatedProfile = await _userRepository.updateUserProfile(profile);
      _userProfile = updatedProfile;
    } catch (e) {
      _setError('Failed to update user profile: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Consume one credit
  Future<bool> consumeCredit() async {
    if (_userProfile == null) {
      _setError('No user profile loaded');
      return false;
    }

    if (!_userProfile!.hasCreditsAvailable) {
      _setError('No credits available');
      return false;
    }

    _setLoading(true);
    _clearError();

    try {
      final updatedProfile = await _userRepository.consumeCredit(_userProfile!.email);
      _userProfile = updatedProfile;
      return true;
    } catch (e) {
      _setError('Failed to consume credit: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh credits if needed
  Future<void> refreshCreditsIfNeeded() async {
    if (_userProfile == null || !_userProfile!.needsRefresh) {
      return;
    }

    _setLoading(true);
    _clearError();

    try {
      final updatedProfile = await _userRepository.refreshCredits(_userProfile!.email);
      _userProfile = updatedProfile;
    } catch (e) {
      _setError('Failed to refresh credits: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update user plan
  Future<void> updatePlan(String newPlan, int newMaxCredits) async {
    if (_userProfile == null) {
      _setError('No user profile loaded');
      return;
    }

    _setLoading(true);
    _clearError();

    try {
      final updatedProfile = _userProfile!.copyWith(
        plan: newPlan,
        maxCredits: newMaxCredits,
        currentCredits: newMaxCredits, // Reset credits when changing plan
        datePlan: DateTime.now(), // Reset plan date
      );

      await updateProfile(updatedProfile);
    } catch (e) {
      _setError('Failed to update plan: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get user statistics
  Future<Map<String, dynamic>?> getUserStats() async {
    if (_userProfile == null) {
      _setError('No user profile loaded');
      return null;
    }

    _setLoading(true);
    _clearError();

    try {
      final stats = await _userRepository.getUserStats(_userProfile!.email);
      return stats;
    } catch (e) {
      _setError('Failed to get user stats: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Check if user can make a request (has credits)
  bool canMakeRequest() {
    return _userProfile?.hasCreditsAvailable ?? false;
  }

  /// Get credits status message
  String getCreditsStatusMessage() {
    if (_userProfile == null) {
      return 'Profile not loaded';
    }

    if (_userProfile!.currentCredits == 0) {
      final nextRefresh = _userProfile!.nextRefreshDate;
      final daysUntilRefresh = nextRefresh.difference(DateTime.now()).inDays;
      
      if (daysUntilRefresh <= 0) {
        return 'Credits refresh today! Please refresh your profile.';
      } else if (daysUntilRefresh == 1) {
        return 'Credits refresh tomorrow';
      } else {
        return 'Credits refresh in $daysUntilRefresh days';
      }
    }

    return '${_userProfile!.currentCredits} of ${_userProfile!.maxCredits} credits remaining';
  }

  /// Get formatted next refresh date
  String getFormattedRefreshDate() {
    if (_userProfile == null) return 'Unknown';
    
    final nextRefresh = _userProfile!.nextRefreshDate;
    final now = DateTime.now();
    final difference = nextRefresh.difference(now);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Tomorrow';
    } else if (difference.inDays < 7) {
      return 'In ${difference.inDays} days';
    } else {
      return '${nextRefresh.day}/${nextRefresh.month}/${nextRefresh.year}';
    }
  }

  /// Get days since joined
  int getDaysSinceJoined() {
    if (_userProfile == null) return 0;
    return DateTime.now().difference(_userProfile!.dateJoined).inDays;
  }

  /// Get plan color based on plan type
  Color getPlanColor() {
    switch (_userProfile?.plan.toLowerCase()) {
      case 'trial':
        return const Color(0xFF9E9E9E); // Grey
      case 'basic':
        return const Color(0xFF2196F3); // Blue
      case 'premium':
        return const Color(0xFF4CAF50); // Green
      case 'enterprise':
        return const Color(0xFF9C27B0); // Purple
      default:
        return const Color(0xFF9E9E9E); // Grey
    }
  }

  /// Clear user profile (for logout)
  void clearProfile() {
    _userProfile = null;
    _clearError();
    notifyListeners();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Refresh profile data
  Future<void> refresh() async {
    if (_userProfile?.email != null) {
      await loadUserProfile(_userProfile!.email);
    }
  }

  /// Get profile summary for display
  Map<String, dynamic> getProfileSummary() {
    if (_userProfile == null) {
      return {
        'email': 'Not loaded',
        'plan': 'Unknown',
        'credits': '0/0',
        'nextRefresh': 'Unknown',
        'daysJoined': 0,
      };
    }

    return {
      'email': _userProfile!.email,
      'plan': _userProfile!.plan,
      'credits': '${_userProfile!.currentCredits}/${_userProfile!.maxCredits}',
      'nextRefresh': getFormattedRefreshDate(),
      'daysJoined': getDaysSinceJoined(),
      'creditsPercentage': _userProfile!.remainingCreditsPercentage,
      'needsRefresh': _userProfile!.needsRefresh,
    };
  }
}