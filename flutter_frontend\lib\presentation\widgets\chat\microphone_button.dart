import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/gradients.dart';

/// Microphone button with German flag design matching Expo version
class MicrophoneButton extends StatefulWidget {
  final VoidCallback onPressed;
  final bool isStreaming;
  final bool isConnecting;
  final bool isDisabled;
  final double size;
  final bool enableHapticFeedback;
  
  const MicrophoneButton({
    super.key,
    required this.onPressed,
    this.isStreaming = false,
    this.isConnecting = false,
    this.isDisabled = false,
    this.size = 80.0,
    this.enableHapticFeedback = true,
  });

  @override
  State<MicrophoneButton> createState() => _MicrophoneButtonState();
}

class _MicrophoneButtonState extends State<MicrophoneButton>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  
  bool _isPressed = false;
  
  @override
  void initState() {
    super.initState();
    
    // Scale animation for press feedback and streaming state
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.15, // Scale to 1.15 when streaming (matching Expo version)
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
    
    // Pulse animation for connecting state
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    // Rotation animation for loading state
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));
  }
  
  @override
  void didUpdateWidget(MicrophoneButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Handle streaming state changes
    if (widget.isStreaming != oldWidget.isStreaming) {
      if (widget.isStreaming) {
        _scaleController.forward();
      } else {
        _scaleController.reverse();
      }
    }
    
    // Handle connecting state changes
    if (widget.isConnecting != oldWidget.isConnecting) {
      if (widget.isConnecting) {
        _pulseController.repeat(reverse: true);
        _rotationController.repeat();
      } else {
        _pulseController.stop();
        _rotationController.stop();
      }
    }
  }
  
  @override
  void dispose() {
    _scaleController.dispose();
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }
  
  void _handleTapDown(TapDownDetails details) {
    if (widget.isDisabled) return;
    
    setState(() {
      _isPressed = true;
    });
    
    if (widget.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }
  }
  
  void _handleTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });
  }
  
  void _handleTapCancel() {
    setState(() {
      _isPressed = false;
    });
  }
  
  void _handleTap() {
    if (widget.isDisabled) return;
    
    if (widget.enableHapticFeedback) {
      HapticFeedback.mediumImpact();
    }
    
    widget.onPressed();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _scaleAnimation,
        _pulseAnimation,
        _rotationAnimation,
      ]),
      builder: (context, child) {
        double scale = _scaleAnimation.value;
        
        // Apply pulse animation when connecting
        if (widget.isConnecting) {
          scale *= _pulseAnimation.value;
        }
        
        // Apply press animation
        if (_isPressed) {
          scale *= 0.95;
        }
        
        return Transform.scale(
          scale: scale,
          child: Transform.rotate(
            angle: widget.isConnecting ? _rotationAnimation.value * 2 * 3.14159 : 0,
            child: GestureDetector(
              onTapDown: _handleTapDown,
              onTapUp: _handleTapUp,
              onTapCancel: _handleTapCancel,
              onTap: _handleTap,
              child: Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadowMedium,
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                    if (widget.isStreaming)
                      BoxShadow(
                        color: AppColors.errorRed.withOpacity(0.3),
                        blurRadius: 16,
                        offset: const Offset(0, 0),
                      ),
                  ],
                ),
                child: Stack(
                  children: [
                    // German flag background
                    _buildGermanFlagBackground(),
                    
                    // Microphone icon overlay
                    _buildMicrophoneIcon(),
                    
                    // Disabled overlay
                    if (widget.isDisabled)
                      _buildDisabledOverlay(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  
  /// Build German flag background with horizontal stripes
  Widget _buildGermanFlagBackground() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.white.withOpacity(0.2),
          width: 2,
        ),
      ),
      child: ClipOval(
        child: Column(
          children: [
            // Black stripe (top)
            Expanded(
              child: Container(
                width: double.infinity,
                color: AppColors.germanBlack,
              ),
            ),
            // Red stripe (middle)
            Expanded(
              child: Container(
                width: double.infinity,
                color: AppColors.germanRed,
              ),
            ),
            // Yellow stripe (bottom)
            Expanded(
              child: Container(
                width: double.infinity,
                color: AppColors.germanYellow,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build microphone icon overlay
  Widget _buildMicrophoneIcon() {
    Color iconColor;
    
    if (widget.isDisabled) {
      iconColor = AppColors.disabledText;
    } else if (widget.isStreaming) {
      iconColor = AppColors.white;
    } else if (widget.isConnecting) {
      iconColor = AppColors.white.withOpacity(0.8);
    } else {
      iconColor = AppColors.white;
    }
    
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: widget.isStreaming 
            ? AppGradients.microphoneActiveGradient
            : null,
      ),
      child: Center(
        child: Icon(
          Icons.mic,
          size: widget.size * 0.4, // 40% of button size
          color: iconColor,
        ),
      ),
    );
  }
  
  /// Build disabled overlay
  Widget _buildDisabledOverlay() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.black.withOpacity(0.5),
      ),
      child: Center(
        child: Icon(
          Icons.mic_off,
          size: widget.size * 0.3,
          color: AppColors.disabledText,
        ),
      ),
    );
  }
}

/// Microphone button with additional status indicators
class MicrophoneButtonWithStatus extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isStreaming;
  final bool isConnecting;
  final bool isDisabled;
  final String? statusText;
  final double size;
  final bool enableHapticFeedback;
  
  const MicrophoneButtonWithStatus({
    super.key,
    required this.onPressed,
    this.isStreaming = false,
    this.isConnecting = false,
    this.isDisabled = false,
    this.statusText,
    this.size = 80.0,
    this.enableHapticFeedback = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        MicrophoneButton(
          onPressed: onPressed,
          isStreaming: isStreaming,
          isConnecting: isConnecting,
          isDisabled: isDisabled,
          size: size,
          enableHapticFeedback: enableHapticFeedback,
        ),
        
        if (statusText != null) ...[
          const SizedBox(height: 12),
          Text(
            statusText!,
            style: TextStyle(
              fontSize: 14,
              color: _getStatusTextColor(),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
  
  Color _getStatusTextColor() {
    if (isDisabled) {
      return AppColors.disabledText;
    } else if (isStreaming) {
      return AppColors.errorRed;
    } else if (isConnecting) {
      return AppColors.infoBlue;
    } else {
      return AppColors.lightText;
    }
  }
}

/// Compact microphone button for smaller spaces
class CompactMicrophoneButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isStreaming;
  final bool isConnecting;
  final bool isDisabled;
  final bool enableHapticFeedback;
  
  const CompactMicrophoneButton({
    super.key,
    required this.onPressed,
    this.isStreaming = false,
    this.isConnecting = false,
    this.isDisabled = false,
    this.enableHapticFeedback = true,
  });

  @override
  Widget build(BuildContext context) {
    return MicrophoneButton(
      onPressed: onPressed,
      isStreaming: isStreaming,
      isConnecting: isConnecting,
      isDisabled: isDisabled,
      size: 48.0, // Smaller size for compact version
      enableHapticFeedback: enableHapticFeedback,
    );
  }
}

/// Large microphone button for prominent placement
class LargeMicrophoneButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isStreaming;
  final bool isConnecting;
  final bool isDisabled;
  final bool enableHapticFeedback;
  
  const LargeMicrophoneButton({
    super.key,
    required this.onPressed,
    this.isStreaming = false,
    this.isConnecting = false,
    this.isDisabled = false,
    this.enableHapticFeedback = true,
  });

  @override
  Widget build(BuildContext context) {
    return MicrophoneButton(
      onPressed: onPressed,
      isStreaming: isStreaming,
      isConnecting: isConnecting,
      isDisabled: isDisabled,
      size: 120.0, // Larger size for prominent placement
      enableHapticFeedback: enableHapticFeedback,
    );
  }
}