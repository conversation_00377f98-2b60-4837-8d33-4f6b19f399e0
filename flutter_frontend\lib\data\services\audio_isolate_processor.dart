import 'dart:async';
import 'dart:isolate';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';

/// Audio processing in isolates for better performance
/// Handles PCM16 audio data processing without blocking the main thread
class AudioIsolateProcessor {
  static final AudioIsolateProcessor _instance = AudioIsolateProcessor._();
  static AudioIsolateProcessor get instance => _instance;
  
  Isolate? _isolate;
  SendPort? _sendPort;
  ReceivePort? _receivePort;
  StreamController<Uint8List>? _processedAudioController;
  StreamController<String>? _errorController;
  
  bool _isInitialized = false;
  bool _isDisposed = false;
  
  AudioIsolateProcessor._();
  
  /// Stream of processed audio data
  Stream<Uint8List> get processedAudioStream => 
      _processedAudioController?.stream ?? const Stream.empty();
  
  /// Stream of processing errors
  Stream<String> get errorStream => 
      _errorController?.stream ?? const Stream.empty();
  
  /// Whether the processor is initialized
  bool get isInitialized => _isInitialized;
  
  /// Initialize the audio isolate processor
  Future<void> initialize() async {
    if (_isDisposed) {
      throw StateError('AudioIsolateProcessor has been disposed');
    }
    
    if (_isInitialized) return;
    
    try {
      _processedAudioController = StreamController<Uint8List>.broadcast();
      _errorController = StreamController<String>.broadcast();
      
      // Create receive port for communication with isolate
      _receivePort = ReceivePort();
      
      // Spawn the audio processing isolate
      _isolate = await Isolate.spawn(
        _audioProcessingIsolateEntryPoint,
        _receivePort!.sendPort,
        debugName: 'AudioProcessingIsolate',
      );
      
      // Set up communication with isolate
      await _setupIsolateCommunication();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('AudioIsolateProcessor initialized successfully');
      }
      
    } catch (e) {
      _errorController?.add('Failed to initialize audio isolate processor: $e');
      rethrow;
    }
  }
  
  /// Set up communication with the audio processing isolate
  Future<void> _setupIsolateCommunication() async {
    if (_receivePort == null) return;
    
    final completer = Completer<void>();
    
    _receivePort!.listen((message) {
      if (message is SendPort) {
        // Isolate is ready and sent us its SendPort
        _sendPort = message;
        completer.complete();
      } else if (message is Map<String, dynamic>) {
        _handleIsolateMessage(message);
      }
    });
    
    // Wait for isolate to be ready
    await completer.future.timeout(
      const Duration(seconds: 5),
      onTimeout: () {
        throw TimeoutException('Audio isolate initialization timeout');
      },
    );
  }
  
  /// Handle messages from the audio processing isolate
  void _handleIsolateMessage(Map<String, dynamic> message) {
    final type = message['type'] as String?;
    
    switch (type) {
      case 'processed_audio':
        final audioData = message['data'] as Uint8List?;
        if (audioData != null) {
          _processedAudioController?.add(audioData);
        }
        break;
        
      case 'error':
        final error = message['error'] as String?;
        if (error != null) {
          _errorController?.add(error);
        }
        break;
        
      case 'debug':
        if (kDebugMode) {
          final debugMessage = message['message'] as String?;
          print('Audio Isolate: $debugMessage');
        }
        break;
        
      default:
        if (kDebugMode) {
          print('Unknown message type from audio isolate: $type');
        }
    }
  }
  
  /// Process audio data in the isolate
  Future<void> processAudioData(Uint8List audioData) async {
    if (!_isInitialized || _sendPort == null) {
      _errorController?.add('Audio isolate processor not initialized');
      return;
    }
    
    try {
      _sendPort!.send({
        'type': 'process_audio',
        'data': audioData,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      _errorController?.add('Failed to send audio data to isolate: $e');
    }
  }
  
  /// Configure audio processing parameters
  Future<void> configureProcessing({
    double? gainBoost,
    bool? enableNoiseReduction,
    int? sampleRate,
  }) async {
    if (!_isInitialized || _sendPort == null) {
      _errorController?.add('Audio isolate processor not initialized');
      return;
    }
    
    try {
      _sendPort!.send({
        'type': 'configure',
        'gain_boost': gainBoost ?? 2.0,
        'enable_noise_reduction': enableNoiseReduction ?? true,
        'sample_rate': sampleRate ?? 16000,
      });
    } catch (e) {
      _errorController?.add('Failed to configure audio processing: $e');
    }
  }
  
  /// Get processing statistics from the isolate
  Future<Map<String, dynamic>?> getProcessingStats() async {
    if (!_isInitialized || _sendPort == null) {
      return null;
    }
    
    try {
      final completer = Completer<Map<String, dynamic>>();
      
      // Set up temporary listener for stats response
      late StreamSubscription subscription;
      subscription = _receivePort!.listen((message) {
        if (message is Map<String, dynamic> && message['type'] == 'stats') {
          subscription.cancel();
          completer.complete(message['data'] as Map<String, dynamic>);
        }
      });
      
      _sendPort!.send({'type': 'get_stats'});
      
      return await completer.future.timeout(
        const Duration(seconds: 2),
        onTimeout: () {
          subscription.cancel();
          return null;
        },
      );
    } catch (e) {
      _errorController?.add('Failed to get processing stats: $e');
      return null;
    }
  }
  
  /// Dispose of the processor and clean up resources
  Future<void> dispose() async {
    if (_isDisposed) return;
    
    _isDisposed = true;
    
    // Send shutdown message to isolate
    if (_sendPort != null) {
      try {
        _sendPort!.send({'type': 'shutdown'});
      } catch (e) {
        if (kDebugMode) {
          print('Error sending shutdown message to isolate: $e');
        }
      }
    }
    
    // Kill the isolate
    _isolate?.kill(priority: Isolate.immediate);
    _isolate = null;
    
    // Close receive port
    _receivePort?.close();
    _receivePort = null;
    _sendPort = null;
    
    // Close streams
    await _processedAudioController?.close();
    await _errorController?.close();
    
    _isInitialized = false;
    
    if (kDebugMode) {
      print('AudioIsolateProcessor disposed');
    }
  }
}

/// Entry point for the audio processing isolate
void _audioProcessingIsolateEntryPoint(SendPort mainSendPort) {
  final receivePort = ReceivePort();
  
  // Send our SendPort back to the main isolate
  mainSendPort.send(receivePort.sendPort);
  
  // Audio processing state
  double gainBoost = 2.0;
  bool enableNoiseReduction = true;
  int sampleRate = 16000;
  int processedChunks = 0;
  int totalBytes = 0;
  
  // Listen for messages from main isolate
  receivePort.listen((message) {
    if (message is Map<String, dynamic>) {
      final type = message['type'] as String?;
      
      switch (type) {
        case 'process_audio':
          final audioData = message['data'] as Uint8List?;
          if (audioData != null) {
            try {
              final processedData = _processAudioChunk(
                audioData,
                gainBoost: gainBoost,
                enableNoiseReduction: enableNoiseReduction,
                sampleRate: sampleRate,
              );
              
              processedChunks++;
              totalBytes += audioData.length;
              
              mainSendPort.send({
                'type': 'processed_audio',
                'data': processedData,
              });
            } catch (e) {
              mainSendPort.send({
                'type': 'error',
                'error': 'Audio processing error: $e',
              });
            }
          }
          break;
          
        case 'configure':
          gainBoost = message['gain_boost'] as double? ?? gainBoost;
          enableNoiseReduction = message['enable_noise_reduction'] as bool? ?? enableNoiseReduction;
          sampleRate = message['sample_rate'] as int? ?? sampleRate;
          
          mainSendPort.send({
            'type': 'debug',
            'message': 'Configuration updated: gain=$gainBoost, noise_reduction=$enableNoiseReduction, sample_rate=$sampleRate',
          });
          break;
          
        case 'get_stats':
          mainSendPort.send({
            'type': 'stats',
            'data': {
              'processed_chunks': processedChunks,
              'total_bytes': totalBytes,
              'gain_boost': gainBoost,
              'enable_noise_reduction': enableNoiseReduction,
              'sample_rate': sampleRate,
            },
          });
          break;
          
        case 'shutdown':
          receivePort.close();
          break;
      }
    }
  });
}

/// Process a single audio chunk in the isolate
Uint8List _processAudioChunk(
  Uint8List audioData, {
  required double gainBoost,
  required bool enableNoiseReduction,
  required int sampleRate,
}) {
  // Convert bytes to 16-bit samples
  final samples = Int16List.view(audioData.buffer);
  final processedSamples = Int16List(samples.length);
  
  for (int i = 0; i < samples.length; i++) {
    double sample = samples[i].toDouble();
    
    // Apply gain boost
    sample *= gainBoost;
    
    // Simple noise reduction (basic high-pass filter)
    if (enableNoiseReduction && i > 0) {
      sample = sample - (processedSamples[i - 1] * 0.1);
    }
    
    // Clamp to prevent overflow
    sample = sample.clamp(-32768.0, 32767.0);
    
    processedSamples[i] = sample.round();
  }
  
  // Convert back to bytes
  final byteData = ByteData(processedSamples.length * 2);
  for (int i = 0; i < processedSamples.length; i++) {
    byteData.setInt16(i * 2, processedSamples[i], Endian.little);
  }
  
  return byteData.buffer.asUint8List();
}
