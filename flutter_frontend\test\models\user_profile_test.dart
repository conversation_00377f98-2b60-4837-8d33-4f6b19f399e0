import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/data/models/user_profile.dart';

void main() {
  group('UserProfile', () {
    late UserProfile testProfile;
    late DateTime testDate;

    setUp(() {
      testDate = DateTime(2024, 1, 15);
      testProfile = UserProfile(
        email: '<EMAIL>',
        plan: 'Trial',
        dateJoined: testDate,
        datePlan: testDate,
        maxCredits: 20,
        currentCredits: 15,
      );
    });

    group('JSON serialization', () {
      test('should serialize to JSON correctly', () {
        final json = testProfile.toJson();
        
        expect(json['email'], equals('<EMAIL>'));
        expect(json['plan'], equals('Trial'));
        expect(json['max_credits'], equals(20));
        expect(json['current_credits'], equals(15));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'email': '<EMAIL>',
          'plan': 'Trial',
          'date_joined': '2024-01-15T00:00:00.000',
          'date_plan': '2024-01-15T00:00:00.000',
          'max_credits': 20,
          'current_credits': 15,
        };

        final profile = UserProfile.fromJson(json);
        
        expect(profile.email, equals('<EMAIL>'));
        expect(profile.plan, equals('Trial'));
        expect(profile.maxCredits, equals(20));
        expect(profile.currentCredits, equals(15));
      });
    });

    group('Utility methods', () {
      test('should calculate next refresh date correctly', () {
        final profile = UserProfile(
          email: '<EMAIL>',
          plan: 'Trial',
          dateJoined: DateTime(2024, 1, 15),
          datePlan: DateTime(2024, 1, 15), // Plan started on 15th
          maxCredits: 20,
          currentCredits: 15,
        );

        final nextRefresh = profile.nextRefreshDate;
        expect(nextRefresh.day, equals(15)); // Should be 15th of next month
      });

      test('should check if credits are available', () {
        expect(testProfile.hasCreditsAvailable, isTrue);
        
        final noCreditsProfile = testProfile.copyWith(currentCredits: 0);
        expect(noCreditsProfile.hasCreditsAvailable, isFalse);
      });

      test('should calculate credits usage percentage', () {
        expect(testProfile.creditsUsagePercentage, equals(0.25)); // 5/20 = 0.25
        expect(testProfile.remainingCreditsPercentage, equals(0.75)); // 15/20 = 0.75
      });

      test('should detect if refresh is needed', () {
        final oldProfile = UserProfile(
          email: '<EMAIL>',
          plan: 'Trial',
          dateJoined: DateTime(2023, 12, 15),
          datePlan: DateTime(2023, 12, 15), // Plan from previous month
          maxCredits: 20,
          currentCredits: 15,
        );

        expect(oldProfile.needsRefresh, isTrue);
        expect(testProfile.needsRefresh, isFalse);
      });
    });

    group('Factory constructors', () {
      test('should create trial profile correctly', () {
        final trialProfile = UserProfile.createTrial('<EMAIL>');
        
        expect(trialProfile.email, equals('<EMAIL>'));
        expect(trialProfile.plan, equals('Trial'));
        expect(trialProfile.maxCredits, equals(20));
        expect(trialProfile.currentCredits, equals(20));
      });
    });

    group('copyWith', () {
      test('should create copy with updated fields', () {
        final updatedProfile = testProfile.copyWith(
          currentCredits: 10,
          plan: 'Premium',
        );

        expect(updatedProfile.email, equals(testProfile.email));
        expect(updatedProfile.currentCredits, equals(10));
        expect(updatedProfile.plan, equals('Premium'));
        expect(updatedProfile.maxCredits, equals(testProfile.maxCredits));
      });
    });

    group('Edge cases', () {
      test('should handle month-end refresh dates correctly', () {
        final profile = UserProfile(
          email: '<EMAIL>',
          plan: 'Trial',
          dateJoined: DateTime(2024, 1, 31),
          datePlan: DateTime(2024, 1, 31), // January 31st
          maxCredits: 20,
          currentCredits: 15,
        );

        final nextRefresh = profile.nextRefreshDate;
        // February doesn't have 31 days, should adjust to last day of February
        expect(nextRefresh.month, equals(2));
        expect(nextRefresh.day, lessThanOrEqualTo(29)); // 2024 is a leap year
      });

      test('should handle zero max credits', () {
        final zeroCreditsProfile = testProfile.copyWith(maxCredits: 0, currentCredits: 0);
        
        expect(zeroCreditsProfile.creditsUsagePercentage, equals(0.0));
        expect(zeroCreditsProfile.remainingCreditsPercentage, equals(0.0));
        expect(zeroCreditsProfile.hasCreditsAvailable, isFalse);
      });
    });
  });
}