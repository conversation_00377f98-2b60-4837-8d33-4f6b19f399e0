import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/data/services/session_tracker.dart';

void main() {
  group('SessionTracker', () {
    late SessionTracker sessionTracker;

    setUp(() {
      sessionTracker = SessionTracker();
    });

    group('Session tracking results', () {
      test('should create success result', () {
        final mockSession = SessionData.create(
          email: '<EMAIL>',
          message: 'Test message',
          response: 'Test response',
        );

        final mockProfile = UserProfile(
          email: '<EMAIL>',
          plan: 'Trial',
          dateJoined: DateTime.now(),
          datePlan: DateTime.now(),
          maxCredits: 20,
          currentCredits: 19,
        );

        final result = SessionTrackingResult.success(
          session: mockSession,
          updatedProfile: mockProfile,
        );

        expect(result.success, isTrue);
        expect(result.session, equals(mockSession));
        expect(result.updatedProfile, equals(mockProfile));
        expect(result.errorMessage, isNull);
        expect(result.wasRefreshed, isFalse);
        expect(result.type, equals(SessionTrackingResultType.success));
      });

      test('should create success result with refresh', () {
        final mockSession = SessionData.create(
          email: '<EMAIL>',
          message: 'Test message',
          response: 'Test response',
        );

        final mockProfile = UserProfile(
          email: '<EMAIL>',
          plan: 'Trial',
          dateJoined: DateTime.now(),
          datePlan: DateTime.now(),
          maxCredits: 20,
          currentCredits: 20,
        );

        final result = SessionTrackingResult.success(
          session: mockSession,
          updatedProfile: mockProfile,
          wasRefreshed: true,
        );

        expect(result.success, isTrue);
        expect(result.wasRefreshed, isTrue);
        expect(result.type, equals(SessionTrackingResultType.success));
      });

      test('should create credit error result', () {
        const message = 'No credits available';
        final result = SessionTrackingResult.creditError(
          message,
          CreditResultType.insufficientCredits,
        );

        expect(result.success, isFalse);
        expect(result.errorMessage, equals(message));
        expect(result.session, isNull);
        expect(result.updatedProfile, isNull);
        expect(result.type, equals(SessionTrackingResultType.creditError));
        expect(result.creditResultType, equals(CreditResultType.insufficientCredits));
      });

      test('should create general error result', () {
        const message = 'Database connection failed';
        final result = SessionTrackingResult.error(message);

        expect(result.success, isFalse);
        expect(result.errorMessage, equals(message));
        expect(result.session, isNull);
        expect(result.updatedProfile, isNull);
        expect(result.type, equals(SessionTrackingResultType.error));
        expect(result.creditResultType, isNull);
      });
    });

    group('Validation result', () {
      test('should create validation result with all fields', () {
        final nextRefresh = DateTime.now().add(const Duration(days: 15));

        final result = ValidationResult(
          canProceed: true,
          reason: 'Credits available',
          currentCredits: 15,
          maxCredits: 20,
          nextRefreshDate: nextRefresh,
          needsRefresh: false,
        );

        expect(result.canProceed, isTrue);
        expect(result.reason, equals('Credits available'));
        expect(result.currentCredits, equals(15));
        expect(result.maxCredits, equals(20));
        expect(result.nextRefreshDate, equals(nextRefresh));
        expect(result.needsRefresh, isFalse);
      });

      test('should create validation result for insufficient credits', () {
        final result = ValidationResult(
          canProceed: false,
          reason: 'No credits available',
          currentCredits: 0,
          maxCredits: 20,
        );

        expect(result.canProceed, isFalse);
        expect(result.reason, equals('No credits available'));
        expect(result.currentCredits, equals(0));
        expect(result.maxCredits, equals(20));
      });
    });

    group('Session statistics', () {
      test('should create session stats with all information', () {
        final firstSession = DateTime.now().subtract(const Duration(days: 30));
        final lastSession = DateTime.now().subtract(const Duration(hours: 1));
        final nextRefresh = DateTime.now().add(const Duration(days: 15));

        final stats = SessionStats(
          email: '<EMAIL>',
          totalSessions: 50,
          totalMessageLength: 1000,
          totalResponseLength: 2000,
          averageMessageLength: 20.0,
          averageResponseLength: 40.0,
          firstSession: firstSession,
          lastSession: lastSession,
          sessionsToday: 3,
          sessionsThisWeek: 15,
          sessionsThisMonth: 45,
          currentCredits: 10,
          maxCredits: 20,
          creditsUsed: 10,
          nextRefreshDate: nextRefresh,
        );

        expect(stats.email, equals('<EMAIL>'));
        expect(stats.totalSessions, equals(50));
        expect(stats.totalMessageLength, equals(1000));
        expect(stats.totalResponseLength, equals(2000));
        expect(stats.averageMessageLength, equals(20.0));
        expect(stats.averageResponseLength, equals(40.0));
        expect(stats.firstSession, equals(firstSession));
        expect(stats.lastSession, equals(lastSession));
        expect(stats.sessionsToday, equals(3));
        expect(stats.sessionsThisWeek, equals(15));
        expect(stats.sessionsThisMonth, equals(45));
        expect(stats.currentCredits, equals(10));
        expect(stats.maxCredits, equals(20));
        expect(stats.creditsUsed, equals(10));
        expect(stats.nextRefreshDate, equals(nextRefresh));
      });
    });

    group('Session analytics', () {
      test('should create session analytics with trends', () {
        final stats = SessionStats(
          email: '<EMAIL>',
          totalSessions: 50,
          totalMessageLength: 1000,
          totalResponseLength: 2000,
          averageMessageLength: 20.0,
          averageResponseLength: 40.0,
          firstSession: DateTime.now().subtract(const Duration(days: 30)),
          lastSession: DateTime.now().subtract(const Duration(hours: 1)),
          sessionsToday: 3,
          sessionsThisWeek: 15,
          sessionsThisMonth: 45,
          currentCredits: 10,
          maxCredits: 20,
          creditsUsed: 10,
          nextRefreshDate: DateTime.now().add(const Duration(days: 15)),
        );

        final dailyCounts = {
          '2024-01-15': 5,
          '2024-01-16': 3,
          '2024-01-17': 7,
        };

        final recentSessions = [
          SessionData.create(
            email: '<EMAIL>',
            message: 'Recent message 1',
            response: 'Recent response 1',
          ),
          SessionData.create(
            email: '<EMAIL>',
            message: 'Recent message 2',
            response: 'Recent response 2',
          ),
        ];

        final analytics = SessionAnalytics(
          stats: stats,
          dailyCounts: dailyCounts,
          recentSessions: recentSessions,
          todaySessions: 3,
          yesterdaySessions: 2,
          trend: 50.0, // 50% increase
          peakDay: '2024-01-17',
          averageSessionsPerDay: 5.0,
        );

        expect(analytics.stats, equals(stats));
        expect(analytics.dailyCounts, equals(dailyCounts));
        expect(analytics.recentSessions, equals(recentSessions));
        expect(analytics.todaySessions, equals(3));
        expect(analytics.yesterdaySessions, equals(2));
        expect(analytics.trend, equals(50.0));
        expect(analytics.peakDay, equals('2024-01-17'));
        expect(analytics.averageSessionsPerDay, equals(5.0));
      });
    });

    group('Usage summary', () {
      test('should create usage summary with all information', () {
        final nextRefresh = DateTime.now().add(const Duration(days: 10));

        final summary = UsageSummary(
          email: '<EMAIL>',
          totalSessions: 25,
          creditsUsed: 15,
          creditsRemaining: 5,
          creditsPercentageUsed: 0.75,
          nextRefreshDate: nextRefresh,
          daysUntilRefresh: 10,
          averageSessionsPerDay: 2.5,
          status: 'Low credits',
        );

        expect(summary.email, equals('<EMAIL>'));
        expect(summary.totalSessions, equals(25));
        expect(summary.creditsUsed, equals(15));
        expect(summary.creditsRemaining, equals(5));
        expect(summary.creditsPercentageUsed, equals(0.75));
        expect(summary.nextRefreshDate, equals(nextRefresh));
        expect(summary.daysUntilRefresh, equals(10));
        expect(summary.averageSessionsPerDay, equals(2.5));
        expect(summary.status, equals('Low credits'));
      });

      test('should format usage message correctly', () {
        final summary = UsageSummary(
          email: '<EMAIL>',
          totalSessions: 25,
          creditsUsed: 15,
          creditsRemaining: 5,
          creditsPercentageUsed: 0.75,
          nextRefreshDate: DateTime.now().add(const Duration(days: 10)),
          daysUntilRefresh: 10,
          averageSessionsPerDay: 2.5,
          status: 'Low credits',
        );

        expect(summary.formattedUsage, equals('15 of 20 credits used (75.0%)'));
      });

      test('should provide appropriate usage colors', () {
        // Red for high usage (90%+)
        var summary = UsageSummary(
          email: '<EMAIL>',
          totalSessions: 25,
          creditsUsed: 18,
          creditsRemaining: 2,
          creditsPercentageUsed: 0.9,
          nextRefreshDate: DateTime.now().add(const Duration(days: 10)),
          daysUntilRefresh: 10,
          averageSessionsPerDay: 2.5,
          status: 'Very low credits',
        );

        expect(summary.usageColor.value, equals(0xFFF44336)); // Red

        // Orange for medium usage (70-89%)
        summary = UsageSummary(
          email: '<EMAIL>',
          totalSessions: 25,
          creditsUsed: 15,
          creditsRemaining: 5,
          creditsPercentageUsed: 0.75,
          nextRefreshDate: DateTime.now().add(const Duration(days: 10)),
          daysUntilRefresh: 10,
          averageSessionsPerDay: 2.5,
          status: 'Low credits',
        );

        expect(summary.usageColor.value, equals(0xFFFF9800)); // Orange

        // Green for low usage (<70%)
        summary = UsageSummary(
          email: '<EMAIL>',
          totalSessions: 25,
          creditsUsed: 10,
          creditsRemaining: 10,
          creditsPercentageUsed: 0.5,
          nextRefreshDate: DateTime.now().add(const Duration(days: 10)),
          daysUntilRefresh: 10,
          averageSessionsPerDay: 2.5,
          status: 'Credits available',
        );

        expect(summary.usageColor.value, equals(0xFF4CAF50)); // Green
      });
    });

    group('Helper methods', () {
      test('should find peak day correctly', () {
        final dailyCounts = {
          '2024-01-15': 5,
          '2024-01-16': 3,
          '2024-01-17': 7,
          '2024-01-18': 2,
        };

        // Using reflection to test private method would be complex
        // Instead, we test the behavior through public methods
        // The peak day should be '2024-01-17' with 7 sessions
        expect(dailyCounts['2024-01-17'], equals(7));
        
        // Find the maximum value
        final maxCount = dailyCounts.values.reduce((a, b) => a > b ? a : b);
        expect(maxCount, equals(7));
      });

      test('should calculate average sessions per day correctly', () {
        final dailyCounts = {
          '2024-01-15': 5,
          '2024-01-16': 3,
          '2024-01-17': 7,
          '2024-01-18': 1,
        };

        final totalSessions = dailyCounts.values.fold(0, (sum, count) => sum + count);
        final averageSessionsPerDay = totalSessions / dailyCounts.length;
        
        expect(totalSessions, equals(16));
        expect(averageSessionsPerDay, equals(4.0));
      });
    });
  });
}