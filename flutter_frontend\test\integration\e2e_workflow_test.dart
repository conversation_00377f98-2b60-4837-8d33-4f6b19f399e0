import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter/material.dart';
import 'package:deutschkorrekt_flutter/main.dart' as app;
import 'package:deutschkorrekt_flutter/core/config/supabase_config.dart';
import 'package:deutschkorrekt_flutter/data/services/session_tracker.dart';
import 'package:deutschkorrekt_flutter/data/services/credit_manager.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('End-to-End Workflow Tests', () {
    late SessionTracker sessionTracker;
    late CreditManager creditManager;
    late String testEmail;

    setUpAll(() async {
      await SupabaseConfig.initialize();
      sessionTracker = SessionTracker();
      creditManager = CreditManager();
      testEmail = 'e2e_test_${DateTime.now().millisecondsSinceEpoch}@example.com';
    });

    testWidgets('Complete user journey: signup -> chat -> profile -> logout', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Step 1: User signup
      await _performSignup(tester, testEmail);
      
      // Step 2: Navigate to chat and verify initial state
      await _verifyInitialChatState(tester);
      
      // Step 3: Simulate chat interactions with credit consumption
      await _simulateChatInteractions(tester);
      
      // Step 4: Check profile and credit status
      await _checkProfileAndCredits(tester);
      
      // Step 5: Test credit refresh functionality
      await _testCreditRefresh(tester);
      
      // Step 6: Logout and verify cleanup
      await _performLogout(tester);
    });

    testWidgets('Credit exhaustion and refresh workflow', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login with test account
      await _performLogin(tester, testEmail);
      
      // Consume all credits through API calls
      await _exhaustAllCredits();
      
      // Try to make a request - should be blocked
      await _verifyRequestBlocked(tester);
      
      // Refresh credits
      await _refreshCreditsViaAPI();
      
      // Verify requests work again
      await _verifyRequestsWorkAgain(tester);
    });

    testWidgets('Session tracking and analytics workflow', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login
      await _performLogin(tester, testEmail);
      
      // Generate multiple sessions
      await _generateMultipleSessions();
      
      // Check session analytics in profile
      await _verifySessionAnalytics(tester);
      
      // Test session search functionality
      await _testSessionSearch();
    });

    testWidgets('Error handling and recovery workflow', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Test network error handling
      await _testNetworkErrorHandling(tester);
      
      // Test authentication error handling
      await _testAuthErrorHandling(tester);
      
      // Test database error recovery
      await _testDatabaseErrorRecovery(tester);
    });

    testWidgets('Profile management workflow', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login
      await _performLogin(tester, testEmail);
      
      // Open profile
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();

      // Verify profile information
      expect(find.text('Profile'), findsOneWidget);
      expect(find.text(testEmail), findsOneWidget);
      expect(find.text('Trial'), findsOneWidget);
      
      // Test profile refresh
      await tester.tap(find.text('Refresh Profile'));
      await tester.pumpAndSettle();
      
      // Should show success message
      expect(find.text('Profile refreshed successfully!'), findsOneWidget);
      
      // Test settings dialog
      await tester.tap(find.text('Settings'));
      await tester.pumpAndSettle();
      
      expect(find.text('Notifications'), findsOneWidget);
      expect(find.text('Language'), findsOneWidget);
      
      // Close settings
      await tester.tap(find.text('Close'));
      await tester.pumpAndSettle();
      
      // Test help dialog
      await tester.tap(find.text('Help & Support'));
      await tester.pumpAndSettle();
      
      expect(find.text('DeutschKorrekt Help'), findsOneWidget);
      expect(find.text('Credits refresh monthly'), findsOneWidget);
      
      // Close help
      await tester.tap(find.text('Close'));
      await tester.pumpAndSettle();
      
      // Close profile
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();
    });
  });
}

Future<void> _performSignup(WidgetTester tester, String email) async {
  // Switch to signup mode
  await tester.tap(find.text('Sign up'));
  await tester.pumpAndSettle();

  // Fill signup form
  await tester.enterText(find.byType(TextFormField).first, email);
  await tester.enterText(find.byType(TextFormField).at(1), 'password123');
  await tester.enterText(find.byType(TextFormField).at(2), 'password123');

  // Submit signup
  await tester.tap(find.text('Sign Up'));
  await tester.pumpAndSettle();

  // Wait for potential email verification or direct login
  await tester.pumpAndSettle(const Duration(seconds: 2));
}

Future<void> _performLogin(WidgetTester tester, String email) async {
  // Fill login form
  await tester.enterText(find.byType(TextFormField).first, email);
  await tester.enterText(find.byType(TextFormField).at(1), 'password123');

  // Submit login
  await tester.tap(find.text('Sign In'));
  await tester.pumpAndSettle();
}

Future<void> _verifyInitialChatState(WidgetTester tester) async {
  // Should be on chat screen
  expect(find.text('DeutschKorrekt'), findsOneWidget);
  expect(find.byIcon(Icons.person), findsOneWidget);
  expect(find.byIcon(Icons.settings), findsOneWidget);
  
  // Should see welcome message
  expect(find.textContaining('Hallo! Ich bin DeutschKorrekt'), findsOneWidget);
}

Future<void> _simulateChatInteractions(WidgetTester tester) async {
  // Note: In a real implementation, this would simulate actual chat interactions
  // For now, we'll simulate the interactions via API calls
  
  // This would involve:
  // 1. Tapping the microphone button
  // 2. Simulating audio input
  // 3. Waiting for response
  // 4. Verifying credit consumption
  
  print('Simulating chat interactions...');
  await tester.pumpAndSettle();
}

Future<void> _checkProfileAndCredits(WidgetTester tester) async {
  // Open profile
  await tester.tap(find.byIcon(Icons.person));
  await tester.pumpAndSettle();

  // Verify profile elements
  expect(find.text('Profile'), findsOneWidget);
  expect(find.text('Credits'), findsOneWidget);
  expect(find.text('Statistics'), findsOneWidget);
  
  // Should show credit information
  expect(find.textContaining('of 20 credits'), findsOneWidget);
  
  // Close profile
  await tester.tap(find.byIcon(Icons.close));
  await tester.pumpAndSettle();
}

Future<void> _testCreditRefresh(WidgetTester tester) async {
  // Open profile
  await tester.tap(find.byIcon(Icons.person));
  await tester.pumpAndSettle();

  // Look for refresh button (if credits need refresh)
  final refreshButton = find.text('Refresh Credits');
  if (refreshButton.evaluate().isNotEmpty) {
    await tester.tap(refreshButton);
    await tester.pumpAndSettle();
    
    // Should show success message
    expect(find.text('Credits refreshed successfully!'), findsOneWidget);
  }
  
  // Close profile
  await tester.tap(find.byIcon(Icons.close));
  await tester.pumpAndSettle();
}

Future<void> _performLogout(WidgetTester tester) async {
  // Open profile
  await tester.tap(find.byIcon(Icons.person));
  await tester.pumpAndSettle();

  // Tap sign out
  await tester.tap(find.text('Sign Out'));
  await tester.pumpAndSettle();

  // Confirm logout
  await tester.tap(find.text('Sign Out').last);
  await tester.pumpAndSettle();

  // Should return to auth screen
  expect(find.text('Sign In'), findsOneWidget);
}

Future<void> _exhaustAllCredits() async {
  // Use API calls to consume all credits
  print('Exhausting all credits via API...');
  // This would make multiple session tracking calls until credits are 0
}

Future<void> _verifyRequestBlocked(WidgetTester tester) async {
  // Try to make a chat request and verify it's blocked
  print('Verifying request is blocked due to no credits...');
  await tester.pumpAndSettle();
}

Future<void> _refreshCreditsViaAPI() async {
  // Use API to refresh credits
  print('Refreshing credits via API...');
}

Future<void> _verifyRequestsWorkAgain(WidgetTester tester) async {
  // Verify that requests work after credit refresh
  print('Verifying requests work after credit refresh...');
  await tester.pumpAndSettle();
}

Future<void> _generateMultipleSessions() async {
  // Generate multiple sessions via API for analytics testing
  print('Generating multiple sessions for analytics...');
}

Future<void> _verifySessionAnalytics(WidgetTester tester) async {
  // Open profile and check session statistics
  await tester.tap(find.byIcon(Icons.person));
  await tester.pumpAndSettle();

  // Should show session statistics
  expect(find.text('Statistics'), findsOneWidget);
  expect(find.text('Total Sessions'), findsOneWidget);
  
  await tester.tap(find.byIcon(Icons.close));
  await tester.pumpAndSettle();
}

Future<void> _testSessionSearch() async {
  // Test session search functionality via API
  print('Testing session search functionality...');
}

Future<void> _testNetworkErrorHandling(WidgetTester tester) async {
  // Test how the app handles network errors
  print('Testing network error handling...');
  await tester.pumpAndSettle();
}

Future<void> _testAuthErrorHandling(WidgetTester tester) async {
  // Test authentication error scenarios
  
  // Try login with invalid credentials
  await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
  await tester.enterText(find.byType(TextFormField).at(1), 'wrongpassword');
  
  await tester.tap(find.text('Sign In'));
  await tester.pumpAndSettle();
  
  // Should show error message
  expect(find.textContaining('Invalid email or password'), findsOneWidget);
}

Future<void> _testDatabaseErrorRecovery(WidgetTester tester) async {
  // Test database error recovery mechanisms
  print('Testing database error recovery...');
  await tester.pumpAndSettle();
}