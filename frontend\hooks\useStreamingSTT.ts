import { useState, useRef, useCallback, useEffect } from 'react';
import { DeutschKorrektSTT } from '../services/deepgram';

interface GroqResponse {
  original_text: string;
  response_text: string;
  processing_time: number;
}

interface UseStreamingSTTProps {
  backendUrl?: string;
  onFinalTranscript?: (text: string) => void;
  onGroqResponse?: (result: GroqResponse) => void;
  onError?: (error: string) => void;
  onTimeout?: () => void;
  onInfo?: (message: string) => void;
}

enum StreamingState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  STREAMING = 'streaming',
  PROCESSING = 'processing',
  ERROR = 'error'
}

/**
 * Professional Streaming STT Hook
 * Handles WebSocket connection and audio streaming with Deepgram + Groq
 */
export function useStreamingSTT({
  backendUrl,
  onFinalTranscript,
  onGroqResponse,
  onError,
  onTimeout,
  onInfo,
}: UseStreamingSTTProps) {
  const [state, setState] = useState<StreamingState>(StreamingState.DISCONNECTED);
  const [partialText, setPartialText] = useState('');
  const [finalText, setFinalText] = useState('');
  const [groqResponse, setGroqResponse] = useState<GroqResponse | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const sttRef = useRef<DeutschKorrektSTT | null>(null);

  const connect = useCallback(async () => {
    if (!sttRef.current) {
      sttRef.current = new DeutschKorrektSTT(backendUrl);
    }

    sttRef.current.connect({
      onPartialTranscript: (text) => {
        setPartialText(text);
      },
      onFinalTranscript: (text) => {
        setFinalText(text);
        setPartialText(''); // Clear partial text when we get final
        onFinalTranscript?.(text);
      },
      onProcessing: () => {
        setIsProcessing(true);
        setPartialText('Processing with Groq...');
      },
      onGroqResponse: (result) => {
        setIsProcessing(false);
        setGroqResponse(result);
        onGroqResponse?.(result);
      },
      onError: (error) => {
        console.error('STT Error:', error);
        setIsConnected(false);
        setIsProcessing(false);
        setState(StreamingState.ERROR);
        onError?.(error);
      },
      onTimeout: () => {
        setIsProcessing(false);
        onTimeout?.();
      },
      onInfo: (message) => {
        console.log('STT Info:', message);
        setIsConnected(true);
        setState(StreamingState.CONNECTED);
        onInfo?.(message);
      },
    });
  }, [backendUrl, onFinalTranscript, onGroqResponse, onError, onTimeout, onInfo]);

  const disconnect = useCallback(() => {
    if (sttRef.current) {
      sttRef.current.stopRealTimeStreaming();
      sttRef.current.disconnect();
      setIsConnected(false);
      setIsStreaming(false);
      setPartialText('');
      setIsProcessing(false);
      setState(StreamingState.DISCONNECTED);
    }
  }, []);

  const startStreaming = useCallback(async () => {
    if (sttRef.current && isConnected) {
      try {
        await sttRef.current.startRealTimeStreaming();
        setIsStreaming(true);
        setState(StreamingState.STREAMING);
        console.log('🎤 Hook: Streaming started, isStreaming = true');
      } catch (error) {
        console.error('❌ Hook: Failed to start streaming:', error);
        setIsStreaming(false);
        setState(StreamingState.ERROR);
      }
    } else {
      console.log('⚠️ Hook: Cannot start streaming - not connected or no service');
    }
  }, [isConnected]);

  const stopStreaming = useCallback(() => {
    if (sttRef.current) {
      sttRef.current.stopRealTimeStreaming();
      setIsStreaming(false);
      setState(StreamingState.CONNECTED);
      console.log('🛑 Hook: Streaming stopped, isStreaming = false');
    } else {
      console.log('⚠️ Hook: Cannot stop streaming - no service');
    }
  }, []);

  const sendAudioData = useCallback((audioData: ArrayBuffer) => {
    if (sttRef.current && isConnected) {
      sttRef.current.sendAudioData(audioData);
    }
  }, [isConnected]);

  const reset = useCallback(() => {
    setPartialText('');
    setFinalText('');
    setIsProcessing(false);
    setGroqResponse(null);
  }, []);

  return {
    state,
    isConnected,
    isStreaming,
    partialText,
    finalText,
    isProcessing,
    groqResponse,
    connect,
    disconnect,
    startStreaming,
    stopStreaming,
    sendAudioData,
    reset,
  };
}