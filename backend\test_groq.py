#!/usr/bin/env python3
"""
Test script to verify Groq integration is working properly.
"""

import os
import asyncio
from services.language_service import LanguageService

async def test_groq_correction():
    """Test the Groq language correction functionality."""
    
    # Initialize the language service
    language_service = LanguageService()
    
    # Test cases with German text that needs correction
    test_cases = [
        "Ich bin sehr müde heute und möchte nach hause gehen.",  # "hause" should be "Hause"
        "Das ist ein sehr schöne Tag für ein spaziergang.",      # "schöne" should be "schöner", "spaziergang" should be "Spaziergang"
        "Können sie mir bitte helfen mit diese aufgabe?",       # "diese" should be "dieser"
        "Wir haben gestern ein interessante film gesehen.",     # "interessante" should be "interessanten"
    ]
    
    print("🧪 Testing Groq Language Correction Service")
    print("=" * 50)
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n📝 Test Case {i}:")
        print(f"Original: {test_text}")
        
        try:
            # Call the language service
            result = await language_service.process_text(test_text)
            
            if "error" in result:
                print(f"❌ Error: {result['error']}")
            else:
                print(f"✅ Response: {result['processed_content']['response_text']}")
                print(f"⏱️  Processing time: {result['processing_time']:.3f}s")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎉 Test completed!")

if __name__ == "__main__":
    # Check if GROQ_API_KEY is set
    if not os.getenv("GROQ_API_KEY"):
        print("❌ Error: GROQ_API_KEY environment variable not set!")
        print("Please set your Groq API key:")
        print("export GROQ_API_KEY=your_api_key_here")
        exit(1)
    
    # Run the test
    asyncio.run(test_groq_correction())