import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/gradients.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/message.dart';
import '../../../data/services/websocket_message_processor.dart';
import '../common/formatted_text.dart';
import 'processing_state_widget.dart';
import 'websocket_error_widget.dart';

/// Enhanced animated message bubble with WebSocket processing state integration
class AnimatedMessageBubble extends StatefulWidget {
  final Message message;
  final bool showTimestamp;
  final bool showAvatar;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final ProcessingState? processingState;
  
  const AnimatedMessageBubble({
    super.key,
    required this.message,
    this.showTimestamp = false,
    this.showAvatar = false,
    this.onTap,
    this.onLongPress,
    this.processingState,
  });

  @override
  State<AnimatedMessageBubble> createState() => _AnimatedMessageBubbleState();
}

class _AnimatedMessageBubbleState extends State<AnimatedMessageBubble>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.85,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.8, curve: Curves.easeInOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: widget.message.isUser ? const Offset(0.2, 0) : const Offset(-0.2, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
    ));
    
    _animationController.forward();
  }
  
  @override
  void didUpdateWidget(AnimatedMessageBubble oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Animate when message content changes (e.g., streaming updates)
    if (oldWidget.message.text != widget.message.text ||
        oldWidget.message.isStreaming != widget.message.isStreaming ||
        oldWidget.message.isProcessing != widget.message.isProcessing) {
      _animationController.forward(from: 0.8);
    }
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
            child: Row(
              mainAxisAlignment: widget.message.isUser 
                  ? MainAxisAlignment.end 
                  : MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // AI avatar (left side)
                if (!widget.message.isUser && widget.showAvatar)
                  _buildAvatar(),
                
                // Message content
                Flexible(
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width * 0.75,
                    ),
                    child: Column(
                      crossAxisAlignment: widget.message.isUser 
                          ? CrossAxisAlignment.end 
                          : CrossAxisAlignment.start,
                      children: [
                        // Processing state indicator (above message)
                        if (widget.processingState != null && 
                            _shouldShowProcessingState())
                          Padding(
                            padding: const EdgeInsets.only(bottom: 8),
                            child: CompactProcessingStateWidget(
                              processingState: widget.processingState!,
                            ),
                          ),
                        
                        // Message bubble
                        _buildMessageBubble(context),
                        
                        // Timestamp
                        if (widget.showTimestamp)
                          _buildTimestamp(),
                      ],
                    ),
                  ),
                ),
                
                // User avatar (right side)
                if (widget.message.isUser && widget.showAvatar)
                  _buildAvatar(),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  /// Build message bubble with enhanced styling and animations
  Widget _buildMessageBubble(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onLongPress: widget.onLongPress,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          gradient: widget.message.isUser ? _getUserMessageGradient() : null,
          color: widget.message.isUser ? null : _getAiMessageColor(),
          border: widget.message.isUser 
              ? null 
              : Border.all(color: _getAiMessageBorderColor(), width: 1),
          borderRadius: BorderRadius.circular(18),
          boxShadow: [
            BoxShadow(
              color: _getShadowColor(),
              blurRadius: widget.message.isStreaming ? 8 : 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Enhanced streaming indicator
            if (widget.message.isStreaming)
              _buildEnhancedStreamingIndicator(),
            
            // Enhanced processing indicator
            if (widget.message.isProcessing)
              _buildEnhancedProcessingIndicator(),
            
            // Message text with enhanced formatting
            _buildEnhancedMessageText(),
            
            // Correction results with enhanced styling
            if (widget.message.correctionResult != null)
              _buildEnhancedCorrectionResult(),
          ],
        ),
      ),
    );
  }
  
  /// Build enhanced streaming indicator with real-time updates
  Widget _buildEnhancedStreamingIndicator() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Animated pulsing dots
          ...List.generate(3, (index) => 
            Padding(
              padding: EdgeInsets.only(right: index < 2 ? 4 : 8),
              child: _buildAnimatedPulsingDot(index * 200),
            ),
          ),
          
          // Status text with confidence if available
          Flexible(
            child: Text(
              _getStreamingStatusText(),
              style: AppTextStyles.streamingIndicator.copyWith(
                color: _getStreamingTextColor(),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build enhanced processing indicator with progress
  Widget _buildEnhancedProcessingIndicator() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Animated processing icon
          SizedBox(
            width: 16,
            height: 16,
            child: TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 1500),
              tween: Tween(begin: 0.0, end: 1.0),
              builder: (context, value, child) {
                return Transform.rotate(
                  angle: value * 2 * 3.14159,
                  child: Icon(
                    Icons.psychology,
                    size: 16,
                    color: AppColors.warningYellow,
                  ),
                );
              },
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Processing text
          Flexible(
            child: Text(
              _getProcessingStatusText(),
              style: AppTextStyles.processingIndicator.copyWith(
                color: AppColors.warningYellow,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build enhanced message text with better formatting
  Widget _buildEnhancedMessageText() {
    if (widget.message.text.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200),
      child: widget.message.isUser
          ? FormattedTextExtensions.userMessage(
              widget.message.text,
              textAlign: TextAlign.left,
              key: ValueKey(widget.message.text),
            )
          : FormattedTextExtensions.aiMessage(
              widget.message.text,
              textAlign: TextAlign.left,
              key: ValueKey(widget.message.text),
            ),
    );
  }
  
  /// Build enhanced correction result with better styling
  Widget _buildEnhancedCorrectionResult() {
    // This will be implemented with the correction result widget
    return const SizedBox.shrink();
  }
  
  /// Build animated pulsing dot with delay
  Widget _buildAnimatedPulsingDot(int delayMs) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.3, end: 1.0),
      builder: (context, value, child) {
        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            final delayedValue = (value + (delayMs / 1000.0)) % 1.0;
            return Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.infoBlue.withOpacity(
                  0.3 + (0.7 * delayedValue),
                ),
              ),
            );
          },
        );
      },
    );
  }
  
  /// Build avatar for user/AI
  Widget _buildAvatar() {
    return Container(
      width: 32,
      height: 32,
      margin: EdgeInsets.only(
        left: widget.message.isUser ? 8 : 0,
        right: widget.message.isUser ? 0 : 8,
      ),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: widget.message.isUser 
            ? AppColors.slate600 
            : AppColors.infoBlue,
        border: Border.all(
          color: AppColors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Icon(
        widget.message.isUser ? Icons.person : Icons.psychology,
        color: AppColors.white,
        size: 18,
      ),
    );
  }
  
  /// Build timestamp
  Widget _buildTimestamp() {
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Text(
        _formatTimestamp(widget.message.timestamp),
        style: AppTextStyles.timestampText,
      ),
    );
  }
  
  /// Get user message gradient based on state
  LinearGradient _getUserMessageGradient() {
    if (widget.message.isStreaming) {
      return LinearGradient(
        colors: [
          AppColors.slate600.withOpacity(0.8),
          AppColors.slate700.withOpacity(0.8),
        ],
      );
    }
    return AppGradients.userMessageGradient;
  }
  
  /// Get AI message color based on state
  Color _getAiMessageColor() {
    if (widget.message.isProcessing) {
      return AppColors.aiMessageBackground.withOpacity(0.9);
    }
    return AppColors.aiMessageBackground;
  }
  
  /// Get AI message border color based on state
  Color _getAiMessageBorderColor() {
    if (widget.message.isProcessing) {
      return AppColors.warningYellow.withOpacity(0.3);
    }
    return AppColors.aiMessageBorder;
  }
  
  /// Get shadow color based on message state
  Color _getShadowColor() {
    if (widget.message.isStreaming) {
      return AppColors.infoBlue.withOpacity(0.3);
    } else if (widget.message.isProcessing) {
      return AppColors.warningYellow.withOpacity(0.3);
    }
    return AppColors.shadowLight;
  }
  
  /// Get streaming status text
  String _getStreamingStatusText() {
    if (widget.processingState != null) {
      return widget.processingState!.message;
    }
    return '● Live transcription...';
  }
  
  /// Get processing status text
  String _getProcessingStatusText() {
    if (widget.processingState != null) {
      return widget.processingState!.message;
    }
    return '⚡ Processing with AI agents...';
  }
  
  /// Get streaming text color
  Color _getStreamingTextColor() {
    return AppColors.infoBlue;
  }
  
  /// Format timestamp for display
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${timestamp.day}/${timestamp.month}';
    }
  }
  
  /// Check if processing state should be shown
  bool _shouldShowProcessingState() {
    if (widget.processingState == null) return false;
    
    // Show processing state for relevant types
    return widget.processingState!.type == ProcessingType.transcribing ||
           widget.processingState!.type == ProcessingType.aiProcessing ||
           widget.processingState!.type == ProcessingType.error;
  }
}

/// Extension for FormattedText with enhanced styling
extension FormattedTextExtensions on FormattedText {
  static Widget userMessage(String text, {TextAlign? textAlign, Key? key}) {
    return FormattedText(
      key: key,
      text: text,
      style: AppTextStyles.userMessageText,
      boldStyle: AppTextStyles.userMessageText.copyWith(
        fontWeight: FontWeight.bold,
      ),
      textAlign: textAlign,
    );
  }
  
  static Widget aiMessage(String text, {TextAlign? textAlign, Key? key}) {
    return FormattedText(
      key: key,
      text: text,
      style: AppTextStyles.aiMessageText,
      boldStyle: AppTextStyles.aiMessageText.copyWith(
        fontWeight: FontWeight.bold,
      ),
      textAlign: textAlign,
    );
  }
}