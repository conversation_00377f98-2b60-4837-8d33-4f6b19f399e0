import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/data/services/auth_service.dart';

void main() {
  group('AuthService', () {
    group('Email validation', () {
      test('should validate correct email formats', () {
        expect(AuthService.isValidEmail('<EMAIL>'), isTrue);
        expect(AuthService.isValidEmail('<EMAIL>'), isTrue);
        expect(AuthService.isValidEmail('<EMAIL>'), isTrue);
      });

      test('should reject invalid email formats', () {
        expect(AuthService.isValidEmail('invalid-email'), isFalse);
        expect(AuthService.isValidEmail('test@'), isFalse);
        expect(AuthService.isValidEmail('@example.com'), isFalse);
        expect(AuthService.isValidEmail('test.example.com'), isFalse);
        expect(AuthService.isValidEmail(''), isFalse);
      });
    });

    group('Password validation', () {
      test('should validate password length', () {
        expect(AuthService.isValidPassword('12345678'), isTrue);
        expect(AuthService.isValidPassword('password123'), isTrue);
        expect(AuthService.isValidPassword('1234567'), isFalse);
        expect(AuthService.isValidPassword(''), isFalse);
      });

      test('should provide detailed password requirements', () {
        final requirements = AuthService.getPasswordRequirements('Password123!');
        
        expect(requirements['minLength'], isTrue);
        expect(requirements['hasLetter'], isTrue);
        expect(requirements['hasNumber'], isTrue);
        expect(requirements['hasSpecialChar'], isTrue);
      });

      test('should identify missing password requirements', () {
        final requirements = AuthService.getPasswordRequirements('pass');
        
        expect(requirements['minLength'], isFalse);
        expect(requirements['hasLetter'], isTrue);
        expect(requirements['hasNumber'], isFalse);
        expect(requirements['hasSpecialChar'], isFalse);
      });
    });

    group('Error message handling', () {
      test('should provide user-friendly error messages', () {
        expect(
          AuthService.getErrorMessage('Invalid login credentials'),
          contains('Invalid email or password'),
        );
        
        expect(
          AuthService.getErrorMessage('Email not confirmed'),
          contains('check your email'),
        );
        
        expect(
          AuthService.getErrorMessage('User already registered'),
          contains('account with this email already exists'),
        );
        
        expect(
          AuthService.getErrorMessage('Password should be at least'),
          contains('Password must be at least 8 characters'),
        );
        
        expect(
          AuthService.getErrorMessage('Network error occurred'),
          contains('Network error'),
        );
        
        expect(
          AuthService.getErrorMessage('Rate limit exceeded'),
          contains('Too many attempts'),
        );
      });

      test('should provide generic message for unknown errors', () {
        final message = AuthService.getErrorMessage('Unknown error type');
        expect(message, equals('An unexpected error occurred. Please try again.'));
      });
    });
  });
}