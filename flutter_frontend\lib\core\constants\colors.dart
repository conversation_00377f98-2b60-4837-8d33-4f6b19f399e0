import 'package:flutter/material.dart';

/// Color constants matching the Expo version exactly
class AppColors {
  // Gradient colors from Expo version
  static const Color slate500 = Color(0xFF94a3b8);
  static const Color slate600 = Color(0xFF64748b);
  static const Color slate700 = Color(0xFF475569);
  static const Color slate800 = Color(0xFF334155);
  static const Color slate900 = Color(0xFF1e293b);
  static const Color slate950 = Color(0xFF0f172a);
  
  // German flag colors
  static const Color germanBlack = Color(0xFF03080c);
  static const Color germanRed = Color(0xFFdd291a);
  static const Color germanYellow = Color(0xFFfdb922);
  
  // Message colors
  static const Color userMessageGradientStart = Color(0xFF64748b);
  static const Color userMessageGradientEnd = Color(0xFF475569);
  static const Color aiMessageBackground = Color(0xFFffffff);
  static const Color aiMessageBorder = Color(0xFFe2e8f0);
  
  // Text colors
  static const Color lightText = Color(0xFFf1f5f9);
  static const Color userMessageText = Color(0xFFf8fafc);
  static const Color aiMessageText = Color(0xFF1e293b);
  
  // Status colors
  static const Color successGreen = Color(0xFF10b981);
  static const Color errorRed = Color(0xFFef4444);
  static const Color warningYellow = Color(0xFFf59e0b);
  static const Color warningOrange = Color(0xFFf97316);
  static const Color infoBlue = Color(0xFF3b82f6);
  static const Color primaryBlue = Color(0xFF3b82f6);
  
  // Additional UI colors
  static const Color transparent = Colors.transparent;
  static const Color white = Color(0xFFffffff);
  static const Color black = Color(0xFF000000);
  
  // Shadow colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);
  
  // Overlay colors
  static const Color overlayLight = Color(0x1Affffff);
  static const Color overlayMedium = Color(0x33ffffff);
  static const Color overlayDark = Color(0x4Dffffff);
  
  // Disabled colors
  static const Color disabledText = Color(0xFF64748b);
  static const Color disabledBackground = Color(0xFF334155);
  
  // Private constructor to prevent instantiation
  const AppColors._();
}