import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/data/services/session_manager.dart';

void main() {
  group('SessionManager', () {
    group('SessionInfo', () {
      test('should create session info with all properties', () {
        final expiresAt = DateTime.now().add(const Duration(hours: 1));
        
        final sessionInfo = SessionInfo(
          accessToken: 'access_token_123',
          refreshToken: 'refresh_token_456',
          expiresAt: expiresAt,
          userId: 'user_123',
          userEmail: '<EMAIL>',
          isValid: true,
        );

        expect(sessionInfo.accessToken, equals('access_token_123'));
        expect(sessionInfo.refreshToken, equals('refresh_token_456'));
        expect(sessionInfo.expiresAt, equals(expiresAt));
        expect(sessionInfo.userId, equals('user_123'));
        expect(sessionInfo.userEmail, equals('<EMAIL>'));
        expect(sessionInfo.isValid, isTrue);
      });

      test('should calculate time until expiry correctly', () {
        final expiresAt = DateTime.now().add(const Duration(minutes: 30));
        
        final sessionInfo = SessionInfo(
          accessToken: 'token',
          refreshToken: 'refresh',
          expiresAt: expiresAt,
          isValid: true,
        );

        final timeUntilExpiry = sessionInfo.timeUntilExpiry;
        expect(timeUntilExpiry.inMinutes, closeTo(30, 1)); // Allow 1 minute tolerance
      });

      test('should detect if session will expire soon', () {
        // Session expiring in 5 minutes
        final soonExpiry = DateTime.now().add(const Duration(minutes: 5));
        final sessionSoon = SessionInfo(
          accessToken: 'token',
          refreshToken: 'refresh',
          expiresAt: soonExpiry,
          isValid: true,
        );

        expect(sessionSoon.willExpireSoon, isTrue);

        // Session expiring in 30 minutes
        final laterExpiry = DateTime.now().add(const Duration(minutes: 30));
        final sessionLater = SessionInfo(
          accessToken: 'token',
          refreshToken: 'refresh',
          expiresAt: laterExpiry,
          isValid: true,
        );

        expect(sessionLater.willExpireSoon, isFalse);
      });

      test('should format expiry time correctly', () {
        // Test expired session
        final expiredSession = SessionInfo(
          accessToken: 'token',
          refreshToken: 'refresh',
          expiresAt: DateTime.now().subtract(const Duration(minutes: 10)),
          isValid: false,
        );

        expect(expiredSession.formattedExpiryTime, equals('Expired'));

        // Test session expiring in days
        final daysSession = SessionInfo(
          accessToken: 'token',
          refreshToken: 'refresh',
          expiresAt: DateTime.now().add(const Duration(days: 2, hours: 3)),
          isValid: true,
        );

        expect(daysSession.formattedExpiryTime, contains('2d'));
        expect(daysSession.formattedExpiryTime, contains('3h'));

        // Test session expiring in hours
        final hoursSession = SessionInfo(
          accessToken: 'token',
          refreshToken: 'refresh',
          expiresAt: DateTime.now().add(const Duration(hours: 2, minutes: 30)),
          isValid: true,
        );

        expect(hoursSession.formattedExpiryTime, contains('2h'));
        expect(hoursSession.formattedExpiryTime, contains('30m'));

        // Test session expiring in minutes
        final minutesSession = SessionInfo(
          accessToken: 'token',
          refreshToken: 'refresh',
          expiresAt: DateTime.now().add(const Duration(minutes: 45)),
          isValid: true,
        );

        expect(minutesSession.formattedExpiryTime, contains('45m'));
      });
    });

    group('SessionStats', () {
      test('should create session stats with all properties', () {
        final timeUntilExpiry = const Duration(hours: 2);
        final sessionAge = const Duration(hours: 22);

        final stats = SessionStats(
          isAuthenticated: true,
          timeUntilExpiry: timeUntilExpiry,
          userId: 'user_123',
          userEmail: '<EMAIL>',
          sessionAge: sessionAge,
        );

        expect(stats.isAuthenticated, isTrue);
        expect(stats.timeUntilExpiry, equals(timeUntilExpiry));
        expect(stats.userId, equals('user_123'));
        expect(stats.userEmail, equals('<EMAIL>'));
        expect(stats.sessionAge, equals(sessionAge));
      });

      test('should format session age correctly', () {
        // Test unknown session age
        final unknownStats = SessionStats(
          isAuthenticated: true,
          sessionAge: null,
        );

        expect(unknownStats.formattedSessionAge, equals('Unknown'));

        // Test session age in days
        final daysStats = SessionStats(
          isAuthenticated: true,
          sessionAge: const Duration(days: 1, hours: 5),
        );

        expect(daysStats.formattedSessionAge, contains('1d'));
        expect(daysStats.formattedSessionAge, contains('5h'));

        // Test session age in hours
        final hoursStats = SessionStats(
          isAuthenticated: true,
          sessionAge: const Duration(hours: 3, minutes: 45),
        );

        expect(hoursStats.formattedSessionAge, contains('3h'));
        expect(hoursStats.formattedSessionAge, contains('45m'));

        // Test session age in minutes
        final minutesStats = SessionStats(
          isAuthenticated: true,
          sessionAge: const Duration(minutes: 30),
        );

        expect(minutesStats.formattedSessionAge, contains('30m'));
      });

      test('should format time until expiry correctly', () {
        // Test unknown time until expiry
        final unknownStats = SessionStats(
          isAuthenticated: true,
          timeUntilExpiry: null,
        );

        expect(unknownStats.formattedTimeUntilExpiry, equals('Unknown'));

        // Test time until expiry in days
        final daysStats = SessionStats(
          isAuthenticated: true,
          timeUntilExpiry: const Duration(days: 2, hours: 8),
        );

        expect(daysStats.formattedTimeUntilExpiry, contains('2d'));
        expect(daysStats.formattedTimeUntilExpiry, contains('8h'));

        // Test time until expiry in hours
        final hoursStats = SessionStats(
          isAuthenticated: true,
          timeUntilExpiry: const Duration(hours: 4, minutes: 20),
        );

        expect(hoursStats.formattedTimeUntilExpiry, contains('4h'));
        expect(hoursStats.formattedTimeUntilExpiry, contains('20m'));

        // Test time until expiry in minutes
        final minutesStats = SessionStats(
          isAuthenticated: true,
          timeUntilExpiry: const Duration(minutes: 15),
        );

        expect(minutesStats.formattedTimeUntilExpiry, contains('15m'));
      });
    });

    group('Static methods', () {
      // Note: These tests would require mocking the secure storage and Supabase client
      // In a real implementation, we would use packages like mockito to mock dependencies
      
      test('should handle session manager initialization', () {
        // This test would verify that SessionManager.initialize() sets up
        // the auth state listener and attempts to restore session
        expect(true, isTrue); // Placeholder
      });

      test('should handle logout correctly', () {
        // This test would verify that SessionManager.logout() clears
        // all session data and signs out from Supabase
        expect(true, isTrue); // Placeholder
      });

      test('should validate session integrity', () {
        // This test would verify that SessionManager.validateSession()
        // correctly checks if the stored session is still valid
        expect(true, isTrue); // Placeholder
      });
    });
  });
}