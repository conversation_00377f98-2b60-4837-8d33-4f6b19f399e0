import '../repositories/session_repository.dart';
import '../repositories/user_repository.dart';
import '../models/session_data.dart';
import '../models/user_profile.dart';
import 'credit_manager.dart';

/// Service for tracking user sessions and managing credit consumption
class SessionTracker {
  final SessionRepository _sessionRepository = SessionRepository();
  final UserRepository _userRepository = UserRepository();
  final CreditManager _creditManager = CreditManager();

  /// Log a user session and consume a credit
  Future<SessionTrackingResult> logSession({
    required String email,
    required String message,
    required String response,
    DateTime? timestamp,
  }) async {
    try {
      // First, try to consume a credit
      final creditResult = await _creditManager.consumeCredit(email);
      
      if (!creditResult.success) {
        return SessionTrackingResult.creditError(
          creditResult.errorMessage ?? 'Failed to consume credit',
          creditResult.type,
        );
      }

      // Create session data
      final sessionData = SessionData.create(
        email: email,
        message: message,
        response: response,
        datetime: timestamp,
      );

      // Log the session
      final loggedSession = await _sessionRepository.logSession(sessionData);

      return SessionTrackingResult.success(
        session: loggedSession,
        updatedProfile: creditResult.profile!,
        wasRefreshed: creditResult.wasRefreshed,
      );
    } catch (e) {
      return SessionTrackingResult.error('Failed to log session: $e');
    }
  }

  /// Validate if user can make a request (has credits available)
  Future<ValidationResult> validateUserCanMakeRequest(String email) async {
    try {
      final checkResult = await _creditManager.canConsumeCredit(email);
      
      if (!checkResult.canConsume) {
        return ValidationResult(
          canProceed: false,
          reason: checkResult.reason,
          currentCredits: checkResult.currentCredits,
          maxCredits: checkResult.maxCredits,
          nextRefreshDate: checkResult.nextRefreshDate,
        );
      }

      return ValidationResult(
        canProceed: true,
        reason: checkResult.reason,
        currentCredits: checkResult.currentCredits,
        maxCredits: checkResult.maxCredits,
        nextRefreshDate: checkResult.nextRefreshDate,
        needsRefresh: checkResult.needsRefresh,
      );
    } catch (e) {
      return ValidationResult(
        canProceed: false,
        reason: 'Error validating user: $e',
      );
    }
  }

  /// Get user session statistics
  Future<SessionStats> getUserSessionStats(String email) async {
    try {
      final stats = await _sessionRepository.getSessionStats(email);
      final creditStatus = await _creditManager.getCreditStatus(email);
      
      return SessionStats(
        email: email,
        totalSessions: stats['total_sessions'] ?? 0,
        totalMessageLength: stats['total_message_length'] ?? 0,
        totalResponseLength: stats['total_response_length'] ?? 0,
        averageMessageLength: stats['average_message_length']?.toDouble() ?? 0.0,
        averageResponseLength: stats['average_response_length']?.toDouble() ?? 0.0,
        firstSession: stats['first_session'],
        lastSession: stats['last_session'],
        sessionsToday: stats['sessions_today'] ?? 0,
        sessionsThisWeek: stats['sessions_this_week'] ?? 0,
        sessionsThisMonth: stats['sessions_this_month'] ?? 0,
        currentCredits: creditStatus.currentCredits,
        maxCredits: creditStatus.maxCredits,
        creditsUsed: creditStatus.maxCredits - creditStatus.currentCredits,
        nextRefreshDate: creditStatus.nextRefreshDate,
      );
    } catch (e) {
      throw Exception('Failed to get session stats: $e');
    }
  }

  /// Get recent sessions for a user
  Future<List<SessionData>> getRecentSessions(String email, {int limit = 10}) async {
    try {
      return await _sessionRepository.getUserSessions(email, limit: limit);
    } catch (e) {
      throw Exception('Failed to get recent sessions: $e');
    }
  }

  /// Get sessions within a date range
  Future<List<SessionData>> getSessionsInRange(
    String email, {
    required DateTime startDate,
    required DateTime endDate,
    int limit = 100,
  }) async {
    try {
      return await _sessionRepository.getUserSessionsInRange(
        email,
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );
    } catch (e) {
      throw Exception('Failed to get sessions in range: $e');
    }
  }

  /// Search sessions by message content
  Future<List<SessionData>> searchSessions(String email, String searchTerm) async {
    try {
      return await _sessionRepository.searchSessions(email, searchTerm);
    } catch (e) {
      throw Exception('Failed to search sessions: $e');
    }
  }

  /// Get daily session counts for analytics
  Future<Map<String, int>> getDailySessionCounts(String email) async {
    try {
      return await _sessionRepository.getDailySessionCounts(email);
    } catch (e) {
      throw Exception('Failed to get daily session counts: $e');
    }
  }

  /// Get session analytics for dashboard
  Future<SessionAnalytics> getSessionAnalytics(String email) async {
    try {
      final stats = await getUserSessionStats(email);
      final dailyCounts = await getDailySessionCounts(email);
      final recentSessions = await getRecentSessions(email, limit: 5);
      
      // Calculate trends
      final today = DateTime.now();
      final yesterday = today.subtract(const Duration(days: 1));
      final todayKey = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
      final yesterdayKey = '${yesterday.year}-${yesterday.month.toString().padLeft(2, '0')}-${yesterday.day.toString().padLeft(2, '0')}';
      
      final todaySessions = dailyCounts[todayKey] ?? 0;
      final yesterdaySessions = dailyCounts[yesterdayKey] ?? 0;
      
      double trend = 0.0;
      if (yesterdaySessions > 0) {
        trend = ((todaySessions - yesterdaySessions) / yesterdaySessions) * 100;
      } else if (todaySessions > 0) {
        trend = 100.0; // 100% increase from 0
      }

      return SessionAnalytics(
        stats: stats,
        dailyCounts: dailyCounts,
        recentSessions: recentSessions,
        todaySessions: todaySessions,
        yesterdaySessions: yesterdaySessions,
        trend: trend,
        peakDay: _findPeakDay(dailyCounts),
        averageSessionsPerDay: _calculateAverageSessionsPerDay(dailyCounts),
      );
    } catch (e) {
      throw Exception('Failed to get session analytics: $e');
    }
  }

  /// Handle session logging failure gracefully
  Future<void> handleSessionLoggingFailure({
    required String email,
    required String message,
    required String response,
    required String error,
  }) async {
    try {
      // Log the failure for investigation (could be sent to error tracking service)
      print('Session logging failed for $email: $error');
      
      // Could implement retry logic here
      // Could store failed sessions locally for retry later
      // Could send to error tracking service
      
      // For now, just log the error
    } catch (e) {
      print('Failed to handle session logging failure: $e');
    }
  }

  /// Check if user has reached daily session limit (if implemented)
  Future<bool> hasReachedDailyLimit(String email, {int dailyLimit = 100}) async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));
      
      final todaySessions = await getSessionsInRange(
        email,
        startDate: startOfDay,
        endDate: endOfDay,
      );
      
      return todaySessions.length >= dailyLimit;
    } catch (e) {
      // If we can't check, assume they haven't reached the limit
      return false;
    }
  }

  /// Get usage summary for user
  Future<UsageSummary> getUsageSummary(String email) async {
    try {
      final stats = await getUserSessionStats(email);
      final creditStatus = await _creditManager.getCreditStatus(email);
      
      return UsageSummary(
        email: email,
        totalSessions: stats.totalSessions,
        creditsUsed: stats.creditsUsed,
        creditsRemaining: stats.currentCredits,
        creditsPercentageUsed: creditStatus.creditsUsagePercentage,
        nextRefreshDate: stats.nextRefreshDate,
        daysUntilRefresh: creditStatus.daysUntilRefresh,
        averageSessionsPerDay: stats.totalSessions > 0 
            ? stats.totalSessions / (DateTime.now().difference(stats.firstSession ?? DateTime.now()).inDays + 1)
            : 0.0,
        status: creditStatus.status,
      );
    } catch (e) {
      throw Exception('Failed to get usage summary: $e');
    }
  }

  /// Find the day with most sessions
  String _findPeakDay(Map<String, int> dailyCounts) {
    if (dailyCounts.isEmpty) return 'No data';
    
    var maxCount = 0;
    var peakDay = '';
    
    dailyCounts.forEach((day, count) {
      if (count > maxCount) {
        maxCount = count;
        peakDay = day;
      }
    });
    
    return peakDay.isNotEmpty ? peakDay : 'No data';
  }

  /// Calculate average sessions per day
  double _calculateAverageSessionsPerDay(Map<String, int> dailyCounts) {
    if (dailyCounts.isEmpty) return 0.0;
    
    final totalSessions = dailyCounts.values.fold(0, (sum, count) => sum + count);
    return totalSessions / dailyCounts.length;
  }
}

/// Result of session tracking operation
class SessionTrackingResult {
  final bool success;
  final SessionData? session;
  final UserProfile? updatedProfile;
  final String? errorMessage;
  final bool wasRefreshed;
  final SessionTrackingResultType type;
  final CreditResultType? creditResultType;

  const SessionTrackingResult._({
    required this.success,
    this.session,
    this.updatedProfile,
    this.errorMessage,
    this.wasRefreshed = false,
    required this.type,
    this.creditResultType,
  });

  factory SessionTrackingResult.success({
    required SessionData session,
    required UserProfile updatedProfile,
    bool wasRefreshed = false,
  }) {
    return SessionTrackingResult._(
      success: true,
      session: session,
      updatedProfile: updatedProfile,
      wasRefreshed: wasRefreshed,
      type: SessionTrackingResultType.success,
    );
  }

  factory SessionTrackingResult.creditError(String message, CreditResultType creditType) {
    return SessionTrackingResult._(
      success: false,
      errorMessage: message,
      type: SessionTrackingResultType.creditError,
      creditResultType: creditType,
    );
  }

  factory SessionTrackingResult.error(String message) {
    return SessionTrackingResult._(
      success: false,
      errorMessage: message,
      type: SessionTrackingResultType.error,
    );
  }
}

enum SessionTrackingResultType { success, creditError, error }

/// Result of user validation
class ValidationResult {
  final bool canProceed;
  final String reason;
  final int currentCredits;
  final int maxCredits;
  final DateTime? nextRefreshDate;
  final bool needsRefresh;

  const ValidationResult({
    required this.canProceed,
    required this.reason,
    this.currentCredits = 0,
    this.maxCredits = 0,
    this.nextRefreshDate,
    this.needsRefresh = false,
  });
}

/// Session statistics
class SessionStats {
  final String email;
  final int totalSessions;
  final int totalMessageLength;
  final int totalResponseLength;
  final double averageMessageLength;
  final double averageResponseLength;
  final DateTime? firstSession;
  final DateTime? lastSession;
  final int sessionsToday;
  final int sessionsThisWeek;
  final int sessionsThisMonth;
  final int currentCredits;
  final int maxCredits;
  final int creditsUsed;
  final DateTime nextRefreshDate;

  const SessionStats({
    required this.email,
    required this.totalSessions,
    required this.totalMessageLength,
    required this.totalResponseLength,
    required this.averageMessageLength,
    required this.averageResponseLength,
    this.firstSession,
    this.lastSession,
    required this.sessionsToday,
    required this.sessionsThisWeek,
    required this.sessionsThisMonth,
    required this.currentCredits,
    required this.maxCredits,
    required this.creditsUsed,
    required this.nextRefreshDate,
  });
}

/// Session analytics with trends
class SessionAnalytics {
  final SessionStats stats;
  final Map<String, int> dailyCounts;
  final List<SessionData> recentSessions;
  final int todaySessions;
  final int yesterdaySessions;
  final double trend;
  final String peakDay;
  final double averageSessionsPerDay;

  const SessionAnalytics({
    required this.stats,
    required this.dailyCounts,
    required this.recentSessions,
    required this.todaySessions,
    required this.yesterdaySessions,
    required this.trend,
    required this.peakDay,
    required this.averageSessionsPerDay,
  });
}

/// Usage summary for user
class UsageSummary {
  final String email;
  final int totalSessions;
  final int creditsUsed;
  final int creditsRemaining;
  final double creditsPercentageUsed;
  final DateTime nextRefreshDate;
  final int daysUntilRefresh;
  final double averageSessionsPerDay;
  final String status;

  const UsageSummary({
    required this.email,
    required this.totalSessions,
    required this.creditsUsed,
    required this.creditsRemaining,
    required this.creditsPercentageUsed,
    required this.nextRefreshDate,
    required this.daysUntilRefresh,
    required this.averageSessionsPerDay,
    required this.status,
  });

  /// Get formatted usage message
  String get formattedUsage {
    return '$creditsUsed of ${creditsUsed + creditsRemaining} credits used (${(creditsPercentageUsed * 100).toStringAsFixed(1)}%)';
  }

  /// Get usage status color
  Color get usageColor {
    if (creditsPercentageUsed >= 0.9) {
      return const Color(0xFFF44336); // Red
    } else if (creditsPercentageUsed >= 0.7) {
      return const Color(0xFFFF9800); // Orange
    } else {
      return const Color(0xFF4CAF50); // Green
    }
  }
}