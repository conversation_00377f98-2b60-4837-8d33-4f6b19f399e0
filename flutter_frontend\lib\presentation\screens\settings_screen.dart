import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../widgets/common/gradient_background.dart';
import '../../core/constants/colors.dart';
import '../../core/constants/gradients.dart';
import '../../core/constants/text_styles.dart';
import '../../data/models/audio_config.dart';

/// Settings screen for app configuration matching Expo version
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  void initState() {
    super.initState();
    
    // Initialize settings provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SettingsProvider>().initialize();
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: Safe<PERSON>rea(
          child: Column(
            children: [
              // Header
              _buildHeader(context),
              
              // Settings content
              Expanded(
                child: Consumer<SettingsProvider>(
                  builder: (context, settingsProvider, child) {
                    if (!settingsProvider.isInitialized) {
                      return _buildLoadingState();
                    }
                    
                    return SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          // General settings
                          _buildGeneralSettings(settingsProvider),
                          
                          const SizedBox(height: 16),
                          
                          // Audio settings
                          _buildAudioSettings(settingsProvider),
                          
                          const SizedBox(height: 16),
                          
                          // Language settings
                          _buildLanguageSettings(settingsProvider),
                          
                          const SizedBox(height: 16),
                          
                          // Privacy settings
                          _buildPrivacySettings(settingsProvider),
                          
                          const SizedBox(height: 16),
                          
                          // About section
                          _buildAboutSection(),
                          
                          const SizedBox(height: 16),
                          
                          // Reset and logout
                          _buildActionButtons(settingsProvider),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// Build header with close button
  Widget _buildHeader(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppGradients.headerGradient,
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(
                Icons.close,
                color: AppColors.lightText,
                size: 24,
              ),
              tooltip: 'Close',
            ),
            
            Expanded(
              child: Text(
                'Settings',
                style: AppTextStyles.headerTitle,
                textAlign: TextAlign.center,
              ),
            ),
            
            // Reset button
            IconButton(
              onPressed: () => _showResetDialog(context),
              icon: const Icon(
                Icons.refresh,
                color: AppColors.lightText,
                size: 24,
              ),
              tooltip: 'Reset Settings',
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build loading state
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.infoBlue),
          ),
          SizedBox(height: 16),
          Text(
            'Loading settings...',
            style: AppTextStyles.messageText,
          ),
        ],
      ),
    );
  }
  
  /// Build general settings section
  Widget _buildGeneralSettings(SettingsProvider settingsProvider) {
    return _buildSettingsSection(
      title: 'General',
      icon: Icons.settings,
      children: [
        _buildSwitchTile(
          title: 'Notifications',
          subtitle: 'Receive learning reminders and updates',
          value: settingsProvider.notificationsEnabled,
          onChanged: () => settingsProvider.toggleNotifications(),
          icon: Icons.notifications,
        ),
        
        _buildSwitchTile(
          title: 'Sound Effects',
          subtitle: 'Play sounds for interactions and feedback',
          value: settingsProvider.soundEnabled,
          onChanged: () => settingsProvider.toggleSound(),
          icon: Icons.volume_up,
        ),
        
        _buildSwitchTile(
          title: 'Haptic Feedback',
          subtitle: 'Vibrate on button presses and interactions',
          value: settingsProvider.hapticFeedbackEnabled,
          onChanged: () => settingsProvider.toggleHapticFeedback(),
          icon: Icons.vibration,
        ),
        
        _buildSwitchTile(
          title: 'Auto-scroll',
          subtitle: 'Automatically scroll to new messages',
          value: settingsProvider.autoScrollEnabled,
          onChanged: () => settingsProvider.toggleAutoScroll(),
          icon: Icons.swap_vert,
        ),
      ],
    );
  }
  
  /// Build audio settings section
  Widget _buildAudioSettings(SettingsProvider settingsProvider) {
    return _buildSettingsSection(
      title: 'Audio & Recording',
      icon: Icons.mic,
      children: [
        _buildSwitchTile(
          title: 'Voice Recording',
          subtitle: 'Enable microphone for speech recognition',
          value: settingsProvider.voiceRecordingEnabled,
          onChanged: () => settingsProvider.toggleVoiceRecording(),
          icon: Icons.mic,
        ),
        
        _buildSwitchTile(
          title: 'Auto-correct',
          subtitle: 'Automatically apply grammar corrections',
          value: settingsProvider.autoCorrectEnabled,
          onChanged: () => settingsProvider.toggleAutoCorrect(),
          icon: Icons.spellcheck,
        ),
        
        _buildSwitchTile(
          title: 'Transcription Hints',
          subtitle: 'Show helpful hints during transcription',
          value: settingsProvider.showTranscriptionHints,
          onChanged: () => settingsProvider.toggleTranscriptionHints(),
          icon: Icons.help_outline,
        ),
        
        _buildTile(
          title: 'Audio Quality',
          subtitle: 'Configure recording quality settings',
          icon: Icons.high_quality,
          onTap: () => _showAudioQualityDialog(context, settingsProvider),
          trailing: const Icon(
            Icons.chevron_right,
            color: AppColors.lightText,
          ),
        ),
      ],
    );
  }
  
  /// Build language settings section
  Widget _buildLanguageSettings(SettingsProvider settingsProvider) {
    return _buildSettingsSection(
      title: 'Language & Region',
      icon: Icons.language,
      children: [
        _buildTile(
          title: 'Learning Language',
          subtitle: settingsProvider.language,
          icon: Icons.translate,
          onTap: () => _showLanguageDialog(context, settingsProvider),
          trailing: const Icon(
            Icons.chevron_right,
            color: AppColors.lightText,
          ),
        ),
        
        _buildTile(
          title: 'App Theme',
          subtitle: _getThemeDisplayName(settingsProvider.theme),
          icon: Icons.palette,
          onTap: () => _showThemeDialog(context, settingsProvider),
          trailing: const Icon(
            Icons.chevron_right,
            color: AppColors.lightText,
          ),
        ),
      ],
    );
  }
  
  /// Build privacy settings section
  Widget _buildPrivacySettings(SettingsProvider settingsProvider) {
    return _buildSettingsSection(
      title: 'Privacy & Data',
      icon: Icons.privacy_tip,
      children: [
        _buildTile(
          title: 'Data Usage',
          subtitle: 'View and manage your data usage',
          icon: Icons.data_usage,
          onTap: () => _showDataUsageDialog(context),
          trailing: const Icon(
            Icons.chevron_right,
            color: AppColors.lightText,
          ),
        ),
        
        _buildTile(
          title: 'Privacy Policy',
          subtitle: 'Read our privacy policy',
          icon: Icons.policy,
          onTap: () => _openPrivacyPolicy(),
          trailing: const Icon(
            Icons.open_in_new,
            color: AppColors.lightText,
            size: 20,
          ),
        ),
        
        _buildTile(
          title: 'Terms of Service',
          subtitle: 'Read our terms of service',
          icon: Icons.description,
          onTap: () => _openTermsOfService(),
          trailing: const Icon(
            Icons.open_in_new,
            color: AppColors.lightText,
            size: 20,
          ),
        ),
      ],
    );
  }
  
  /// Build about section
  Widget _buildAboutSection() {
    return _buildSettingsSection(
      title: 'About',
      icon: Icons.info,
      children: [
        _buildTile(
          title: 'App Version',
          subtitle: '1.0.0 (Build 1)',
          icon: Icons.info_outline,
          onTap: null,
        ),
        
        _buildTile(
          title: 'Help & Support',
          subtitle: 'Get help and contact support',
          icon: Icons.help,
          onTap: () => _openSupport(),
          trailing: const Icon(
            Icons.chevron_right,
            color: AppColors.lightText,
          ),
        ),
        
        _buildTile(
          title: 'Rate App',
          subtitle: 'Rate DeutschKorrekt in the app store',
          icon: Icons.star,
          onTap: () => _rateApp(),
          trailing: const Icon(
            Icons.open_in_new,
            color: AppColors.lightText,
            size: 20,
          ),
        ),
      ],
    );
  }
  
  /// Build action buttons section
  Widget _buildActionButtons(SettingsProvider settingsProvider) {
    return Column(
      children: [
        // Export settings
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _exportSettings(settingsProvider),
            icon: const Icon(Icons.download),
            label: const Text('Export Settings'),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Reset settings
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _showResetDialog(context),
            icon: const Icon(Icons.refresh),
            label: const Text('Reset to Defaults'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.warningYellow,
              side: const BorderSide(color: AppColors.warningYellow),
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Logout button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => _showLogoutDialog(context),
            icon: const Icon(Icons.logout),
            label: const Text('Sign Out'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
              foregroundColor: AppColors.white,
            ),
          ),
        ),
      ],
    );
  }
  
  /// Build settings section container
  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.slate800.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.slate600.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: AppGradients.correctionContainerGradient,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: AppColors.lightText,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: AppTextStyles.modalSubtitle,
                ),
              ],
            ),
          ),
          
          // Section content
          ...children,
        ],
      ),
    );
  }
  
  /// Build switch tile
  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required VoidCallback onChanged,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.slate600,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.lightText.withOpacity(0.7),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.messageText.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: AppTextStyles.captionText,
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: (_) => onChanged(),
            activeColor: AppColors.infoBlue,
          ),
        ],
      ),
    );
  }
  
  /// Build regular tile
  Widget _buildTile({
    required String title,
    required String subtitle,
    required IconData icon,
    VoidCallback? onTap,
    Widget? trailing,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: AppColors.slate600,
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppColors.lightText.withOpacity(0.7),
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.messageText.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: AppTextStyles.captionText,
                  ),
                ],
              ),
            ),
            if (trailing != null) trailing,
          ],
        ),
      ),
    );
  }
  
  /// Get theme display name
  String _getThemeDisplayName(String theme) {
    switch (theme) {
      case 'dark':
        return 'Dark';
      case 'light':
        return 'Light';
      case 'system':
        return 'System';
      default:
        return 'Dark';
    }
  }
  
  /// Show language selection dialog
  void _showLanguageDialog(BuildContext context, SettingsProvider settingsProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.slate800,
        title: Text(
          'Learning Language',
          style: AppTextStyles.modalTitle,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: settingsProvider.getAvailableLanguages()
              .map((language) => RadioListTile<String>(
                title: Text(
                  language,
                  style: AppTextStyles.messageText,
                ),
                value: language,
                groupValue: settingsProvider.language,
                onChanged: (value) {
                  if (value != null) {
                    settingsProvider.setLanguage(value);
                    Navigator.pop(context);
                  }
                },
                activeColor: AppColors.infoBlue,
              ))
              .toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: AppTextStyles.smallButtonText.copyWith(
                color: AppColors.lightText,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// Show theme selection dialog
  void _showThemeDialog(BuildContext context, SettingsProvider settingsProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.slate800,
        title: Text(
          'App Theme',
          style: AppTextStyles.modalTitle,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: settingsProvider.getAvailableThemes()
              .map((theme) => RadioListTile<String>(
                title: Text(
                  _getThemeDisplayName(theme),
                  style: AppTextStyles.messageText,
                ),
                value: theme,
                groupValue: settingsProvider.theme,
                onChanged: (value) {
                  if (value != null) {
                    settingsProvider.setTheme(value);
                    Navigator.pop(context);
                  }
                },
                activeColor: AppColors.infoBlue,
              ))
              .toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: AppTextStyles.smallButtonText.copyWith(
                color: AppColors.lightText,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// Show audio quality dialog
  void _showAudioQualityDialog(BuildContext context, SettingsProvider settingsProvider) {
    showDialog(
      context: context,
      builder: (context) => _AudioQualityDialog(settingsProvider: settingsProvider),
    );
  }
  
  /// Show data usage dialog
  void _showDataUsageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.slate800,
        title: Text(
          'Data Usage',
          style: AppTextStyles.modalTitle,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Audio data is processed securely and not stored permanently.',
              style: AppTextStyles.messageText,
            ),
            const SizedBox(height: 8),
            Text(
              'All voice recordings are deleted after processing.',
              style: AppTextStyles.captionText,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'OK',
              style: AppTextStyles.smallButtonText.copyWith(
                color: AppColors.infoBlue,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// Show reset dialog
  void _showResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.slate800,
        title: Text(
          'Reset Settings',
          style: AppTextStyles.modalTitle,
        ),
        content: Text(
          'This will reset all settings to their default values. This action cannot be undone.',
          style: AppTextStyles.messageText,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: AppTextStyles.smallButtonText.copyWith(
                color: AppColors.lightText,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<SettingsProvider>().resetToDefaults();
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.warningYellow,
              foregroundColor: AppColors.black,
            ),
            child: Text(
              'Reset',
              style: AppTextStyles.smallButtonText,
            ),
          ),
        ],
      ),
    );
  }
  
  /// Show logout dialog
  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.slate800,
        title: Text(
          'Sign Out',
          style: AppTextStyles.modalTitle,
        ),
        content: Text(
          'Are you sure you want to sign out? Your progress will be saved.',
          style: AppTextStyles.messageText,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: AppTextStyles.smallButtonText.copyWith(
                color: AppColors.lightText,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement logout functionality
              Navigator.pop(context);
              Navigator.pop(context); // Close settings
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
              foregroundColor: AppColors.white,
            ),
            child: Text(
              'Sign Out',
              style: AppTextStyles.smallButtonText,
            ),
          ),
        ],
      ),
    );
  }
  
  /// Export settings
  void _exportSettings(SettingsProvider settingsProvider) {
    final settings = settingsProvider.exportSettings();
    // TODO: Implement settings export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Settings exported successfully',
          style: AppTextStyles.captionText,
        ),
        backgroundColor: AppColors.successGreen,
      ),
    );
  }
  
  /// Open privacy policy
  void _openPrivacyPolicy() {
    // TODO: Open privacy policy URL
  }
  
  /// Open terms of service
  void _openTermsOfService() {
    // TODO: Open terms of service URL
  }
  
  /// Open support
  void _openSupport() {
    // TODO: Open support/help URL
  }
  
  /// Rate app
  void _rateApp() {
    // TODO: Open app store rating
  }
}

/// Audio quality settings dialog
class _AudioQualityDialog extends StatefulWidget {
  final SettingsProvider settingsProvider;
  
  const _AudioQualityDialog({required this.settingsProvider});

  @override
  State<_AudioQualityDialog> createState() => _AudioQualityDialogState();
}

class _AudioQualityDialogState extends State<_AudioQualityDialog> {
  late AudioConfig _config;
  
  @override
  void initState() {
    super.initState();
    _config = widget.settingsProvider.audioConfig;
  }
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppColors.slate800,
      title: Text(
        'Audio Quality',
        style: AppTextStyles.modalTitle,
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Sample rate
          _buildDropdownField(
            label: 'Sample Rate',
            value: _config.sampleRate,
            items: widget.settingsProvider.getAvailableSampleRates(),
            onChanged: (value) {
              setState(() {
                _config = _config.copyWith(sampleRate: value);
              });
            },
            suffix: 'Hz',
          ),
          
          const SizedBox(height: 16),
          
          // Channels
          _buildDropdownField(
            label: 'Channels',
            value: _config.channels,
            items: widget.settingsProvider.getAvailableChannels(),
            onChanged: (value) {
              setState(() {
                _config = _config.copyWith(channels: value);
              });
            },
            suffix: _config.channels == 1 ? 'Mono' : 'Stereo',
          ),
          
          const SizedBox(height: 16),
          
          // Chunk size
          _buildDropdownField(
            label: 'Chunk Size',
            value: _config.chunkSize,
            items: widget.settingsProvider.getAvailableChunkSizes(),
            onChanged: (value) {
              setState(() {
                _config = _config.copyWith(chunkSize: value);
              });
            },
            suffix: 'samples',
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: AppTextStyles.smallButtonText.copyWith(
              color: AppColors.lightText,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () {
            widget.settingsProvider.updateAudioConfig(_config);
            Navigator.pop(context);
          },
          child: Text(
            'Save',
            style: AppTextStyles.smallButtonText,
          ),
        ),
      ],
    );
  }
  
  Widget _buildDropdownField({
    required String label,
    required int value,
    required List<int> items,
    required ValueChanged<int> onChanged,
    String? suffix,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.captionText,
        ),
        const SizedBox(height: 4),
        DropdownButtonFormField<int>(
          value: value,
          style: AppTextStyles.messageText,
          dropdownColor: AppColors.slate700,
          decoration: const InputDecoration(
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: items
              .map((item) => DropdownMenuItem(
                value: item,
                child: Text('$item${suffix != null ? ' $suffix' : ''}'),
              ))
              .toList(),
          onChanged: (newValue) {
            if (newValue != null) {
              onChanged(newValue);
            }
          },
        ),
      ],
    );
  }
}