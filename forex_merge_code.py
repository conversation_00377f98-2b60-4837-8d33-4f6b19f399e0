import pandas as pd

# Assuming you have your dataframes: EURUSD_forex and ox_securities

# Step 1: Convert date columns to datetime format
EURUSD_forex['date'] = pd.to_datetime(EURUSD_forex['date'])
ox_securities['Date'] = pd.to_datetime(ox_securities['Date'])

# Step 1.5: Normalize timezones - remove timezone info to make them compatible
# Convert both to timezone-naive datetime (removes timezone info)
if EURUSD_forex['date'].dt.tz is not None:
    EURUSD_forex['date'] = EURUSD_forex['date'].dt.tz_localize(None)
if ox_securities['Date'].dt.tz is not None:
    ox_securities['Date'] = ox_securities['Date'].dt.tz_localize(None)

# Step 2: Convert numeric columns to float
EURUSD_forex['adjusted_close'] = pd.to_numeric(EURUSD_forex['adjusted_close'], errors='coerce')
ox_securities['Profits'] = pd.to_numeric(ox_securities['Profits'], errors='coerce')

# Step 3: Merge the dataframes to add exchange_rate column
# Using left join to keep all ox_securities records
ox_securities = ox_securities.merge(
    EURUSD_forex[['date', 'adjusted_close']],
    left_on='Date',
    right_on='date',
    how='left'
)

# Step 4: Rename the adjusted_close column to exchange_rate
ox_securities = ox_securities.rename(columns={'adjusted_close': 'exchange_rate'})

# Step 5: Drop the duplicate date column from the merge
ox_securities = ox_securities.drop(columns=['date'])

# Step 6: Create euro_conversion column
ox_securities['euro_conversion'] = ox_securities['Profits'] * ox_securities['exchange_rate']

# Display the results
print("Updated ox_securities dataframe:")
print(ox_securities.head())
print(f"\nDataframe shape: {ox_securities.shape}")
print(f"\nColumns: {list(ox_securities.columns)}")

# Check for any missing exchange rates (dates not found in forex data)
missing_rates = ox_securities['exchange_rate'].isna().sum()
if missing_rates > 0:
    print(f"\nWarning: {missing_rates} rows have missing exchange rates")
    print("Dates with missing exchange rates:")
    print(ox_securities[ox_securities['exchange_rate'].isna()]['Date'].unique())
