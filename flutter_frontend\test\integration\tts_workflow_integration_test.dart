import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:deutschkorrekt_flutter/core/utils/sentence_extractor.dart';
import 'package:deutschkorrekt_flutter/data/services/audio_cache_manager.dart';
import 'package:deutschkorrekt_flutter/data/services/tts_audio_service.dart';
import 'package:deutschkorrekt_flutter/presentation/widgets/chat/audio_icon_widget.dart';
import 'package:deutschkorrekt_flutter/presentation/widgets/chat/message_bubble.dart';
import 'package:deutschkorrekt_flutter/data/models/message.dart';

import 'tts_workflow_integration_test.mocks.dart';

// Generate mocks for integration testing
@GenerateMocks([http.Client, AudioCacheManager])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('TTS Workflow Integration Tests', () {
    late MockClient mockHttpClient;
    late MockAudioCacheManager mockCacheManager;
    late TTSAudioService ttsService;
    
    setUp(() {
      mockHttpClient = MockClient();
      mockCacheManager = MockAudioCacheManager();
      ttsService = TTSAudioService(
        httpClient: mockHttpClient,
        cacheManager: mockCacheManager,
      );
    });
    
    tearDown(() {
      ttsService.dispose();
    });
    
    group('End-to-End TTS Workflow', () {
      testWidgets('should complete full TTS workflow from sentence extraction to audio playback', (WidgetTester tester) async {
        // Step 1: Test sentence extraction from Groq response
        const groqResponse = 'Here is the analysis: {Hallo Welt! Wie geht es dir heute?} Additional context follows.';
        final extractedSentence = SentenceExtractor.extractFirstSentenceFromBraces(groqResponse);
        
        expect(extractedSentence, equals('Hallo Welt!'));
        expect(SentenceExtractor.isValidForTTS(extractedSentence), isTrue);
        
        // Step 2: Create message with extracted sentence
        final message = Message(
          id: 'integration_test_message',
          text: 'AI response with TTS capability',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: extractedSentence,
        );
        
        // Step 3: Mock cache miss (first-time request)
        when(mockCacheManager.getCachedAudio('integration_test_message'))
            .thenAnswer((_) async => null);
        
        // Step 4: Mock successful backend TTS response
        final audioData = Uint8List.fromList(List.generate(1024, (i) => i % 256));
        final responseBody = {
          'audio_data': base64.encode(audioData),
          'content_type': 'audio/mpeg',
          'duration_seconds': 2.5,
          'processing_time': 0.8,
        };
        
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode(responseBody),
          200,
        ));
        
        // Step 5: Mock successful caching
        when(mockCacheManager.cacheAudio('integration_test_message', audioData))
            .thenAnswer((_) async {});
        when(mockCacheManager.getCachedAudio('integration_test_message'))
            .thenAnswer((_) async => '/cached/audio.mp3');
        
        // Step 6: Render message bubble with audio icon
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        // Step 7: Verify UI components are rendered correctly
        expect(find.text('AI response with TTS capability'), findsOneWidget);
        expect(find.byType(AudioIconWidget), findsOneWidget);
        
        // Step 8: Verify audio icon is in idle state initially
        final audioIconWidget = tester.widget<AudioIconWidget>(
          find.byType(AudioIconWidget),
        );
        expect(audioIconWidget.initialState, equals(AudioIconState.idle));
        expect(audioIconWidget.isFirstTime, isTrue);
        
        // Step 9: Simulate first-time audio icon tap
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        // Step 10: Verify TTS service interactions
        verify(mockCacheManager.getCachedAudio('integration_test_message')).called(greaterThan(0));
        verify(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).called(1);
        verify(mockCacheManager.cacheAudio('integration_test_message', audioData)).called(1);
        
        // Step 11: Verify request payload
        final captured = verify(mockHttpClient.post(
          captureAny,
          headers: captureAnyNamed('headers'),
          body: captureAnyNamed('body'),
        )).captured;
        
        final requestBody = json.decode(captured[2] as String) as Map<String, dynamic>;
        expect(requestBody['text'], equals('Hallo Welt!'));
        expect(requestBody['message_id'], equals('integration_test_message'));
        expect(requestBody['voice_config'], equals('de-DE-Chirp3-HD-Aoede'));
      });
      
      testWidgets('should handle cached audio playback on subsequent taps', (WidgetTester tester) async {
        // Setup message with extracted sentence
        final message = Message(
          id: 'cached_test_message',
          text: 'AI response with cached audio',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: 'Cached audio test!',
        );
        
        // Mock cache hit (subsequent request)
        when(mockCacheManager.getCachedAudio('cached_test_message'))
            .thenAnswer((_) async => '/cached/audio.mp3');
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        // Simulate audio icon tap
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        // Verify cache was checked and no backend request was made
        verify(mockCacheManager.getCachedAudio('cached_test_message')).called(greaterThan(0));
        verifyNever(mockHttpClient.post(any, headers: anyNamed('headers'), body: anyNamed('body')));
      });
      
      testWidgets('should handle TTS errors gracefully with user feedback', (WidgetTester tester) async {
        final message = Message(
          id: 'error_test_message',
          text: 'AI response that will fail TTS',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: 'Error test sentence!',
        );
        
        // Mock cache miss
        when(mockCacheManager.getCachedAudio('error_test_message'))
            .thenAnswer((_) async => null);
        
        // Mock backend error
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode({
            'error_message': 'TTS service unavailable',
            'error_code': 'SERVICE_UNAVAILABLE',
          }),
          503,
        ));
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        // Tap audio icon to trigger error
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        // Allow time for error handling
        await tester.pump(const Duration(milliseconds: 500));
        
        // Verify error was handled
        verify(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).called(greaterThan(0));
      });
    });
    
    group('Cache Management Integration', () {
      testWidgets('should clear old cache when new Groq response arrives', (WidgetTester tester) async {
        // Mock cache stats
        when(mockCacheManager.getCacheStats()).thenAnswer((_) async => {
          'fileCount': 3,
          'totalSizeBytes': 1024000,
        });
        when(mockCacheManager.clearOldCache()).thenAnswer((_) async {});
        
        // Simulate new Groq response cleanup
        final result = await ttsService.onNewGroqResponse();
        
        expect(result['success'], isTrue);
        verify(mockCacheManager.clearOldCache()).called(1);
        verify(mockCacheManager.getCacheStats()).called(2); // Before and after
      });
      
      testWidgets('should handle cache errors during cleanup', (WidgetTester tester) async {
        // Mock cache error
        when(mockCacheManager.getCacheStats()).thenThrow(Exception('Cache error'));
        
        final result = await ttsService.onNewGroqResponse();
        
        expect(result['success'], isFalse);
        expect(result['error'], isNotNull);
      });
    });
    
    group('Error Recovery Integration', () {
      testWidgets('should retry TTS request after cache error', (WidgetTester tester) async {
        final message = Message(
          id: 'retry_test_message',
          text: 'AI response for retry test',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: 'Retry test sentence!',
        );
        
        // Mock initial cache error, then success
        when(mockCacheManager.getCachedAudio('retry_test_message'))
            .thenThrow(Exception('Cache error'))
            .thenAnswer((_) async => null);
        
        // Mock cache validation
        when(mockCacheManager.validateAndCleanCache()).thenAnswer((_) async {});
        
        // Mock successful backend response
        final audioData = Uint8List.fromList([1, 2, 3, 4, 5]);
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode({
            'audio_data': base64.encode(audioData),
            'content_type': 'audio/mpeg',
            'duration_seconds': 2.0,
          }),
          200,
        ));
        
        when(mockCacheManager.cacheAudio('retry_test_message', audioData))
            .thenAnswer((_) async {});
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        // Tap audio icon to trigger error recovery
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        // Verify cache validation was attempted
        verify(mockCacheManager.validateAndCleanCache()).called(1);
      });
      
      testWidgets('should handle network timeout with retry mechanism', (WidgetTester tester) async {
        final message = Message(
          id: 'timeout_test_message',
          text: 'AI response for timeout test',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: 'Timeout test sentence!',
        );
        
        // Mock cache miss
        when(mockCacheManager.getCachedAudio('timeout_test_message'))
            .thenAnswer((_) async => null);
        
        // Mock timeout error
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenThrow(TimeoutException('Request timeout', const Duration(seconds: 10)));
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        // Tap audio icon to trigger timeout
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        // Allow time for retries
        await tester.pump(const Duration(seconds: 1));
        
        // Verify multiple retry attempts were made
        verify(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).called(3); // Max retry attempts
      });
    });
    
    group('Performance Integration', () {
      testWidgets('should handle multiple concurrent TTS requests', (WidgetTester tester) async {
        final messages = List.generate(3, (index) => Message(
          id: 'concurrent_message_$index',
          text: 'Concurrent message $index',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: 'Concurrent sentence $index!',
        ));
        
        // Mock cache misses for all messages
        for (int i = 0; i < 3; i++) {
          when(mockCacheManager.getCachedAudio('concurrent_message_$i'))
              .thenAnswer((_) async => null);
        }
        
        // Mock successful backend responses
        final audioData = Uint8List.fromList([1, 2, 3, 4, 5]);
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode({
            'audio_data': base64.encode(audioData),
            'content_type': 'audio/mpeg',
            'duration_seconds': 1.0,
          }),
          200,
        ));
        
        // Mock caching for all messages
        for (int i = 0; i < 3; i++) {
          when(mockCacheManager.cacheAudio('concurrent_message_$i', audioData))
              .thenAnswer((_) async {});
          when(mockCacheManager.getCachedAudio('concurrent_message_$i'))
              .thenAnswer((_) async => '/cached/audio_$i.mp3');
        }
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Column(
                children: messages.map((message) => MessageBubble(message: message)).toList(),
              ),
            ),
          ),
        );
        
        // Find all audio icons
        final audioIcons = find.byType(AudioIconWidget);
        expect(audioIcons, findsNWidgets(3));
        
        // Tap all audio icons concurrently
        for (int i = 0; i < 3; i++) {
          await tester.tap(audioIcons.at(i));
        }
        await tester.pump();
        
        // Allow time for all requests to complete
        await tester.pump(const Duration(seconds: 1));
        
        // Verify all requests were handled
        verify(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).called(3);
      });
      
      testWidgets('should manage cache size efficiently', (WidgetTester tester) async {
        // Mock cache stats showing large cache
        when(mockCacheManager.getCacheStats()).thenAnswer((_) async => {
          'fileCount': 50,
          'totalSizeBytes': 100 * 1024 * 1024, // 100MB
          'totalSizeKB': 100 * 1024,
        });
        
        // Get service stats to check cache management
        final stats = await ttsService.getServiceStats();
        
        expect(stats['cacheStats'], isNotNull);
        expect(stats['cacheStats']['fileCount'], equals(50));
        expect(stats['cacheStats']['totalSizeBytes'], equals(100 * 1024 * 1024));
      });
    });
    
    group('Sentence Extraction Integration', () {
      testWidgets('should extract and validate sentences from various Groq response formats', (WidgetTester tester) async {
        final testCases = [
          {
            'input': 'Analysis: {Guten Morgen! Wie geht es Ihnen?} Context follows.',
            'expected': 'Guten Morgen!',
            'valid': true,
          },
          {
            'input': 'Response: {Sehr gut, danke.} Additional info.',
            'expected': 'Sehr gut, danke.',
            'valid': true,
          },
          {
            'input': 'Result: {123} Numbers only.',
            'expected': '123',
            'valid': false,
          },
          {
            'input': 'Empty: {} Nothing here.',
            'expected': null,
            'valid': false,
          },
          {
            'input': 'No braces here at all.',
            'expected': null,
            'valid': false,
          },
        ];
        
        for (final testCase in testCases) {
          final extracted = SentenceExtractor.extractFirstSentenceFromBraces(
            testCase['input'] as String,
          );
          final isValid = SentenceExtractor.isValidForTTS(extracted);
          
          expect(extracted, equals(testCase['expected']));
          expect(isValid, equals(testCase['valid']));
          
          // Test integration with message bubble
          if (extracted != null && isValid) {
            final message = Message(
              id: 'extraction_test_${testCase.hashCode}',
              text: 'Test message',
              isUser: false,
              timestamp: DateTime.now(),
              extractedSentence: extracted,
            );
            
            await tester.pumpWidget(
              MaterialApp(
                home: Scaffold(
                  body: MessageBubble(message: message),
                ),
              ),
            );
            
            // Should show audio icon for valid sentences
            expect(find.byType(AudioIconWidget), findsOneWidget);
            
            // Clean up for next test
            await tester.pumpWidget(Container());
          }
        }
      });
    });
  });
}