/// WebSocket utility functions for message parsing and connection management
class WebSocketUtils {
  /// Backend WebSocket URL matching Expo version
  static const String backendUrl = 'wss://deutschkorrekt-backend-645996191396.europe-west3.run.app/stt';
  
  /// Parse WebSocket message type from string
  static WebSocketMessageType parseMessageType(String type) {
    switch (type) {
      case 'partial_transcript':
        return WebSocketMessageType.partialTranscript;
      case 'final_transcript':
        return WebSocketMessageType.finalTranscript;
      case 'processing':
        return WebSocketMessageType.processing;
      case 'groq_response':
        return WebSocketMessageType.groqResponse;
      case 'error':
        return WebSocketMessageType.error;
      case 'timeout':
        return WebSocketMessageType.timeout;
      case 'info':
        return WebSocketMessageType.info;
      case 'session_started':
        return WebSocketMessageType.sessionStarted;
      default:
        return WebSocketMessageType.info;
    }
  }
}

/// WebSocket message types matching backend protocol
enum WebSocketMessageType {
  partialTranscript,
  finalTranscript,
  processing,
  groqResponse,
  error,
  timeout,
  info,
  sessionStarted,
}