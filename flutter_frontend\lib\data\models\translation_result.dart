/// Model for translation results
class TranslationResult {
  final String originalText;
  final String translatedText;
  final String sourceLanguage;
  final String targetLanguage;
  final double confidence;
  final DateTime timestamp;
  
  const TranslationResult({
    required this.originalText,
    required this.translatedText,
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.confidence,
    required this.timestamp,
  });
  
  /// Create from JSON response
  factory TranslationResult.fromJson(Map<String, dynamic> json) {
    return TranslationResult(
      originalText: json['original_text'] ?? '',
      translatedText: json['translated_text'] ?? '',
      sourceLanguage: json['source_language'] ?? 'de',
      targetLanguage: json['target_language'] ?? 'en',
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      timestamp: DateTime.now(),
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'original_text': originalText,
      'translated_text': translatedText,
      'source_language': sourceLanguage,
      'target_language': targetLanguage,
      'confidence': confidence,
      'timestamp': timestamp.toIso8601String(),
    };
  }
  
  /// Check if translation has high confidence
  bool get hasHighConfidence => confidence > 0.8;
  
  @override
  String toString() {
    return 'TranslationResult(original: $originalText, translated: $translatedText, confidence: $confidence)';
  }
}