#!/usr/bin/env python3
"""
Keep-alive script to prevent Cloud Run cold starts.
Run this every 5 minutes via Cloud Scheduler.
"""
import requests
import time

def keep_warm():
    """Send requests to keep instances warm"""
    base_url = "https://deutschkorrekt-backend-645996191396.europe-west3.run.app"
    
    endpoints = [
        "/health",
        "/warmup"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            print(f"✅ {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")
        
        time.sleep(1)

if __name__ == "__main__":
    keep_warm()