import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:deutschkorrekt_flutter/presentation/widgets/profile/profile_popup.dart';
import 'package:deutschkorrekt_flutter/presentation/providers/auth_provider.dart';
import 'package:deutschkorrekt_flutter/presentation/providers/enhanced_profile_provider.dart';
import 'package:deutschkorrekt_flutter/data/models/user_profile.dart';

void main() {
  group('ProfilePopup', () {
    late AuthProvider mockAuthProvider;
    late EnhancedProfileProvider mockProfileProvider;

    setUp(() {
      mockAuthProvider = AuthProvider();
      mockProfileProvider = EnhancedProfileProvider();
    });

    tearDown(() {
      mockAuthProvider.dispose();
      mockProfileProvider.dispose();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: Scaffold(
          body: MultiProvider(
            providers: [
              ChangeNotifierProvider<AuthProvider>.value(value: mockAuthProvider),
              ChangeNotifierProvider<EnhancedProfileProvider>.value(value: mockProfileProvider),
            ],
            child: const ProfilePopup(),
          ),
        ),
      );
    }

    testWidgets('should display profile popup with header', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Profile'), findsOneWidget);
      expect(find.byIcon(Icons.person), findsOneWidget);
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('should close popup when close button is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Tap the close button
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      // The dialog should be closed (no longer find the Profile text)
      expect(find.text('Profile'), findsNothing);
    });

    testWidgets('should display profile header section', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Should find profile header elements
      expect(find.text('No email'), findsOneWidget); // Default when no user
      expect(find.text('Unknown'), findsOneWidget); // Default plan
    });

    testWidgets('should display credits section', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Credits'), findsOneWidget);
      expect(find.byIcon(Icons.account_balance_wallet), findsOneWidget);
    });

    testWidgets('should display statistics section', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Statistics'), findsOneWidget);
      expect(find.byIcon(Icons.analytics), findsOneWidget);
    });

    testWidgets('should display action buttons', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Refresh Profile'), findsOneWidget);
      expect(find.text('Settings'), findsOneWidget);
      expect(find.text('Help & Support'), findsOneWidget);
      expect(find.text('Sign Out'), findsOneWidget);
    });

    testWidgets('should show loading state when loading stats', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Should show loading indicator for stats
      expect(find.text('Loading statistics...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsWidgets);
    });

    testWidgets('should display user information when profile is loaded', (WidgetTester tester) async {
      // Set up mock profile
      final mockProfile = UserProfile(
        email: '<EMAIL>',
        plan: 'Premium',
        dateJoined: DateTime(2024, 1, 15),
        datePlan: DateTime(2024, 1, 15),
        maxCredits: 50,
        currentCredits: 30,
      );

      // Note: In a real test, we would need to mock the providers properly
      // This is a simplified version showing the test structure

      await tester.pumpWidget(createTestWidget());

      // The actual assertions would depend on how we mock the providers
      // For now, we're testing the widget structure
      expect(find.byType(ProfilePopup), findsOneWidget);
    });

    testWidgets('should handle refresh button tap', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find and tap refresh button
      await tester.tap(find.text('Refresh Profile'));
      await tester.pumpAndSettle();

      // Should handle the refresh action
      // In a real test, we would verify the provider method was called
    });

    testWidgets('should show settings dialog when settings button is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Tap settings button
      await tester.tap(find.text('Settings'));
      await tester.pumpAndSettle();

      // Should show settings dialog
      expect(find.text('Settings'), findsWidgets); // One in button, one in dialog
      expect(find.text('Notifications'), findsOneWidget);
      expect(find.text('Language'), findsOneWidget);
      expect(find.text('Dark Mode'), findsOneWidget);
    });

    testWidgets('should show help dialog when help button is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Tap help button
      await tester.tap(find.text('Help & Support'));
      await tester.pumpAndSettle();

      // Should show help dialog
      expect(find.text('DeutschKorrekt Help'), findsOneWidget);
      expect(find.text('Credits refresh monthly'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
    });

    testWidgets('should show logout confirmation dialog', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Tap sign out button
      await tester.tap(find.text('Sign Out'));
      await tester.pumpAndSettle();

      // Should show confirmation dialog
      expect(find.text('Are you sure you want to sign out?'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Sign Out'), findsWidgets); // One in button, one in dialog
    });

    testWidgets('should cancel logout when cancel is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Tap sign out button
      await tester.tap(find.text('Sign Out'));
      await tester.pumpAndSettle();

      // Tap cancel in confirmation dialog
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Should still be in profile popup
      expect(find.text('Profile'), findsOneWidget);
    });
  });
}