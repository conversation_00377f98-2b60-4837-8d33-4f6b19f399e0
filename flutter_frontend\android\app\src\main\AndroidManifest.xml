<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Permissions for microphone access -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <!-- Audio focus and session management -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Hardware features -->
    <uses-feature
        android:name="android.hardware.microphone"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.audio.low_latency"
        android:required="false" />
    
    <application
        android:label="DeutschKorrekt"
        android:name="${applicationName}">
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
              
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>