import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/config/supabase_config.dart';

/// Authentication service for handling Supabase Auth operations
class AuthService {
  static final SupabaseClient _client = SupabaseConfig.client;

  /// Sign in with email and password
  Future<AuthResponse> signInWithEmail(String email, String password) async {
    try {
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      return response;
    } catch (e) {
      throw Exception('Failed to sign in: $e');
    }
  }

  /// Sign up with email and password
  Future<AuthResponse> signUpWithEmail(String email, String password) async {
    try {
      final response = await _client.auth.signUp(
        email: email,
        password: password,
      );
      
      return response;
    } catch (e) {
      throw Exception('Failed to sign up: $e');
    }
  }

  /// Sign in with Google OAuth
  Future<bool> signInWithGoogle() async {
    try {
      final response = await _client.auth.signInWithOAuth(
        Provider.google,
        redirectTo: 'io.supabase.deutschkorrekt://login-callback/',
      );
      
      return response;
    } catch (e) {
      throw Exception('Failed to sign in with Google: $e');
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    try {
      await _client.auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: $e');
    }
  }

  /// Get current user
  User? getCurrentUser() {
    return _client.auth.currentUser;
  }

  /// Check if user is authenticated
  bool isAuthenticated() {
    return getCurrentUser() != null;
  }

  /// Get current session
  Session? getCurrentSession() {
    return _client.auth.currentSession;
  }

  /// Refresh current session
  Future<AuthResponse> refreshSession() async {
    try {
      final response = await _client.auth.refreshSession();
      return response;
    } catch (e) {
      throw Exception('Failed to refresh session: $e');
    }
  }

  /// Listen to auth state changes
  Stream<AuthState> get authStateChanges {
    return _client.auth.onAuthStateChange;
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _client.auth.resetPasswordForEmail(email);
    } catch (e) {
      throw Exception('Failed to reset password: $e');
    }
  }

  /// Update user password
  Future<UserResponse> updatePassword(String newPassword) async {
    try {
      final response = await _client.auth.updateUser(
        UserAttributes(password: newPassword),
      );
      return response;
    } catch (e) {
      throw Exception('Failed to update password: $e');
    }
  }

  /// Update user email
  Future<UserResponse> updateEmail(String newEmail) async {
    try {
      final response = await _client.auth.updateUser(
        UserAttributes(email: newEmail),
      );
      return response;
    } catch (e) {
      throw Exception('Failed to update email: $e');
    }
  }

  /// Resend email confirmation
  Future<void> resendEmailConfirmation(String email) async {
    try {
      await _client.auth.resend(
        type: OtpType.signup,
        email: email,
      );
    } catch (e) {
      throw Exception('Failed to resend email confirmation: $e');
    }
  }

  /// Validate email format
  static bool isValidEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  /// Validate password strength
  static bool isValidPassword(String password) {
    // Minimum 8 characters
    return password.length >= 8;
  }

  /// Get detailed password requirements
  static Map<String, bool> getPasswordRequirements(String password) {
    return {
      'minLength': password.length >= 8,
      'hasLetter': password.contains(RegExp(r'[a-zA-Z]')),
      'hasNumber': password.contains(RegExp(r'[0-9]')),
      'hasSpecialChar': password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]')),
    };
  }

  /// Get user-friendly error message
  static String getErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('invalid login credentials')) {
      return 'Invalid email or password. Please check your credentials and try again.';
    } else if (errorString.contains('email not confirmed')) {
      return 'Please check your email and click the confirmation link before signing in.';
    } else if (errorString.contains('user already registered')) {
      return 'An account with this email already exists. Please sign in instead.';
    } else if (errorString.contains('password should be at least')) {
      return 'Password must be at least 8 characters long.';
    } else if (errorString.contains('invalid email')) {
      return 'Please enter a valid email address.';
    } else if (errorString.contains('network')) {
      return 'Network error. Please check your internet connection and try again.';
    } else if (errorString.contains('rate limit')) {
      return 'Too many attempts. Please wait a few minutes before trying again.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Check if session is expired
  bool isSessionExpired() {
    final session = getCurrentSession();
    if (session == null) return true;
    
    final expiresAt = DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000);
    return DateTime.now().isAfter(expiresAt);
  }

  /// Get time until session expires
  Duration? getTimeUntilExpiry() {
    final session = getCurrentSession();
    if (session == null) return null;
    
    final expiresAt = DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000);
    final now = DateTime.now();
    
    if (now.isAfter(expiresAt)) return Duration.zero;
    return expiresAt.difference(now);
  }
}