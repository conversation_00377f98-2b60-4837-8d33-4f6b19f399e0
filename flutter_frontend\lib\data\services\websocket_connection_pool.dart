import 'dart:async';
import 'dart:collection';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import '../models/app_error.dart';
import '../models/websocket_message.dart';

/// WebSocket connection pool for improved performance and reliability
/// Manages multiple connections and load balancing
class WebSocketConnectionPool {
  static final WebSocketConnectionPool _instance = WebSocketConnectionPool._();
  static WebSocketConnectionPool get instance => _instance;
  
  final List<WebSocketChannel> _connections = [];
  final Queue<WebSocketChannel> _availableConnections = Queue<WebSocketChannel>();
  final Map<WebSocketChannel, bool> _connectionStatus = {};
  final Map<WebSocketChannel, DateTime> _lastUsed = {};
  
  StreamController<WebSocketMessage>? _messageController;
  StreamController<AppError>? _errorController;
  StreamController<bool>? _connectionController;
  
  Timer? _healthCheckTimer;
  Timer? _cleanupTimer;
  
  int _maxConnections = 3;
  int _currentRoundRobin = 0;
  bool _isInitialized = false;
  bool _isDisposed = false;
  
  // Connection configuration
  static const String backendUrl = 'wss://deutschkorrekt-backend-645996191396.europe-west3.run.app/stt';
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration healthCheckInterval = Duration(seconds: 30);
  static const Duration cleanupInterval = Duration(minutes: 5);
  static const Duration maxIdleTime = Duration(minutes: 10);
  
  WebSocketConnectionPool._();
  
  /// Stream of WebSocket messages from any connection
  Stream<WebSocketMessage> get messageStream => 
      _messageController?.stream ?? const Stream.empty();
  
  /// Stream of connection errors
  Stream<AppError> get errorStream => 
      _errorController?.stream ?? const Stream.empty();
  
  /// Stream of overall connection status
  Stream<bool> get connectionStream => 
      _connectionController?.stream ?? const Stream.empty();
  
  /// Whether the pool has at least one active connection
  bool get isConnected => _availableConnections.isNotEmpty;
  
  /// Number of active connections
  int get activeConnections => _availableConnections.length;
  
  /// Maximum number of connections in the pool
  int get maxConnections => _maxConnections;
  
  /// Initialize the connection pool
  Future<void> initialize({int maxConnections = 3}) async {
    if (_isDisposed) {
      throw StateError('WebSocketConnectionPool has been disposed');
    }
    
    if (_isInitialized) return;
    
    _maxConnections = maxConnections;
    
    _messageController = StreamController<WebSocketMessage>.broadcast();
    _errorController = StreamController<AppError>.broadcast();
    _connectionController = StreamController<bool>.broadcast();
    
    // Start health check timer
    _healthCheckTimer = Timer.periodic(healthCheckInterval, (_) => _performHealthCheck());
    
    // Start cleanup timer
    _cleanupTimer = Timer.periodic(cleanupInterval, (_) => _performCleanup());
    
    // Create initial connections
    await _createInitialConnections();
    
    _isInitialized = true;
    
    if (kDebugMode) {
      print('WebSocketConnectionPool initialized with $_maxConnections max connections');
    }
  }
  
  /// Create initial connections to the pool
  Future<void> _createInitialConnections() async {
    final initialConnections = (_maxConnections / 2).ceil(); // Start with half capacity
    
    for (int i = 0; i < initialConnections; i++) {
      try {
        await _createConnection();
      } catch (e) {
        if (kDebugMode) {
          print('Failed to create initial connection $i: $e');
        }
      }
    }
    
    _updateConnectionStatus();
  }
  
  /// Create a new WebSocket connection
  Future<WebSocketChannel> _createConnection() async {
    try {
      final channel = WebSocketChannel.connect(
        Uri.parse(backendUrl),
        protocols: ['websocket'],
      );
      
      // Wait for connection to be established
      await channel.ready.timeout(connectionTimeout);
      
      _connections.add(channel);
      _availableConnections.add(channel);
      _connectionStatus[channel] = true;
      _lastUsed[channel] = DateTime.now();
      
      // Set up message handling for this connection
      _setupConnectionHandling(channel);
      
      if (kDebugMode) {
        print('Created new WebSocket connection (${_connections.length}/${_maxConnections})');
      }
      
      return channel;
      
    } catch (e) {
      final error = AppError.websocketConnectionFailed(
        details: 'Failed to create WebSocket connection: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
      rethrow;
    }
  }
  
  /// Set up message handling for a connection
  void _setupConnectionHandling(WebSocketChannel channel) {
    channel.stream.listen(
      (data) {
        _lastUsed[channel] = DateTime.now();
        
        try {
          if (data is String) {
            final message = WebSocketMessage.fromJsonString(data);
            _messageController?.add(message);
          } else {
            if (kDebugMode) {
              print('Received non-string data from WebSocket: ${data.runtimeType}');
            }
          }
        } catch (e) {
          final error = AppError.websocketMessageParsingFailed(
            details: 'Failed to parse WebSocket message: $e',
            originalException: e is Exception ? e : Exception(e.toString()),
          );
          _errorController?.add(error);
        }
      },
      onError: (error) {
        _handleConnectionError(channel, error);
      },
      onDone: () {
        _handleConnectionClosed(channel);
      },
    );
  }
  
  /// Handle connection error
  void _handleConnectionError(WebSocketChannel channel, dynamic error) {
    if (kDebugMode) {
      print('WebSocket connection error: $error');
    }
    
    _markConnectionAsFailed(channel);
    
    final appError = AppError.websocketConnectionFailed(
      details: 'WebSocket connection error: $error',
      originalException: error is Exception ? error : Exception(error.toString()),
    );
    _errorController?.add(appError);
    
    // Try to create a replacement connection
    _createReplacementConnection();
  }
  
  /// Handle connection closed
  void _handleConnectionClosed(WebSocketChannel channel) {
    if (kDebugMode) {
      print('WebSocket connection closed');
    }
    
    _markConnectionAsFailed(channel);
    
    // Try to create a replacement connection
    _createReplacementConnection();
  }
  
  /// Mark a connection as failed and remove it from available pool
  void _markConnectionAsFailed(WebSocketChannel channel) {
    _connectionStatus[channel] = false;
    _availableConnections.remove(channel);
    _updateConnectionStatus();
  }
  
  /// Create a replacement connection when one fails
  Future<void> _createReplacementConnection() async {
    if (_connections.length < _maxConnections) {
      try {
        await _createConnection();
        _updateConnectionStatus();
      } catch (e) {
        if (kDebugMode) {
          print('Failed to create replacement connection: $e');
        }
      }
    }
  }
  
  /// Get the best available connection using round-robin
  WebSocketChannel? _getBestConnection() {
    if (_availableConnections.isEmpty) {
      return null;
    }
    
    // Simple round-robin selection
    final connections = _availableConnections.toList();
    final connection = connections[_currentRoundRobin % connections.length];
    _currentRoundRobin = (_currentRoundRobin + 1) % connections.length;
    
    _lastUsed[connection] = DateTime.now();
    return connection;
  }
  
  /// Send audio data using the best available connection
  Future<bool> sendAudioData(Uint8List audioData) async {
    final connection = _getBestConnection();
    if (connection == null) {
      final error = AppError.websocketConnectionFailed(
        details: 'No available WebSocket connections for sending audio data',
      );
      _errorController?.add(error);
      return false;
    }
    
    try {
      connection.sink.add(audioData);
      return true;
    } catch (e) {
      _handleConnectionError(connection, e);
      return false;
    }
  }
  
  /// Send a text message using the best available connection
  Future<bool> sendMessage(String message) async {
    final connection = _getBestConnection();
    if (connection == null) {
      final error = AppError.websocketConnectionFailed(
        details: 'No available WebSocket connections for sending message',
      );
      _errorController?.add(error);
      return false;
    }
    
    try {
      connection.sink.add(message);
      return true;
    } catch (e) {
      _handleConnectionError(connection, e);
      return false;
    }
  }
  
  /// Perform health check on all connections
  void _performHealthCheck() {
    if (!_isInitialized) return;
    
    final now = DateTime.now();
    final connectionsToCheck = List<WebSocketChannel>.from(_connections);
    
    for (final connection in connectionsToCheck) {
      final lastUsed = _lastUsed[connection];
      if (lastUsed != null && now.difference(lastUsed) > maxIdleTime) {
        // Connection has been idle too long, close it
        _closeConnection(connection);
      }
    }
    
    // Ensure we have minimum connections
    if (_availableConnections.length < (_maxConnections / 2).ceil()) {
      _createReplacementConnection();
    }
  }
  
  /// Perform cleanup of old connections
  void _performCleanup() {
    if (!_isInitialized) return;
    
    final connectionsToRemove = <WebSocketChannel>[];
    
    for (final connection in _connections) {
      if (_connectionStatus[connection] == false) {
        connectionsToRemove.add(connection);
      }
    }
    
    for (final connection in connectionsToRemove) {
      _removeConnection(connection);
    }
  }
  
  /// Close a specific connection
  void _closeConnection(WebSocketChannel connection) {
    try {
      connection.sink.close();
    } catch (e) {
      if (kDebugMode) {
        print('Error closing connection: $e');
      }
    }
    
    _markConnectionAsFailed(connection);
  }
  
  /// Remove a connection from all tracking
  void _removeConnection(WebSocketChannel connection) {
    _connections.remove(connection);
    _availableConnections.remove(connection);
    _connectionStatus.remove(connection);
    _lastUsed.remove(connection);
  }
  
  /// Update overall connection status
  void _updateConnectionStatus() {
    final wasConnected = _connectionController?.hasListener == true && 
                        _connectionController!.isClosed == false;
    final isNowConnected = _availableConnections.isNotEmpty;
    
    if (wasConnected != isNowConnected) {
      _connectionController?.add(isNowConnected);
    }
  }
  
  /// Get connection pool statistics
  Map<String, dynamic> getStats() {
    return {
      'total_connections': _connections.length,
      'available_connections': _availableConnections.length,
      'max_connections': _maxConnections,
      'is_connected': isConnected,
      'current_round_robin': _currentRoundRobin,
    };
  }
  
  /// Dispose of the connection pool and clean up resources
  Future<void> dispose() async {
    if (_isDisposed) return;
    
    _isDisposed = true;
    
    // Cancel timers
    _healthCheckTimer?.cancel();
    _cleanupTimer?.cancel();
    
    // Close all connections
    for (final connection in _connections) {
      try {
        await connection.sink.close();
      } catch (e) {
        if (kDebugMode) {
          print('Error closing connection during dispose: $e');
        }
      }
    }
    
    // Clear all collections
    _connections.clear();
    _availableConnections.clear();
    _connectionStatus.clear();
    _lastUsed.clear();
    
    // Close streams
    await _messageController?.close();
    await _errorController?.close();
    await _connectionController?.close();
    
    _isInitialized = false;
    
    if (kDebugMode) {
      print('WebSocketConnectionPool disposed');
    }
  }
}
