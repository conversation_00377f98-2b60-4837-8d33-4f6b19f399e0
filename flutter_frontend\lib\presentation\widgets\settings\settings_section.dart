import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/gradients.dart';
import '../../../core/constants/text_styles.dart';

/// Settings section container with header and grouped items
class SettingsSection extends StatelessWidget {
  final String title;
  final IconData icon;
  final List<Widget> children;
  final String? subtitle;
  final Widget? trailing;
  final bool isCollapsible;
  final bool initiallyExpanded;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  
  const SettingsSection({
    super.key,
    required this.title,
    required this.icon,
    required this.children,
    this.subtitle,
    this.trailing,
    this.isCollapsible = false,
    this.initiallyExpanded = true,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    if (isCollapsible) {
      return _buildCollapsibleSection();
    } else {
      return _buildStaticSection();
    }
  }
  
  Widget _buildStaticSection() {
    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.slate800.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.slate600.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          ...children,
        ],
      ),
    );
  }
  
  Widget _buildCollapsibleSection() {
    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.slate800.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.slate600.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: ExpansionTile(
        initiallyExpanded: initiallyExpanded,
        tilePadding: const EdgeInsets.all(16),
        childrenPadding: EdgeInsets.zero,
        backgroundColor: Colors.transparent,
        collapsedBackgroundColor: Colors.transparent,
        iconColor: AppColors.lightText,
        collapsedIconColor: AppColors.lightText.withOpacity(0.7),
        title: Row(
          children: [
            Icon(
              icon,
              color: AppColors.lightText,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.modalSubtitle,
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      subtitle!,
                      style: AppTextStyles.captionText,
                    ),
                  ],
                ],
              ),
            ),
            if (trailing != null) trailing!,
          ],
        ),
        children: children,
      ),
    );
  }
  
  Widget _buildHeader() {
    return Container(
      padding: padding ?? const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        gradient: AppGradients.correctionContainerGradient,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.lightText,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.modalSubtitle,
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    subtitle!,
                    style: AppTextStyles.captionText,
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) trailing!,
        ],
      ),
    );
  }
}

/// Animated settings section with smooth transitions
class AnimatedSettingsSection extends StatefulWidget {
  final String title;
  final IconData icon;
  final List<Widget> children;
  final String? subtitle;
  final Widget? trailing;
  final bool initiallyExpanded;
  final Duration animationDuration;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  
  const AnimatedSettingsSection({
    super.key,
    required this.title,
    required this.icon,
    required this.children,
    this.subtitle,
    this.trailing,
    this.initiallyExpanded = true,
    this.animationDuration = const Duration(milliseconds: 300),
    this.padding,
    this.margin,
  });

  @override
  State<AnimatedSettingsSection> createState() => _AnimatedSettingsSectionState();
}

class _AnimatedSettingsSectionState extends State<AnimatedSettingsSection>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _expandAnimation;
  late Animation<double> _rotationAnimation;
  bool _isExpanded = true;
  
  @override
  void initState() {
    super.initState();
    
    _isExpanded = widget.initiallyExpanded;
    
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _expandAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    if (_isExpanded) {
      _controller.value = 1.0;
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin ?? const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.slate800.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.slate600.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Header
          GestureDetector(
            onTap: _toggleExpanded,
            child: Container(
              padding: widget.padding ?? const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                gradient: AppGradients.correctionContainerGradient,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    widget.icon,
                    color: AppColors.lightText,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.title,
                          style: AppTextStyles.modalSubtitle,
                        ),
                        if (widget.subtitle != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            widget.subtitle!,
                            style: AppTextStyles.captionText,
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (widget.trailing != null) ...[
                    widget.trailing!,
                    const SizedBox(width: 8),
                  ],
                  RotationTransition(
                    turns: _rotationAnimation,
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: AppColors.lightText,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Expandable content
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: Column(
              children: widget.children,
            ),
          ),
        ],
      ),
    );
  }
}

/// Settings section with badge
class BadgeSettingsSection extends StatelessWidget {
  final String title;
  final IconData icon;
  final List<Widget> children;
  final String? subtitle;
  final String badgeText;
  final Color badgeColor;
  final bool isCollapsible;
  final bool initiallyExpanded;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  
  const BadgeSettingsSection({
    super.key,
    required this.title,
    required this.icon,
    required this.children,
    this.subtitle,
    required this.badgeText,
    this.badgeColor = AppColors.infoBlue,
    this.isCollapsible = false,
    this.initiallyExpanded = true,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return SettingsSection(
      title: title,
      icon: icon,
      subtitle: subtitle,
      isCollapsible: isCollapsible,
      initiallyExpanded: initiallyExpanded,
      padding: padding,
      margin: margin,
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: badgeColor.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: badgeColor.withOpacity(0.5),
            width: 1,
          ),
        ),
        child: Text(
          badgeText,
          style: AppTextStyles.captionText.copyWith(
            color: badgeColor,
            fontSize: 11,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      children: children,
    );
  }
}

/// Settings section with status indicator
class StatusSettingsSection extends StatelessWidget {
  final String title;
  final IconData icon;
  final List<Widget> children;
  final String? subtitle;
  final SectionStatus status;
  final bool isCollapsible;
  final bool initiallyExpanded;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  
  const StatusSettingsSection({
    super.key,
    required this.title,
    required this.icon,
    required this.children,
    this.subtitle,
    required this.status,
    this.isCollapsible = false,
    this.initiallyExpanded = true,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return SettingsSection(
      title: title,
      icon: icon,
      subtitle: subtitle,
      isCollapsible: isCollapsible,
      initiallyExpanded: initiallyExpanded,
      padding: padding,
      margin: margin,
      trailing: Container(
        width: 12,
        height: 12,
        decoration: BoxDecoration(
          color: _getStatusColor(status),
          shape: BoxShape.circle,
        ),
      ),
      children: children,
    );
  }
  
  Color _getStatusColor(SectionStatus status) {
    switch (status) {
      case SectionStatus.active:
        return AppColors.successGreen;
      case SectionStatus.inactive:
        return AppColors.lightText.withOpacity(0.3);
      case SectionStatus.warning:
        return AppColors.warningYellow;
      case SectionStatus.error:
        return AppColors.errorRed;
    }
  }
}

/// Section status enumeration
enum SectionStatus {
  active,
  inactive,
  warning,
  error,
}