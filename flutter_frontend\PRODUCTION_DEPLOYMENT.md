# DeutschKorrekt Production Deployment Guide

This document provides comprehensive instructions for deploying the DeutschKorrekt Flutter app to production environments.

## Pre-Deployment Checklist

### Code Quality
- [ ] All tests passing (unit, widget, integration, E2E)
- [ ] Code coverage meets minimum thresholds (85%+)
- [ ] No critical or high-severity linting issues
- [ ] Performance benchmarks within acceptable limits
- [ ] Memory leak testing completed
- [ ] Security audit completed

### Configuration
- [ ] Production API endpoints configured
- [ ] SSL certificate pinning implemented
- [ ] Error reporting service configured
- [ ] Analytics tracking implemented
- [ ] Crash reporting enabled
- [ ] Performance monitoring setup

### Assets & Resources
- [ ] App icons generated for all platforms and sizes
- [ ] Splash screens configured
- [ ] Localization files complete
- [ ] Audio processing optimized
- [ ] Network timeouts configured appropriately

### Legal & Compliance
- [ ] Privacy policy updated
- [ ] Terms of service finalized
- [ ] GDPR compliance verified
- [ ] App store metadata prepared
- [ ] Screenshots and promotional materials ready

## Android Production Build

### Build Configuration

#### `android/app/build.gradle`
```gradle
android {
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId "com.deutschkorrekt.app"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        
        // Enable multidex for large apps
        multiDexEnabled true
        
        // Optimize for production
        proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            useProguard true
            
            // Enable R8 optimization
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

#### ProGuard Rules (`android/app/proguard-rules.pro`)
```proguard
# Flutter specific rules
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Audio processing
-keep class com.deutschkorrekt.** { *; }
-keep class * extends android.media.** { *; }

# WebSocket
-keep class okhttp3.** { *; }
-keep class okio.** { *; }

# Prevent obfuscation of model classes
-keep class * extends java.io.Serializable { *; }
```

### Signing Configuration

#### Generate Keystore
```bash
keytool -genkey -v -keystore deutschkorrekt-release-key.keystore -alias deutschkorrekt -keyalg RSA -keysize 2048 -validity 10000
```

#### `android/key.properties`
```properties
storePassword=<store_password>
keyPassword=<key_password>
keyAlias=deutschkorrekt
storeFile=deutschkorrekt-release-key.keystore
```

### Build Commands

```bash
# Clean build
flutter clean
flutter pub get

# Build release APK
flutter build apk --release --target-platform android-arm64

# Build App Bundle (recommended for Play Store)
flutter build appbundle --release

# Build for multiple architectures
flutter build apk --release --split-per-abi
```

### Play Store Deployment

#### App Bundle Upload
1. Generate signed App Bundle
2. Upload to Google Play Console
3. Configure release tracks (internal → alpha → beta → production)
4. Set up staged rollout (5% → 20% → 50% → 100%)

#### Store Listing Requirements
- App title: "DeutschKorrekt - German Language Learning"
- Short description: "AI-powered German pronunciation and grammar correction"
- Full description: Detailed feature list and benefits
- Screenshots: 8 high-quality screenshots showing key features
- Feature graphic: 1024x500 promotional image
- App icon: 512x512 high-resolution icon

## iOS Production Build

### Build Configuration

#### `ios/Runner.xcodeproj/project.pbxproj`
- Set deployment target to iOS 12.0+
- Configure release scheme
- Enable bitcode for App Store optimization
- Set up automatic signing with distribution certificate

#### `ios/Runner/Info.plist` Production Settings
```xml
<key>CFBundleDisplayName</key>
<string>DeutschKorrekt</string>

<key>CFBundleVersion</key>
<string>$(FLUTTER_BUILD_NUMBER)</string>

<key>CFBundleShortVersionString</key>
<string>$(FLUTTER_BUILD_NAME)</string>

<!-- Production API endpoints -->
<key>API_BASE_URL</key>
<string>https://deutschkorrekt-backend-645996191396.europe-west3.run.app</string>

<!-- Disable debug features -->
<key>FLUTTER_DEBUG</key>
<false/>
```

### Code Signing

#### Certificates Required
1. iOS Distribution Certificate
2. App Store Distribution Provisioning Profile
3. Push Notification Certificate (if using notifications)

#### Xcode Configuration
1. Select "Automatically manage signing" for development
2. Use manual signing for distribution
3. Configure release scheme with distribution profile

### Build Commands

```bash
# Clean build
flutter clean
flutter pub get

# Build iOS release
flutter build ios --release

# Build with specific configuration
flutter build ios --release --flavor production
```

### App Store Deployment

#### TestFlight Beta Testing
1. Archive app in Xcode
2. Upload to App Store Connect
3. Configure TestFlight testing groups
4. Distribute to internal and external testers

#### App Store Submission
1. Complete App Store Connect metadata
2. Upload final build
3. Submit for review
4. Monitor review status and respond to feedback

## Environment Configuration

### Production Environment Variables

#### Flutter Environment
```dart
// lib/config/environment.dart
class Environment {
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'https://deutschkorrekt-backend-645996191396.europe-west3.run.app',
  );
  
  static const bool isProduction = bool.fromEnvironment(
    'PRODUCTION',
    defaultValue: false,
  );
  
  static const String sentryDsn = String.fromEnvironment('SENTRY_DSN');
}
```

#### Build-time Configuration
```bash
# Build with production environment
flutter build apk --release --dart-define=PRODUCTION=true --dart-define=API_BASE_URL=https://api.deutschkorrekt.com
```

### Feature Flags

```dart
// lib/config/feature_flags.dart
class FeatureFlags {
  static const bool enableAdvancedAudioProcessing = bool.fromEnvironment(
    'ENABLE_ADVANCED_AUDIO',
    defaultValue: true,
  );
  
  static const bool enableAnalytics = bool.fromEnvironment(
    'ENABLE_ANALYTICS',
    defaultValue: true,
  );
}
```

## Monitoring & Analytics

### Crash Reporting (Sentry)

#### Setup
```dart
// lib/main.dart
import 'package:sentry_flutter/sentry_flutter.dart';

Future<void> main() async {
  await SentryFlutter.init(
    (options) {
      options.dsn = Environment.sentryDsn;
      options.environment = Environment.isProduction ? 'production' : 'development';
      options.tracesSampleRate = 0.1;
    },
    appRunner: () => runApp(MyApp()),
  );
}
```

### Performance Monitoring

#### Firebase Performance
```dart
// lib/services/performance_service.dart
class PerformanceService {
  static final FirebasePerformance _performance = FirebasePerformance.instance;
  
  static Future<void> trackAudioProcessingTime() async {
    final trace = _performance.newTrace('audio_processing');
    await trace.start();
    
    // Audio processing code
    
    await trace.stop();
  }
}
```

### Analytics (Firebase Analytics)

```dart
// lib/services/analytics_service.dart
class AnalyticsService {
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  
  static Future<void> logRecordingStarted() async {
    await _analytics.logEvent(
      name: 'recording_started',
      parameters: {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }
}
```

## Security Configuration

### Network Security

#### Certificate Pinning
```dart
// lib/services/http_service.dart
class HttpService {
  static final Dio _dio = Dio();
  
  static void configureCertificatePinning() {
    (_dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
      client.badCertificateCallback = (cert, host, port) {
        // Implement certificate pinning logic
        return _validateCertificate(cert, host);
      };
      return client;
    };
  }
}
```

#### API Security
```dart
// lib/services/api_service.dart
class ApiService {
  static const Map<String, String> _securityHeaders = {
    'X-API-Version': '1.0',
    'X-Client-Platform': 'flutter',
    'X-Client-Version': '1.0.0',
  };
  
  static Future<Response> secureRequest(String endpoint, Map<String, dynamic> data) async {
    return await _dio.post(
      endpoint,
      data: data,
      options: Options(headers: _securityHeaders),
    );
  }
}
```

### Data Protection

#### Sensitive Data Handling
```dart
// lib/services/secure_storage_service.dart
class SecureStorageService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: IOSAccessibility.first_unlock_this_device,
    ),
  );
  
  static Future<void> storeSecurely(String key, String value) async {
    await _storage.write(key: key, value: value);
  }
}
```

## Performance Optimization

### Build Optimization

#### Flutter Build Flags
```bash
# Optimize for size
flutter build apk --release --tree-shake-icons --split-debug-info=debug-info

# Optimize for performance
flutter build apk --release --profile --dart-define=FLUTTER_WEB_USE_SKIA=true
```

#### Asset Optimization
```yaml
# pubspec.yaml
flutter:
  assets:
    - assets/images/
  
  # Optimize images
  uses-material-design: true
  generate: true
```

### Runtime Optimization

#### Memory Management
```dart
// lib/services/memory_service.dart
class MemoryService {
  static void optimizeMemoryUsage() {
    // Force garbage collection
    GC.collect();
    
    // Clear image cache
    PaintingBinding.instance.imageCache.clear();
    
    // Clear network cache
    DefaultCacheManager().emptyCache();
  }
}
```

## Accessibility Features

### Screen Reader Support

```dart
// lib/widgets/accessible_button.dart
class AccessibleButton extends StatelessWidget {
  final String semanticLabel;
  final VoidCallback onPressed;
  
  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel,
      button: true,
      child: ElevatedButton(
        onPressed: onPressed,
        child: Text('Record'),
      ),
    );
  }
}
```

### High Contrast Support

```dart
// lib/theme/accessible_theme.dart
class AccessibleTheme {
  static ThemeData getHighContrastTheme() {
    return ThemeData(
      brightness: Brightness.dark,
      primaryColor: Colors.white,
      backgroundColor: Colors.black,
      textTheme: TextTheme(
        bodyText1: TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
```

### Keyboard Navigation

```dart
// lib/widgets/keyboard_navigable.dart
class KeyboardNavigable extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Focus(
      autofocus: true,
      onKey: (node, event) {
        if (event.logicalKey == LogicalKeyboardKey.enter) {
          // Handle enter key
          return KeyEventResult.handled;
        }
        return KeyEventResult.ignored;
      },
      child: child,
    );
  }
}
```

## Final Deployment Steps

### Pre-Release Testing
1. Run full test suite on production builds
2. Test on multiple devices and OS versions
3. Verify all features work with production backend
4. Test offline functionality
5. Verify accessibility features
6. Performance testing under load

### Release Process
1. Create release branch from main
2. Update version numbers
3. Generate release builds
4. Upload to distribution platforms
5. Monitor crash reports and user feedback
6. Prepare hotfix process for critical issues

### Post-Release Monitoring
1. Monitor crash rates and performance metrics
2. Track user engagement and retention
3. Collect user feedback and reviews
4. Plan next iteration based on data
5. Maintain backend infrastructure
6. Update dependencies and security patches

## Rollback Plan

### Emergency Rollback
1. Identify critical issue
2. Disable problematic features via feature flags
3. Revert to previous stable version
4. Communicate with users about the issue
5. Implement fix and redeploy

### Gradual Rollback
1. Reduce rollout percentage
2. Monitor metrics for improvement
3. Investigate and fix issues
4. Resume rollout when stable

This comprehensive deployment guide ensures a smooth transition from development to production while maintaining high quality and user experience standards.
