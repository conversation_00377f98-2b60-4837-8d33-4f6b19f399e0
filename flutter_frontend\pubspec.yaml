name: deutschkorrekt_flutter
description: Flutter frontend for DeutschKorrekt - German language learning app with real-time speech-to-text transcription and AI-powered grammar correction.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI and styling
  cupertino_icons: ^1.0.6

  # WebSocket communication
  web_socket_channel: ^2.4.0

  # Audio recording (cutting-edge package)
  flutter_sound: ^9.28.0
  permission_handler: ^11.1.0

  # HTTP requests
  http: ^1.1.0

  # Markdown rendering for Groq responses
  flutter_markdown: ^0.7.4+1

  # File system access for audio cache
  path_provider: ^2.1.1

  # Audio playback for TTS
  audioplayers: ^6.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  mockito: ^5.4.4
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
  
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700