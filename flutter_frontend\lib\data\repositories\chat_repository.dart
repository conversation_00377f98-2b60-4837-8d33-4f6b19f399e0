import 'dart:async';
import 'dart:typed_data';
import '../models/message.dart';
import '../models/websocket_message.dart';
import '../models/groq_response.dart';
import '../models/correction_result.dart';
import '../models/app_error.dart';
import '../models/audio_config.dart';
import '../services/websocket_service.dart';
import '../services/audio_service.dart';
import '../services/permissions_service.dart';
import '../services/websocket_message_processor.dart';

/// Repository for managing chat functionality and coordinating services
class ChatRepository {
  static ChatRepository? _instance;
  
  /// Singleton instance
  static ChatRepository get instance {
    _instance ??= ChatRepository._();
    return _instance!;
  }
  
  final WebSocketService _webSocketService = WebSocketService();
  final AudioService _audioService = AudioService.instance;
  final PermissionsService _permissionsService = PermissionsService.instance;
  
  StreamController<Message>? _messageController;
  StreamController<AppError>? _errorController;
  StreamController<bool>? _connectionController;
  StreamController<bool>? _recordingController;
  StreamController<ProcessingState>? _processingController;
  
  StreamSubscription? _webSocketMessageSubscription;
  StreamSubscription? _webSocketErrorSubscription;
  StreamSubscription? _webSocketConnectionSubscription;
  StreamSubscription? _audioStreamSubscription;
  StreamSubscription? _audioErrorSubscription;
  StreamSubscription? _audioRecordingSubscription;
  
  WebSocketMessageProcessor? _messageProcessor;
  
  bool _isInitialized = false;
  bool _isDisposed = false;
  String? _currentMessageId;
  
  ChatRepository._();
  
  /// Stream of chat messages
  Stream<Message> get messageStream => _messageController?.stream ?? const Stream.empty();
  
  /// Stream of errors
  Stream<AppError> get errorStream => _errorController?.stream ?? const Stream.empty();
  
  /// Stream of connection status
  Stream<bool> get connectionStream => _connectionController?.stream ?? const Stream.empty();
  
  /// Stream of recording status
  Stream<bool> get recordingStream => _recordingController?.stream ?? const Stream.empty();
  
  /// Check if connected to backend
  bool get isConnected => _webSocketService.isConnected;
  
  /// Check if currently recording
  bool get isRecording => _audioService.isRecording;
  
  /// Stream of processing states
  Stream<ProcessingState> get processingStream => _processingController?.stream ?? const Stream.empty();
  
  /// Initialize the repository
  Future<void> initialize() async {
    if (_isDisposed) {
      throw StateError('ChatRepository has been disposed');
    }
    
    if (_isInitialized) return;
    
    // Initialize stream controllers
    _messageController = StreamController<Message>.broadcast();
    _errorController = StreamController<AppError>.broadcast();
    _connectionController = StreamController<bool>.broadcast();
    _recordingController = StreamController<bool>.broadcast();
    _processingController = StreamController<ProcessingState>.broadcast();
    
    // Initialize message processor
    _messageProcessor = WebSocketMessageProcessor(
      messageController: _messageController!,
      errorController: _errorController!,
      processingController: _processingController!,
    );
    
    // Initialize services
    await _audioService.initialize();
    
    // Set up subscriptions
    _setupSubscriptions();
    
    _isInitialized = true;
    print('ChatRepository initialized');
  }
  
  /// Set up service subscriptions
  void _setupSubscriptions() {
    // WebSocket message subscription
    _webSocketMessageSubscription = _webSocketService.messageStream.listen(
      _handleWebSocketMessage,
      onError: (error) => _handleError(error),
    );
    
    // WebSocket error subscription
    _webSocketErrorSubscription = _webSocketService.errorStream.listen(
      _handleError,
    );
    
    // WebSocket connection subscription
    _webSocketConnectionSubscription = _webSocketService.connectionStream.listen(
      (connected) => _connectionController?.add(connected),
    );
    
    // Audio stream subscription
    _audioStreamSubscription = _audioService.audioStream.listen(
      _handleAudioData,
      onError: (error) => _handleError(error),
    );
    
    // Audio error subscription
    _audioErrorSubscription = _audioService.errorStream.listen(
      _handleError,
    );
    
    // Audio recording state subscription
    _audioRecordingSubscription = _audioService.recordingStateStream.listen(
      (recording) => _recordingController?.add(recording),
    );
  }
  
  /// Handle incoming WebSocket messages using enhanced processor
  void _handleWebSocketMessage(WebSocketMessage wsMessage) {
    try {
      _messageProcessor?.processMessage(wsMessage);
    } catch (e) {
      _handleError(AppError.backendError(
        details: 'Error processing WebSocket message: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      ));
    }
  }
  

  
  /// Handle audio data from microphone
  void _handleAudioData(Uint8List audioData) {
    if (_webSocketService.isConnected) {
      _webSocketService.sendAudioData(audioData);
    }
  }
  
  /// Handle errors from various sources
  void _handleError(dynamic error) {
    AppError appError;
    
    if (error is AppError) {
      appError = error;
    } else {
      appError = AppError.unknown(
        details: error.toString(),
        originalException: error is Exception ? error : Exception(error.toString()),
      );
    }
    
    _errorController?.add(appError);
  }
  
  /// Connect to backend
  Future<void> connect() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    await _webSocketService.connect();
  }
  
  /// Disconnect from backend
  Future<void> disconnect() async {
    await _webSocketService.disconnect();
  }
  
  /// Start recording and streaming matching Expo version flow
  Future<void> startRecording() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    // Check permissions first
    if (!await _audioService.hasPermissions()) {
      final hasPermission = await _audioService.requestPermissions();
      if (!hasPermission) {
        return; // Error will be handled by audio service
      }
    }
    
    // Connect to backend if not connected
    if (!_webSocketService.isConnected) {
      await _webSocketService.connect();
    }
    
    // Start session
    _webSocketService.startSession();
    
    // Generate new message ID for this recording
    _currentMessageId = 'msg_${DateTime.now().millisecondsSinceEpoch}';
    
    // Notify message processor about new message
    _messageProcessor?.startNewMessage(_currentMessageId!);
    
    // Create initial user message with "Connecting..." state (matching Expo)
    final connectingMessage = Message.user(
      id: _currentMessageId!,
      text: 'Connecting...',
      isAudio: true,
      isStreaming: true,
    );
    _messageController?.add(connectingMessage);
    
    // Start audio recording
    await _audioService.startRecording();
    
    // Update message to show "Listening..." state (matching Expo)
    final listeningMessage = Message.user(
      id: _currentMessageId!,
      text: 'Listening...',
      isAudio: true,
      isStreaming: true,
    );
    _messageController?.add(listeningMessage);
  }
  
  /// Stop recording
  Future<void> stopRecording() async {
    await _audioService.stopRecording();
    _webSocketService.endSession();
  }
  
  /// Send a text message
  void sendTextMessage(String text) {
    final message = Message.user(
      id: 'text_${DateTime.now().millisecondsSinceEpoch}',
      text: text,
    );
    _messageController?.add(message);
    
    // Send to backend for processing
    _webSocketService.sendMessage({
      'type': 'text_message',
      'text': text,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  /// Update audio configuration
  void updateAudioConfig(AudioConfig config) {
    _audioService.updateConfig(config);
  }
  
  /// Get repository statistics
  Map<String, dynamic> getStats() {
    return {
      'isInitialized': _isInitialized,
      'isConnected': isConnected,
      'isRecording': isRecording,
      'currentMessageId': _currentMessageId,
      'webSocketStats': _webSocketService.getConnectionStats(),
      'audioStats': _audioService.getStats(),
    };
  }
  
  /// Dispose the repository and clean up resources
  Future<void> dispose() async {
    if (_isDisposed) return;
    
    _isDisposed = true;
    
    // Cancel subscriptions
    await _webSocketMessageSubscription?.cancel();
    await _webSocketErrorSubscription?.cancel();
    await _webSocketConnectionSubscription?.cancel();
    await _audioStreamSubscription?.cancel();
    await _audioErrorSubscription?.cancel();
    await _audioRecordingSubscription?.cancel();
    
    // Dispose services
    await _webSocketService.dispose();
    await _audioService.dispose();
    
    // Close stream controllers
    await _messageController?.close();
    await _errorController?.close();
    await _connectionController?.close();
    await _recordingController?.close();
    
    _messageController = null;
    _errorController = null;
    _connectionController = null;
    _recordingController = null;
    
    _isInitialized = false;
  }
}