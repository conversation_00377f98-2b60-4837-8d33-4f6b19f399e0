import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../data/models/profile_data.dart';

/// Provider for managing user profile data with persistent storage
class ProfileProvider extends ChangeNotifier {
  static const String _keyUserName = 'user_name';
  static const String _keyUserLevel = 'user_level';
  static const String _keyCorrectionsCount = 'corrections_count';
  static const String _keyImprovementPercentage = 'improvement_percentage';
  static const String _keyLearningGoalsCompleted = 'learning_goals_completed';
  static const String _keyJoinDate = 'join_date';
  static const String _keyTotalSessions = 'total_sessions';
  static const String _keyTotalRecordingTime = 'total_recording_time';
  static const String _keyStreakDays = 'streak_days';
  static const String _keyLastActiveDate = 'last_active_date';
  static const String _keyAvatarUrl = 'avatar_url';
  
  SharedPreferences? _prefs;
  bool _isInitialized = false;
  bool _isDisposed = false;
  
  // Profile data
  String _userName = 'User';
  String _userLevel = 'Beginner';
  String _avatarUrl = '';
  DateTime _joinDate = DateTime.now();
  DateTime _lastActiveDate = DateTime.now();
  
  // Statistics
  int _correctionsCount = 0;
  double _improvementPercentage = 0.0;
  int _learningGoalsCompleted = 0;
  int _totalSessions = 0;
  int _totalRecordingTimeMinutes = 0;
  int _streakDays = 0;
  
  // Learning goals and activities
  final List<LearningGoal> _learningGoals = [];
  final List<ActivityItem> _recentActivity = [];
  final List<StatItem> _stats = [];
  
  // Getters
  String get userName => _userName;
  String get userLevel => _userLevel;
  String get avatarUrl => _avatarUrl;
  DateTime get joinDate => _joinDate;
  DateTime get lastActiveDate => _lastActiveDate;
  int get correctionsCount => _correctionsCount;
  double get improvementPercentage => _improvementPercentage;
  int get learningGoalsCompleted => _learningGoalsCompleted;
  int get totalSessions => _totalSessions;
  int get totalRecordingTimeMinutes => _totalRecordingTimeMinutes;
  int get streakDays => _streakDays;
  List<LearningGoal> get learningGoals => List.unmodifiable(_learningGoals);
  List<ActivityItem> get recentActivity => List.unmodifiable(_recentActivity);
  List<StatItem> get stats => List.unmodifiable(_stats);
  bool get isInitialized => _isInitialized;
  
  /// Get profile data as a complete object
  ProfileData get profileData => ProfileData(
    userName: _userName,
    userLevel: _userLevel,
    avatarUrl: _avatarUrl,
    joinDate: _joinDate,
    stats: _stats,
    goals: _learningGoals,
    recentActivity: _recentActivity,
  );
  
  /// Initialize profile provider and load saved data
  Future<void> initialize() async {
    if (_isDisposed || _isInitialized) return;
    
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadProfileData();
      _generateStats();
      _generateDefaultGoals();
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      print('Error initializing profile: $e');
      _isInitialized = true; // Continue with defaults
      _generateStats();
      _generateDefaultGoals();
      notifyListeners();
    }
  }
  
  /// Load profile data from persistent storage
  Future<void> _loadProfileData() async {
    if (_prefs == null) return;
    
    _userName = _prefs!.getString(_keyUserName) ?? 'User';
    _userLevel = _prefs!.getString(_keyUserLevel) ?? 'Beginner';
    _avatarUrl = _prefs!.getString(_keyAvatarUrl) ?? '';
    _correctionsCount = _prefs!.getInt(_keyCorrectionsCount) ?? 0;
    _improvementPercentage = _prefs!.getDouble(_keyImprovementPercentage) ?? 0.0;
    _learningGoalsCompleted = _prefs!.getInt(_keyLearningGoalsCompleted) ?? 0;
    _totalSessions = _prefs!.getInt(_keyTotalSessions) ?? 0;
    _totalRecordingTimeMinutes = _prefs!.getInt(_keyTotalRecordingTime) ?? 0;
    _streakDays = _prefs!.getInt(_keyStreakDays) ?? 0;
    
    // Load dates
    final joinDateString = _prefs!.getString(_keyJoinDate);
    if (joinDateString != null) {
      _joinDate = DateTime.tryParse(joinDateString) ?? DateTime.now();
    }
    
    final lastActiveDateString = _prefs!.getString(_keyLastActiveDate);
    if (lastActiveDateString != null) {
      _lastActiveDate = DateTime.tryParse(lastActiveDateString) ?? DateTime.now();
    }
  }
  
  /// Generate statistics from current data
  void _generateStats() {
    _stats.clear();
    
    _stats.addAll([
      StatItem(
        key: 'corrections',
        value: _correctionsCount.toString(),
        label: 'Corrections Made',
        icon: '✓',
      ),
      StatItem(
        key: 'improvement',
        value: '${_improvementPercentage.toStringAsFixed(1)}%',
        label: 'Improvement',
        icon: '📈',
      ),
      StatItem(
        key: 'sessions',
        value: _totalSessions.toString(),
        label: 'Total Sessions',
        icon: '🎯',
      ),
      StatItem(
        key: 'recording_time',
        value: '${_totalRecordingTimeMinutes}min',
        label: 'Recording Time',
        icon: '🎤',
      ),
      StatItem(
        key: 'streak',
        value: _streakDays.toString(),
        label: 'Day Streak',
        icon: '🔥',
      ),
      StatItem(
        key: 'level',
        value: _userLevel,
        label: 'Current Level',
        icon: '🏆',
      ),
    ]);
  }
  
  /// Generate default learning goals
  void _generateDefaultGoals() {
    if (_learningGoals.isEmpty) {
      _learningGoals.addAll([
        LearningGoal(
          id: 'daily_practice',
          title: 'Daily Practice',
          description: 'Practice German for 15 minutes daily',
          progress: _streakDays.toDouble(),
          target: 30.0,
          isCompleted: _streakDays >= 30,
          deadline: DateTime.now().add(const Duration(days: 30)),
        ),
        LearningGoal(
          id: 'corrections_goal',
          title: 'Grammar Corrections',
          description: 'Get 50 grammar corrections',
          progress: _correctionsCount.toDouble(),
          target: 50.0,
          isCompleted: _correctionsCount >= 50,
          deadline: DateTime.now().add(const Duration(days: 60)),
        ),
        LearningGoal(
          id: 'recording_time',
          title: 'Speaking Practice',
          description: 'Record 2 hours of speech',
          progress: _totalRecordingTimeMinutes.toDouble(),
          target: 120.0,
          isCompleted: _totalRecordingTimeMinutes >= 120,
          deadline: DateTime.now().add(const Duration(days: 45)),
        ),
      ]);
    }
  }
  
  /// Save profile data to persistent storage
  Future<void> _saveProfileData() async {
    if (_prefs == null) return;
    
    await _prefs!.setString(_keyUserName, _userName);
    await _prefs!.setString(_keyUserLevel, _userLevel);
    await _prefs!.setString(_keyAvatarUrl, _avatarUrl);
    await _prefs!.setInt(_keyCorrectionsCount, _correctionsCount);
    await _prefs!.setDouble(_keyImprovementPercentage, _improvementPercentage);
    await _prefs!.setInt(_keyLearningGoalsCompleted, _learningGoalsCompleted);
    await _prefs!.setInt(_keyTotalSessions, _totalSessions);
    await _prefs!.setInt(_keyTotalRecordingTime, _totalRecordingTimeMinutes);
    await _prefs!.setInt(_keyStreakDays, _streakDays);
    await _prefs!.setString(_keyJoinDate, _joinDate.toIso8601String());
    await _prefs!.setString(_keyLastActiveDate, _lastActiveDate.toIso8601String());
  }
  
  /// Update user name
  Future<void> updateUserName(String name) async {
    if (_isDisposed || name.trim().isEmpty) return;
    
    _userName = name.trim();
    await _saveProfileData();
    _addActivity('Profile updated', 'Changed username to $name');
    notifyListeners();
  }
  
  /// Update user level
  Future<void> updateUserLevel(String level) async {
    if (_isDisposed || _userLevel == level) return;
    
    final oldLevel = _userLevel;
    _userLevel = level;
    await _saveProfileData();
    _generateStats();
    _addActivity('Level changed', 'Advanced from $oldLevel to $level');
    notifyListeners();
  }
  
  /// Update avatar URL
  Future<void> updateAvatarUrl(String url) async {
    if (_isDisposed) return;
    
    _avatarUrl = url;
    await _saveProfileData();
    _addActivity('Profile updated', 'Changed profile picture');
    notifyListeners();
  }
  
  /// Increment corrections count
  Future<void> incrementCorrections() async {
    if (_isDisposed) return;
    
    _correctionsCount++;
    await _saveProfileData();
    _generateStats();
    _updateGoalProgress('corrections_goal', _correctionsCount.toDouble());
    _addActivity('Grammar correction', 'Received grammar correction');
    notifyListeners();
  }
  
  /// Update improvement percentage
  Future<void> updateImprovement(double percentage) async {
    if (_isDisposed) return;
    
    _improvementPercentage = percentage.clamp(0.0, 100.0);
    await _saveProfileData();
    _generateStats();
    _addActivity('Progress update', 'Improvement: ${percentage.toStringAsFixed(1)}%');
    notifyListeners();
  }
  
  /// Increment learning goals completed
  Future<void> incrementLearningGoals() async {
    if (_isDisposed) return;
    
    _learningGoalsCompleted++;
    await _saveProfileData();
    _addActivity('Goal completed', 'Completed a learning goal');
    notifyListeners();
  }
  
  /// Record a new session
  Future<void> recordSession(int durationMinutes) async {
    if (_isDisposed) return;
    
    _totalSessions++;
    _totalRecordingTimeMinutes += durationMinutes;
    _updateStreak();
    
    await _saveProfileData();
    _generateStats();
    _updateGoalProgress('recording_time', _totalRecordingTimeMinutes.toDouble());
    _updateGoalProgress('daily_practice', _streakDays.toDouble());
    
    _addActivity('Practice session', 'Completed ${durationMinutes}min session');
    notifyListeners();
  }
  
  /// Update streak based on last active date
  void _updateStreak() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final lastActive = DateTime(_lastActiveDate.year, _lastActiveDate.month, _lastActiveDate.day);
    
    if (today.difference(lastActive).inDays == 1) {
      // Consecutive day
      _streakDays++;
    } else if (today.difference(lastActive).inDays > 1) {
      // Streak broken
      _streakDays = 1;
    }
    // Same day - no change to streak
    
    _lastActiveDate = now;
  }
  
  /// Update progress for a specific goal
  void _updateGoalProgress(String goalId, double progress) {
    final goalIndex = _learningGoals.indexWhere((g) => g.id == goalId);
    if (goalIndex != -1) {
      final goal = _learningGoals[goalIndex];
      final updatedGoal = LearningGoal(
        id: goal.id,
        title: goal.title,
        description: goal.description,
        progress: progress,
        target: goal.target,
        isCompleted: progress >= goal.target,
        deadline: goal.deadline,
      );
      _learningGoals[goalIndex] = updatedGoal;
      
      if (updatedGoal.isCompleted && !goal.isCompleted) {
        incrementLearningGoals();
      }
    }
  }
  
  /// Add an activity item
  void _addActivity(String type, String description) {
    final activity = ActivityItem(
      id: 'activity_${DateTime.now().millisecondsSinceEpoch}',
      type: type,
      description: description,
      timestamp: DateTime.now(),
    );
    
    _recentActivity.insert(0, activity);
    
    // Keep only last 20 activities
    if (_recentActivity.length > 20) {
      _recentActivity.removeRange(20, _recentActivity.length);
    }
  }
  
  /// Add a custom learning goal
  void addLearningGoal(String title, String description, double target, DateTime deadline) {
    if (_isDisposed) return;
    
    final goal = LearningGoal(
      id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
      title: title,
      description: description,
      progress: 0.0,
      target: target,
      isCompleted: false,
      deadline: deadline,
    );
    
    _learningGoals.add(goal);
    _addActivity('Goal created', 'Added new goal: $title');
    notifyListeners();
  }
  
  /// Remove a learning goal
  void removeLearningGoal(String goalId) {
    if (_isDisposed) return;
    
    final goalIndex = _learningGoals.indexWhere((g) => g.id == goalId);
    if (goalIndex != -1) {
      final goal = _learningGoals.removeAt(goalIndex);
      _addActivity('Goal removed', 'Removed goal: ${goal.title}');
      notifyListeners();
    }
  }
  
  /// Get available user levels
  List<String> getAvailableLevels() {
    return ['Beginner', 'Elementary', 'Intermediate', 'Upper Intermediate', 'Advanced', 'Proficient'];
  }
  
  /// Get level progress (0.0 to 1.0)
  double getLevelProgress() {
    final levels = getAvailableLevels();
    final currentIndex = levels.indexOf(_userLevel);
    if (currentIndex == -1) return 0.0;
    
    return (currentIndex + 1) / levels.length;
  }
  
  /// Calculate overall progress score
  double getOverallProgress() {
    if (_learningGoals.isEmpty) return 0.0;
    
    double totalProgress = 0.0;
    for (final goal in _learningGoals) {
      totalProgress += goal.progressPercentage;
    }
    
    return totalProgress / _learningGoals.length;
  }
  
  /// Export profile data to JSON
  Map<String, dynamic> exportProfile() {
    return {
      'user_name': _userName,
      'user_level': _userLevel,
      'avatar_url': _avatarUrl,
      'join_date': _joinDate.toIso8601String(),
      'last_active_date': _lastActiveDate.toIso8601String(),
      'corrections_count': _correctionsCount,
      'improvement_percentage': _improvementPercentage,
      'learning_goals_completed': _learningGoalsCompleted,
      'total_sessions': _totalSessions,
      'total_recording_time_minutes': _totalRecordingTimeMinutes,
      'streak_days': _streakDays,
      'stats': _stats.map((s) => s.toJson()).toList(),
      'learning_goals': _learningGoals.map((g) => g.toJson()).toList(),
      'recent_activity': _recentActivity.map((a) => a.toJson()).toList(),
    };
  }
  
  /// Reset profile to defaults
  Future<void> resetProfile() async {
    if (_isDisposed) return;
    
    _userName = 'User';
    _userLevel = 'Beginner';
    _avatarUrl = '';
    _joinDate = DateTime.now();
    _lastActiveDate = DateTime.now();
    _correctionsCount = 0;
    _improvementPercentage = 0.0;
    _learningGoalsCompleted = 0;
    _totalSessions = 0;
    _totalRecordingTimeMinutes = 0;
    _streakDays = 0;
    
    _learningGoals.clear();
    _recentActivity.clear();
    
    if (_prefs != null) {
      await _prefs!.clear();
    }
    
    _generateStats();
    _generateDefaultGoals();
    await _saveProfileData();
    
    notifyListeners();
  }
  
  /// Get profile statistics
  Map<String, dynamic> getProfileStats() {
    return {
      'isInitialized': _isInitialized,
      'totalStats': _stats.length,
      'totalGoals': _learningGoals.length,
      'completedGoals': _learningGoals.where((g) => g.isCompleted).length,
      'recentActivities': _recentActivity.length,
      'overallProgress': getOverallProgress(),
      'levelProgress': getLevelProgress(),
    };
  }
  
  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}