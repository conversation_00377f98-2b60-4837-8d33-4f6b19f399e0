import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/core/utils/error_handler.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

void main() {
  group('ErrorHandler', () {
    group('handleAuthError', () {
      test('should handle invalid login credentials', () {
        final error = AuthException('Invalid login credentials');
        final message = ErrorHandler.handleAuthError(error);
        
        expect(message, contains('Invalid email or password'));
      });

      test('should handle email not confirmed', () {
        final error = AuthException('Email not confirmed');
        final message = ErrorHandler.handleAuthError(error);
        
        expect(message, contains('check your email'));
      });

      test('should handle user already registered', () {
        final error = AuthException('User already registered');
        final message = ErrorHandler.handleAuthError(error);
        
        expect(message, contains('account with this email already exists'));
      });

      test('should handle password length requirement', () {
        final error = AuthException('Password should be at least 6 characters');
        final message = ErrorHandler.handleAuthError(error);
        
        expect(message, contains('Password must be at least 8 characters'));
      });

      test('should handle invalid email', () {
        final error = AuthException('Invalid email');
        final message = ErrorHandler.handleAuthError(error);
        
        expect(message, contains('valid email address'));
      });

      test('should handle generic auth error', () {
        final error = AuthException('Some other auth error');
        final message = ErrorHandler.handleAuthError(error);
        
        expect(message, contains('Authentication failed'));
      });

      test('should handle non-auth error', () {
        final error = Exception('Generic error');
        final message = ErrorHandler.handleAuthError(error);
        
        expect(message, equals('An unexpected error occurred. Please try again.'));
      });
    });

    group('handleDatabaseError', () {
      test('should handle unique constraint violation', () {
        final error = PostgrestException(
          message: 'Unique constraint violation',
          code: '23505',
        );
        final message = ErrorHandler.handleDatabaseError(error);
        
        expect(message, contains('already exists'));
      });

      test('should handle foreign key constraint violation', () {
        final error = PostgrestException(
          message: 'Foreign key constraint violation',
          code: '23503',
        );
        final message = ErrorHandler.handleDatabaseError(error);
        
        expect(message, contains('Referenced data not found'));
      });

      test('should handle insufficient privilege', () {
        final error = PostgrestException(
          message: 'Insufficient privilege',
          code: '42501',
        );
        final message = ErrorHandler.handleDatabaseError(error);
        
        expect(message, contains('don\'t have permission'));
      });

      test('should handle connection failure', () {
        final error = PostgrestException(
          message: 'Connection failure',
          code: '08006',
        );
        final message = ErrorHandler.handleDatabaseError(error);
        
        expect(message, contains('Database connection failed'));
      });

      test('should handle query timeout', () {
        final error = PostgrestException(
          message: 'Query timeout',
          code: '57014',
        );
        final message = ErrorHandler.handleDatabaseError(error);
        
        expect(message, contains('timed out'));
      });

      test('should handle generic database error', () {
        final error = PostgrestException(
          message: 'Some database error',
          code: '99999',
        );
        final message = ErrorHandler.handleDatabaseError(error);
        
        expect(message, contains('Database error'));
      });
    });

    group('handleNetworkError', () {
      test('should handle socket exception', () {
        final error = Exception('SocketException: Connection failed');
        final message = ErrorHandler.handleNetworkError(error);
        
        expect(message, contains('Network connection failed'));
      });

      test('should handle timeout error', () {
        final error = Exception('TimeoutException: Request timed out');
        final message = ErrorHandler.handleNetworkError(error);
        
        expect(message, contains('Request timed out'));
      });

      test('should handle SSL certificate error', () {
        final error = Exception('Certificate verification failed');
        final message = ErrorHandler.handleNetworkError(error);
        
        expect(message, contains('Secure connection failed'));
      });

      test('should handle generic network error', () {
        final error = Exception('Some other error');
        final message = ErrorHandler.handleNetworkError(error);
        
        expect(message, equals('An unexpected error occurred. Please try again.'));
      });
    });

    group('handleCreditError', () {
      test('should handle insufficient credits', () {
        final error = Exception('Insufficient credits available');
        final message = ErrorHandler.handleCreditError(error);
        
        expect(message, contains('don\'t have enough credits'));
      });

      test('should handle no credits', () {
        final error = Exception('No credits remaining');
        final message = ErrorHandler.handleCreditError(error);
        
        expect(message, contains('don\'t have enough credits'));
      });

      test('should handle quota exceeded', () {
        final error = Exception('Quota exceeded for this period');
        final message = ErrorHandler.handleCreditError(error);
        
        expect(message, contains('Usage limit reached'));
      });

      test('should handle generic credit error', () {
        final error = Exception('Some other credit error');
        final message = ErrorHandler.handleCreditError(error);
        
        expect(message, equals('An unexpected error occurred. Please try again.'));
      });
    });

    group('handleSessionError', () {
      test('should handle session logging error', () {
        final error = Exception('Failed to log session data');
        final message = ErrorHandler.handleSessionError(error);
        
        expect(message, contains('Failed to save session data'));
      });

      test('should handle generic session error', () {
        final error = Exception('Some other session error');
        final message = ErrorHandler.handleSessionError(error);
        
        expect(message, equals('An unexpected error occurred. Please try again.'));
      });
    });

    group('isRecoverableError', () {
      test('should identify network errors as recoverable', () {
        final error = Exception('Network connection failed');
        expect(ErrorHandler.isRecoverableError(error), isTrue);
      });

      test('should identify timeout errors as recoverable', () {
        final error = Exception('Request timeout occurred');
        expect(ErrorHandler.isRecoverableError(error), isTrue);
      });

      test('should identify rate limit errors as recoverable', () {
        final error = Exception('Rate limit exceeded');
        expect(ErrorHandler.isRecoverableError(error), isTrue);
      });

      test('should identify server errors as recoverable', () {
        final error = Exception('Internal server error 500');
        expect(ErrorHandler.isRecoverableError(error), isTrue);
      });

      test('should identify validation errors as non-recoverable', () {
        final error = Exception('Invalid email format');
        expect(ErrorHandler.isRecoverableError(error), isFalse);
      });
    });

    group('getRetryDelay', () {
      test('should return longer delay for rate limit errors', () {
        final error = Exception('Rate limit exceeded');
        final delay = ErrorHandler.getRetryDelay(error, 1);
        
        expect(delay.inSeconds, equals(5)); // 1^2 * 5 = 5 seconds
      });

      test('should return linear delay for network errors', () {
        final error = Exception('Network connection failed');
        final delay = ErrorHandler.getRetryDelay(error, 2);
        
        expect(delay.inSeconds, equals(4)); // 2 * 2 = 4 seconds
      });

      test('should return exponential delay for other errors', () {
        final error = Exception('Some other error');
        final delay = ErrorHandler.getRetryDelay(error, 3);
        
        expect(delay.inSeconds, equals(9)); // 3^2 = 9 seconds
      });
    });
  });
}