# Implementation Plan

- [x] 1. Set up backend Google Cloud Text-to-Speech integration





  - Install Google Cloud TTS Python client library in backend requirements
  - Create Google TTS client service with authentication and voice configuration
  - Implement TTS service class with text validation and audio generation
  - _Requirements: 3.1, 3.2, 3.3, 3.5, 3.6, 3.7_

- [x] 1.1 Install and configure Google Cloud TTS dependencies


  - Add google-cloud-texttospeech to backend/requirements.txt
  - Configure Google Cloud authentication in backend settings
  - Create environment variables for TTS configuration
  - _Requirements: 3.1, 3.2, 6.5_

- [x] 1.2 Implement Google TTS client service


  - Create backend/services/google_tts_client.py with Google Cloud TTS integration
  - Configure voice settings for de-DE-Chirp3-HD-Aoede at 0.9 speed
  - Implement audio format configuration for mobile playback (MP3, 24kHz)
  - Add proper error handling and logging for TTS API calls
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7_

- [x] 1.3 Create TTS service with text validation


  - Create backend/services/tts_service.py with TTSService class
  - Implement text validation (length limits, content sanitization)
  - Add generate_speech method that interfaces with Google TTS client
  - Implement proper error handling for invalid inputs and API failures
  - _Requirements: 3.5, 3.6, 6.3_

- [x] 1.4 Create TTS API endpoint


  - Add TTS endpoint to backend/main.py or create separate router
  - Implement POST /api/tts endpoint with TTSRequest/TTSResponse models
  - Add request validation, rate limiting, and error handling
  - Include proper logging and monitoring for TTS requests
  - _Requirements: 3.5, 6.3, 6.5_

- [x] 2. Implement Flutter frontend audio cache management




  - Create audio cache manager for storing and retrieving TTS audio files
  - Implement cache cleanup logic for old audio files
  - Add secure app-private storage for cached audio
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 6.4_

- [x] 2.1 Create audio cache manager service


  - Create flutter_frontend/lib/data/services/audio_cache_manager.dart
  - Implement AudioCacheManager class with cache storage and retrieval methods
  - Add getCachedAudio, cacheAudio, and clearOldCache methods
  - Use path_provider for app-private directory access
  - _Requirements: 4.1, 4.2, 4.4, 6.4_

- [x] 2.2 Implement cache cleanup logic


  - Add deleteAudioFile method to remove specific cached files
  - Implement clearOldCache to remove files when new Groq responses arrive
  - Add cache size management to prevent storage bloat
  - Include error handling for cache operations
  - _Requirements: 4.3, 4.4, 4.5_

- [x] 3. Create TTS audio service for backend communication




  - Implement service to coordinate TTS requests with backend API
  - Add audio playback functionality using Flutter audio players
  - Integrate with audio cache manager for efficient caching
  - _Requirements: 1.4, 4.1, 4.2, 5.3, 6.1, 6.2_

- [x] 3.1 Create TTS audio service class






  - Create flutter_frontend/lib/data/services/tts_audio_service.dart
  - Implement TTSAudioService class with playTTS and stopPlayback methods
  - Add HTTP client integration for backend TTS API calls
  - Include proper error handling and timeout management
  - _Requirements: 1.4, 5.3, 6.1, 6.3_

- [x] 3.2 Integrate audio playback functionality


  - Add audioplayers dependency to pubspec.yaml
  - Implement audio playback using AudioPlayer for cached and new audio
  - Add audio session management for background/foreground handling
  - Include playback state management and error recovery
  - _Requirements: 1.4, 5.4, 5.5, 6.2_

- [x] 3.3 Connect TTS service with cache manager


  - Integrate AudioCacheManager into TTSAudioService
  - Implement cache-first playback logic (check cache before API call)
  - Add cache storage after successful TTS API responses
  - Include cache error handling with fallback behavior
  - _Requirements: 4.1, 4.2, 4.5_

- [x] 4. Implement sentence extraction from Groq responses




  - Add logic to extract first sentence from curly braces in Groq responses
  - Store extracted sentence temporarily for current response
  - Clear previous sentence when new Groq response arrives
  - _Requirements: 1.1, 1.2_

- [x] 4.1 Create sentence extraction utility


  - Create flutter_frontend/lib/core/utils/sentence_extractor.dart
  - Implement extractFirstSentenceFromBraces method with regex parsing
  - Add validation to ensure sentence exists and is not empty
  - Include unit tests for various curly brace scenarios
  - _Requirements: 1.1, 5.1_

- [x] 4.2 Integrate sentence extraction in message handling


  - Modify existing message processing to extract sentences from Groq responses
  - Store extracted sentence in message model or state management
  - Ensure sentence is cleared when new responses arrive
  - Add logging for sentence extraction debugging
  - _Requirements: 1.1, 1.2_

- [x] 5. Create audio icon widget with professional design




  - Design and implement audio/speaker icon component
  - Add visual states for idle, loading, playing, and error
  - Implement smooth animations and visual feedback
  - _Requirements: 2.3, 2.4, 2.5_

- [x] 5.1 Create audio icon widget component


  - Create flutter_frontend/lib/presentation/widgets/chat/audio_icon_widget.dart
  - Implement AudioIconWidget with StatefulWidget for state management
  - Add visual states enum (idle, loading, playing, error)
  - Design professional audio/speaker icon with appropriate styling
  - _Requirements: 2.3, 2.4, 2.5_

- [x] 5.2 Implement icon state animations


  - Add smooth transitions between icon states (loading spinner, play indicator)
  - Implement tap animations and visual feedback
  - Add loading animation during TTS API calls
  - Include error state visualization with retry options
  - _Requirements: 2.4, 2.5, 5.3_

- [x] 5.3 Add user interaction handling


  - Implement onTap handler for audio icon clicks
  - Add state management for first-time vs subsequent clicks
  - Include proper error handling and user feedback
  - Add accessibility support for audio icon interactions
  - _Requirements: 1.4, 1.5, 2.4_

- [x] 6. Enhance message bubble to display audio icon




  - Modify existing MessageBubble widget to show audio icon when sentence available
  - Implement dynamic layout adjustment for audio icon positioning
  - Position audio icon in top-right corner of response bubbles
  - _Requirements: 1.3, 2.1, 2.2, 5.1_

- [x] 6.1 Modify MessageBubble widget for audio integration


  - Update flutter_frontend/lib/presentation/widgets/chat/message_bubble.dart
  - Add conditional audio icon display based on extracted sentence availability
  - Implement top-right positioning with proper layout constraints
  - Ensure audio icon only appears for AI responses, not user messages
  - _Requirements: 1.3, 2.1, 5.1_

- [x] 6.2 Implement dynamic layout adjustment


  - Modify message bubble layout to accommodate audio icon space
  - Add responsive padding and margin adjustments
  - Ensure text content doesn't overlap with audio icon
  - Test layout on different screen sizes and orientations
  - _Requirements: 2.2_

- [x] 6.3 Connect audio icon with TTS service


  - Integrate AudioIconWidget with TTSAudioService
  - Pass extracted sentence to TTS service on icon tap
  - Handle TTS service responses and update icon states accordingly
  - Add proper error handling and user feedback integration
  - _Requirements: 1.4, 1.5, 5.3_

- [x] 7. Implement complete TTS workflow integration




  - Connect all components for end-to-end TTS functionality
  - Add proper error handling and user feedback throughout the flow
  - Implement cache management for new responses
  - _Requirements: 1.4, 1.5, 4.3, 5.2, 5.3, 5.5_

- [x] 7.1 Integrate complete TTS workflow


  - Connect sentence extraction, audio icon, TTS service, and cache manager
  - Implement first-time TTS API call workflow with caching
  - Add subsequent-click cached playback workflow
  - Include proper state management across all components
  - _Requirements: 1.4, 1.5, 4.1, 4.2_

- [x] 7.2 Add comprehensive error handling


  - Implement error handling for TTS API failures, network issues, and cache errors
  - Add user-friendly error messages and retry mechanisms
  - Include proper logging for debugging and monitoring
  - Add fallback behavior for various failure scenarios
  - _Requirements: 4.5, 5.2, 5.3, 6.3_

- [x] 7.3 Implement cache cleanup for new responses


  - Add logic to clear old cached audio when new Groq responses arrive
  - Ensure proper cleanup to prevent storage bloat
  - Include error handling for cleanup operations
  - Add logging for cache management operations
  - _Requirements: 4.3, 4.4_

- [x] 8. Add comprehensive testing for TTS functionality





  - Create unit tests for all TTS-related components
  - Add widget tests for audio icon and message bubble integration
  - Implement integration tests for complete TTS workflow
  - _Requirements: 6.1, 6.2, 6.3_

- [x] 8.1 Create unit tests for core components






  - Write tests for sentence extraction utility with various input scenarios
  - Add tests for audio cache manager (storage, retrieval, cleanup)
  - Create tests for TTS audio service with mocked backend responses
  - Include tests for audio icon widget state management
  - _Requirements: 6.1_

- [x] 8.2 Add widget and integration tests



  - Create widget tests for MessageBubble with audio icon integration
  - Add integration tests for complete TTS workflow (extraction to playback)
  - Include tests for error scenarios and recovery mechanisms
  - Add performance tests for cache operations and audio playback
  - _Requirements: 6.1, 6.2_

- [x] 8.3 Create backend TTS service tests













  - Write unit tests for Google TTS client integration
  - Add API endpoint tests for TTS service with various inputs
  - Include tests for error handling and validation
  - Add integration tests with actual Google Cloud TTS API
  - _Requirements: 6.1, 6.3_