import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/presentation/widgets/chat/audio_icon_widget.dart';

void main() {
  group('AudioIconWidget', () {
    group('Widget Rendering', () {
      testWidgets('should render with default properties', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
              ),
            ),
          ),
        );
        
        expect(find.byType(AudioIconWidget), findsOneWidget);
        expect(find.byIcon(Icons.volume_up_outlined), findsOneWidget);
      });
      
      testWidgets('should display correct icon for idle state', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.idle,
              ),
            ),
          ),
        );
        
        expect(find.byIcon(Icons.volume_up_outlined), findsOneWidget);
      });
      
      testWidgets('should display correct icon for loading state', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.loading,
              ),
            ),
          ),
        );
        
        expect(find.byIcon(Icons.sync), findsOneWidget);
      });
      
      testWidgets('should display correct icon for playing state', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.playing,
              ),
            ),
          ),
        );
        
        expect(find.byIcon(Icons.volume_up), findsOneWidget);
      });
      
      testWidgets('should display correct icon for error state', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.error,
              ),
            ),
          ),
        );
        
        expect(find.byIcon(Icons.volume_off), findsOneWidget);
      });
      
      testWidgets('should render with custom size', (WidgetTester tester) async {
        const customSize = 32.0;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                size: customSize,
              ),
            ),
          ),
        );
        
        final iconWidget = tester.widget<Icon>(find.byType(Icon));
        expect(iconWidget.size, equals(customSize));
      });
      
      testWidgets('should render with custom semantics label', (WidgetTester tester) async {
        const customLabel = 'Custom audio button';
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                semanticsLabel: customLabel,
              ),
            ),
          ),
        );
        
        expect(find.bySemanticsLabel(customLabel), findsOneWidget);
      });
    });
    
    group('State Management', () {
      testWidgets('should start with correct initial state', (WidgetTester tester) async {
        final widget = AudioIconWidget(
          text: 'Test text',
          messageId: 'test_message',
          initialState: AudioIconState.loading,
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(body: widget),
          ),
        );
        
        expect(find.byIcon(Icons.sync), findsOneWidget);
      });
      
      testWidgets('should display correct icon based on initial state', (WidgetTester tester) async {
        // Test different initial states
        final states = [
          (AudioIconState.idle, Icons.volume_up_outlined),
          (AudioIconState.loading, Icons.sync),
          (AudioIconState.playing, Icons.volume_up),
          (AudioIconState.error, Icons.volume_off),
        ];
        
        for (final (state, expectedIcon) in states) {
          await tester.pumpWidget(
            MaterialApp(
              home: Scaffold(
                body: AudioIconWidget(
                  text: 'Test text',
                  messageId: 'test_message',
                  initialState: state,
                ),
              ),
            ),
          );
          
          expect(find.byIcon(expectedIcon), findsOneWidget);
          
          // Clean up for next iteration
          await tester.pumpWidget(Container());
        }
      });
    });
    
    group('User Interactions', () {
      testWidgets('should call onFirstTap when tapped in idle state (first time)', (WidgetTester tester) async {
        bool firstTapCalled = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.idle,
                isFirstTime: true,
                onFirstTap: () {
                  firstTapCalled = true;
                },
              ),
            ),
          ),
        );
        
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        expect(firstTapCalled, isTrue);
      });
      
      testWidgets('should call onSubsequentTap when tapped in idle state (not first time)', (WidgetTester tester) async {
        bool subsequentTapCalled = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.idle,
                isFirstTime: false,
                onSubsequentTap: () {
                  subsequentTapCalled = true;
                },
              ),
            ),
          ),
        );
        
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        expect(subsequentTapCalled, isTrue);
      });
      
      testWidgets('should call onSubsequentTap when tapped in playing state', (WidgetTester tester) async {
        bool subsequentTapCalled = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.playing,
                onSubsequentTap: () {
                  subsequentTapCalled = true;
                },
              ),
            ),
          ),
        );
        
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        expect(subsequentTapCalled, isTrue);
      });
      
      testWidgets('should call onRetryTap when tapped in error state', (WidgetTester tester) async {
        bool retryTapCalled = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.error,
                showRetryOnError: true,
                onRetryTap: () {
                  retryTapCalled = true;
                },
              ),
            ),
          ),
        );
        
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        expect(retryTapCalled, isTrue);
      });
      
      testWidgets('should not call onRetryTap when showRetryOnError is false', (WidgetTester tester) async {
        bool retryTapCalled = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.error,
                showRetryOnError: false,
                onRetryTap: () {
                  retryTapCalled = true;
                },
              ),
            ),
          ),
        );
        
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        expect(retryTapCalled, isFalse);
      });
      
      testWidgets('should not respond to tap when disabled', (WidgetTester tester) async {
        bool tapCalled = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                enabled: false,
                onFirstTap: () {
                  tapCalled = true;
                },
              ),
            ),
          ),
        );
        
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        expect(tapCalled, isFalse);
      });
      
      testWidgets('should not respond to tap in loading state', (WidgetTester tester) async {
        bool tapCalled = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.loading,
                onFirstTap: () {
                  tapCalled = true;
                },
              ),
            ),
          ),
        );
        
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        expect(tapCalled, isFalse);
      });
      
      testWidgets('should call onLongPress when long pressed', (WidgetTester tester) async {
        bool longPressCalled = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                onLongPress: () {
                  longPressCalled = true;
                },
              ),
            ),
          ),
        );
        
        await tester.longPress(find.byType(AudioIconWidget));
        await tester.pump();
        
        expect(longPressCalled, isTrue);
      });
      
      testWidgets('should not call onLongPress when disabled', (WidgetTester tester) async {
        bool longPressCalled = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                enabled: false,
                onLongPress: () {
                  longPressCalled = true;
                },
              ),
            ),
          ),
        );
        
        await tester.longPress(find.byType(AudioIconWidget));
        await tester.pump();
        
        expect(longPressCalled, isFalse);
      });
    });
    
    group('Animations', () {
      testWidgets('should have rotation animation in loading state', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.loading,
              ),
            ),
          ),
        );
        
        // Let animation run for a bit
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pump(const Duration(milliseconds: 100));
        
        // Should find the rotating icon
        expect(find.byIcon(Icons.sync), findsOneWidget);
      });
      
      testWidgets('should have pulse animation in playing state', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.playing,
              ),
            ),
          ),
        );
        
        // Let animation run for a bit
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pump(const Duration(milliseconds: 100));
        
        // Should find the pulsing icon
        expect(find.byIcon(Icons.volume_up), findsOneWidget);
      });
      
      testWidgets('should have shake animation in error state', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.error,
              ),
            ),
          ),
        );
        
        // Let animation run for a bit
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pump(const Duration(milliseconds: 100));
        
        // Should find the shaking icon
        expect(find.byIcon(Icons.volume_off), findsOneWidget);
      });
      
      testWidgets('should have tap scale animation when tapped', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                onFirstTap: () {},
              ),
            ),
          ),
        );
        
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump(const Duration(milliseconds: 50));
        
        // Animation should be running
        expect(find.byType(AudioIconWidget), findsOneWidget);
      });
    });
    
    group('Accessibility', () {
      testWidgets('should have proper semantics for idle state', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.idle,
                isFirstTime: true,
              ),
            ),
          ),
        );
        
        final semantics = tester.getSemantics(find.byType(AudioIconWidget));
        expect(semantics.hasFlag(SemanticsFlag.isButton), isTrue);
        expect(semantics.hasFlag(SemanticsFlag.isEnabled), isTrue);
        expect(semantics.value, contains('Ready'));
      });
      
      testWidgets('should have proper semantics for loading state', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.loading,
              ),
            ),
          ),
        );
        
        final semantics = tester.getSemantics(find.byType(AudioIconWidget));
        expect(semantics.value, contains('Loading'));
      });
      
      testWidgets('should have proper semantics for playing state', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.playing,
              ),
            ),
          ),
        );
        
        final semantics = tester.getSemantics(find.byType(AudioIconWidget));
        expect(semantics.value, contains('Playing'));
      });
      
      testWidgets('should have proper semantics for error state', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.error,
              ),
            ),
          ),
        );
        
        final semantics = tester.getSemantics(find.byType(AudioIconWidget));
        expect(semantics.value, contains('Error'));
      });
      
      testWidgets('should have minimum touch target size', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                size: 16.0, // Small icon size
              ),
            ),
          ),
        );
        
        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(AudioIconWidget),
            matching: find.byType(Container),
          ).first,
        );
        
        expect(container.constraints?.minWidth, equals(44.0));
        expect(container.constraints?.minHeight, equals(44.0));
      });
      
      testWidgets('should be disabled in semantics when enabled is false', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                enabled: false,
              ),
            ),
          ),
        );
        
        final semantics = tester.getSemantics(find.byType(AudioIconWidget));
        expect(semantics.hasFlag(SemanticsFlag.isEnabled), isFalse);
      });
    });
    
    group('Extension Methods', () {
      testWidgets('should create idle first time widget correctly', (WidgetTester tester) async {
        bool firstTapCalled = false;
        
        final widget = AudioIconWidget.idleFirstTime(
          text: 'Test text',
          messageId: 'test_message',
          onFirstTap: () {
            firstTapCalled = true;
          },
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(body: widget),
          ),
        );
        
        expect(find.byIcon(Icons.volume_up_outlined), findsOneWidget);
        
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        expect(firstTapCalled, isTrue);
      });
      
      testWidgets('should create idle subsequent widget correctly', (WidgetTester tester) async {
        bool subsequentTapCalled = false;
        
        final widget = AudioIconWidget.idleSubsequent(
          text: 'Test text',
          messageId: 'test_message',
          onSubsequentTap: () {
            subsequentTapCalled = true;
          },
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(body: widget),
          ),
        );
        
        expect(find.byIcon(Icons.volume_up_outlined), findsOneWidget);
        
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        expect(subsequentTapCalled, isTrue);
      });
      
      testWidgets('should create loading widget correctly', (WidgetTester tester) async {
        final widget = AudioIconWidget.loading(
          text: 'Test text',
          messageId: 'test_message',
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(body: widget),
          ),
        );
        
        expect(find.byIcon(Icons.sync), findsOneWidget);
      });
      
      testWidgets('should create playing widget correctly', (WidgetTester tester) async {
        bool subsequentTapCalled = false;
        
        final widget = AudioIconWidget.playing(
          text: 'Test text',
          messageId: 'test_message',
          onSubsequentTap: () {
            subsequentTapCalled = true;
          },
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(body: widget),
          ),
        );
        
        expect(find.byIcon(Icons.volume_up), findsOneWidget);
        
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        expect(subsequentTapCalled, isTrue);
      });
      
      testWidgets('should create error widget correctly', (WidgetTester tester) async {
        bool retryTapCalled = false;
        
        final widget = AudioIconWidget.error(
          text: 'Test text',
          messageId: 'test_message',
          onRetryTap: () {
            retryTapCalled = true;
          },
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(body: widget),
          ),
        );
        
        expect(find.byIcon(Icons.volume_off), findsOneWidget);
        
        await tester.tap(find.byType(AudioIconWidget));
        await tester.pump();
        
        expect(retryTapCalled, isTrue);
      });
    });
    
    group('Visual Feedback', () {
      testWidgets('should show background color in playing state', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.playing,
              ),
            ),
          ),
        );
        
        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(AudioIconWidget),
            matching: find.byType(Container),
          ).first,
        );
        
        expect(container.decoration, isA<BoxDecoration>());
        final decoration = container.decoration as BoxDecoration;
        expect(decoration.color, isNotNull);
      });
      
      testWidgets('should show background color in error state', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.error,
              ),
            ),
          ),
        );
        
        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(AudioIconWidget),
            matching: find.byType(Container),
          ).first,
        );
        
        expect(container.decoration, isA<BoxDecoration>());
        final decoration = container.decoration as BoxDecoration;
        expect(decoration.color, isNotNull);
      });
      
      testWidgets('should have tooltip with correct message', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: 'test_message',
                initialState: AudioIconState.idle,
              ),
            ),
          ),
        );
        
        expect(find.byTooltip('Play audio'), findsOneWidget);
      });
      
      testWidgets('should have different tooltip messages for different states', (WidgetTester tester) async {
        // Test tooltip messages for different initial states
        final stateTooltips = [
          (AudioIconState.idle, 'Play audio'),
          (AudioIconState.loading, 'Loading audio...'),
          (AudioIconState.playing, 'Playing audio'),
          (AudioIconState.error, 'Audio error - tap to retry'),
        ];
        
        for (final (state, expectedTooltip) in stateTooltips) {
          await tester.pumpWidget(
            MaterialApp(
              home: Scaffold(
                body: AudioIconWidget(
                  text: 'Test text',
                  messageId: 'test_message',
                  initialState: state,
                ),
              ),
            ),
          );
          
          expect(find.byTooltip(expectedTooltip), findsOneWidget);
          
          // Clean up for next iteration
          await tester.pumpWidget(Container());
        }
      });
    });
    
    group('Edge Cases', () {
      testWidgets('should handle empty text gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: '',
                messageId: 'test_message',
              ),
            ),
          ),
        );
        
        expect(find.byType(AudioIconWidget), findsOneWidget);
        expect(find.byIcon(Icons.volume_up_outlined), findsOneWidget);
      });
      
      testWidgets('should handle empty message ID gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: 'Test text',
                messageId: '',
              ),
            ),
          ),
        );
        
        expect(find.byType(AudioIconWidget), findsOneWidget);
        expect(find.byIcon(Icons.volume_up_outlined), findsOneWidget);
      });
      
      testWidgets('should handle very long text gracefully', (WidgetTester tester) async {
        final longText = 'A' * 1000;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: longText,
                messageId: 'test_message',
              ),
            ),
          ),
        );
        
        expect(find.byType(AudioIconWidget), findsOneWidget);
        expect(find.byIcon(Icons.volume_up_outlined), findsOneWidget);
      });
      
      testWidgets('should handle special characters in text and message ID', (WidgetTester tester) async {
        const specialText = 'Test with special chars: äöü ß @#$%^&*()';
        const specialMessageId = 'message-with_special.chars@123';
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: AudioIconWidget(
                text: specialText,
                messageId: specialMessageId,
              ),
            ),
          ),
        );
        
        expect(find.byType(AudioIconWidget), findsOneWidget);
        expect(find.byIcon(Icons.volume_up_outlined), findsOneWidget);
      });
    });
  });
}