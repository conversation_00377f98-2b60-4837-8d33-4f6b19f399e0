#Tue Jul 29 16:20:38 CEST 2025
path.4=0/classes.dex
path.3=8/classes.dex
path.2=15/classes.dex
path.1=12/classes.dex
path.7=4/classes.dex
path.6=6/classes.dex
path.5=1/classes.dex
path.0=classes.dex
base.4=C\:\\Users\\jlasq\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.3=C\:\\Users\\jlasq\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\8\\classes.dex
base.2=C\:\\Users\\jlasq\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\15\\classes.dex
base.1=C\:\\Users\\jlasq\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\12\\classes.dex
base.0=C\:\\Users\\jlasq\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
renamed.3=classes4.dex
renamed.2=classes3.dex
renamed.1=classes2.dex
renamed.0=classes.dex
renamed.7=classes8.dex
renamed.6=classes7.dex
base.7=C\:\\Users\\jlasq\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\4\\classes.dex
renamed.5=classes6.dex
base.6=C\:\\Users\\jlasq\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\6\\classes.dex
renamed.4=classes5.dex
base.5=C\:\\Users\\jlasq\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
