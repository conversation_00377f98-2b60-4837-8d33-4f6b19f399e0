import 'correction_result.dart';

/// Model for Groq AI responses
class GroqResponse {
  final String id;
  final String text;
  final String type;
  final CorrectionResult? correctionResult;
  final DateTime timestamp;
  
  const GroqResponse({
    required this.id,
    required this.text,
    required this.type,
    this.correctionResult,
    required this.timestamp,
  });
  
  /// Create from JSON response
  factory GroqResponse.fromJson(Map<String, dynamic> json) {
    return GroqResponse(
      id: json['id'] ?? '',
      text: json['text'] ?? '',
      type: json['type'] ?? 'response',
      correctionResult: json['correction_result'] != null
          ? CorrectionResult.fromJson(json['correction_result'])
          : null,
      timestamp: DateTime.now(),
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'type': type,
      'correction_result': correctionResult?.toJson(),
      'timestamp': timestamp.toIso8601String(),
    };
  }
  
  @override
  String toString() {
    return 'GroqResponse(id: $id, type: $type, text: ${text.substring(0, text.length > 50 ? 50 : text.length)}...)';
  }
}