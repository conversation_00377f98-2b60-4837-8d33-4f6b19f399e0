# Requirements Document

## Introduction

This feature adds professional-grade user authentication and database functionality to the German language learning app using Supabase as the backend service. The system provides secure authentication (Google OAuth and email/password), user profile management with a credit-based usage system, and session tracking for analytics. The implementation follows modern development best practices while maintaining simplicity and focusing on the core needs of a language learning application.

## Requirements

### Requirement 1: User Authentication System

**User Story:** As a user, I want to securely sign up and log in to the app using Google OAuth or email/password, so that I can access personalized features and have my usage tracked.

#### Acceptance Criteria

1. WHEN a user opens the app for the first time THEN the system SHALL present authentication options (Google OAuth and email/password)
2. WHEN a user selects Google OAuth THEN the system SHALL redirect to Google's authentication flow and handle the OAuth callback securely
3. WHEN a user provides email and password for signup THEN the system SHALL validate email format, enforce password strength requirements (minimum 8 characters), and create account if validation passes
4. WHEN a user provides valid existing credentials for login THEN the system SHALL authenticate the user and grant access
5. W<PERSON><PERSON> authentication fails THEN the system SHALL display appropriate error messages and implement basic rate limiting (5 attempts per 15 minutes)
6. WH<PERSON> a user signs up with email/password THEN the system SHALL send an email verification link and require email confirmation before account activation
7. WH<PERSON> a user is successfully authenticated THEN the system SHALL store the session securely and redirect to the main app interface
8. WHEN a session expires THEN the system SHALL automatically refresh tokens or prompt for re-authentication

### Requirement 2: User Profile Management

**User Story:** As a new user, I want my profile to be automatically created with trial plan details when I sign up, so that I can immediately start using the app with allocated credits.

#### Acceptance Criteria

1. WHEN a user successfully completes signup THEN the system SHALL create an entry in the Users table with email, plan set to "Trial", current date as date_joined and date_plan, max_credits set to 20, and current_credits set to 20
2. WHEN a user's profile is created THEN the system SHALL ensure all required fields are populated with appropriate default values
3. WHEN there is an error creating the user profile THEN the system SHALL handle the error gracefully and notify the user with retry options
4. IF a user attempts to sign up with an existing email THEN the system SHALL prevent duplicate account creation and display appropriate messaging
5. WHEN profile data is stored THEN the system SHALL use Supabase's built-in security features and row-level security policies

### Requirement 3: Session Tracking System

**User Story:** As the system, I want to track every user interaction in a Sessions table, so that usage can be monitored, billed, and analyzed.

#### Acceptance Criteria

1. WHEN a user makes a request that consumes credits THEN the system SHALL create a new entry in the Sessions table with incremental Session ID, user's email, message content, response content, and UTC datetime
2. WHEN a session is logged THEN the system SHALL decrement the user's current_credits by 1
3. WHEN a user's current_credits reaches 0 THEN the system SHALL prevent further usage until credits are refreshed or plan is upgraded
4. WHEN session logging fails THEN the system SHALL handle the error without blocking the user's request but log the failure for investigation
5. WHEN session data is stored THEN the system SHALL use UTC timestamps for consistency across different user locations

### Requirement 4: Credit Management System

**User Story:** As a user, I want my credits to automatically refresh monthly based on my plan start date, so that I can continue using the app without manual intervention.

#### Acceptance Criteria

1. WHEN the current date matches or exceeds the user's monthly refresh date THEN the system SHALL reset current_credits to equal max_credits
2. WHEN credits are refreshed THEN the system SHALL update the date_plan to the new refresh cycle start date
3. WHEN a user consumes a credit through app usage THEN the system SHALL decrement current_credits by 1 and ensure it cannot go below 0
4. IF current_credits is 0 THEN the system SHALL prevent credit-consuming actions and display appropriate messaging about credit limits
5. WHEN calculating the refresh date THEN the system SHALL handle edge cases (leap years, month-end dates) and provide accurate next refresh date

### Requirement 5: Profile Information Display

**User Story:** As a user, I want to view my profile information including email, plan details, and credit status in a profile popup, so that I can monitor my account status and usage.

#### Acceptance Criteria

1. WHEN a user clicks the profile icon THEN the system SHALL display a popup with email, date joined, plan start date, current credits/max credits ratio, and next refresh date
2. WHEN calculating the refresh date THEN the system SHALL determine the next occurrence of the same day of month as the date_plan (e.g., if plan started on 16th, next refresh is next month's 16th)
3. WHEN displaying dates THEN the system SHALL format them in a user-friendly manner appropriate for the user's locale
4. WHEN the profile popup is displayed THEN the system SHALL ensure all information is current and accurately reflects the user's account status
5. WHEN there is an error retrieving profile information THEN the system SHALL display appropriate error messaging and allow retry

### Requirement 6: Database Schema Implementation

**User Story:** As the system, I want properly structured database tables to store user and session data, so that information is organized, queryable, and maintainable.

#### Acceptance Criteria

1. WHEN the system is deployed THEN the Users table SHALL exist with columns: email (primary key), plan (text), date_joined (timestamp), date_plan (timestamp), max_credits (integer), current_credits (integer)
2. WHEN the system is deployed THEN the Sessions table SHALL exist with columns: session_id (auto-incrementing primary key), email (foreign key to Users), message (text), response (text), datetime (UTC timestamp)
3. WHEN database operations are performed THEN the system SHALL ensure data integrity through appropriate constraints and relationships
4. WHEN storing timestamps THEN the system SHALL use UTC timezone for consistency across different user locations
5. WHEN sensitive data is stored THEN the system SHALL use Supabase's built-in security features and row-level security policies

### Requirement 7: Supabase Integration

**User Story:** As a developer, I want the app to integrate seamlessly with the existing Supabase instance, so that authentication and data storage work reliably with the provided credentials.

#### Acceptance Criteria

1. WHEN the app initializes THEN the system SHALL connect to the Supabase instance at https://eaemndginhddncydpaix.supabase.co using the provided API key
2. WHEN authentication operations are performed THEN the system SHALL use Supabase Auth for user management and session handling
3. WHEN database operations are performed THEN the system SHALL use Supabase's database client for all CRUD operations
4. WHEN there are connection issues THEN the system SHALL handle Supabase service errors gracefully and provide appropriate user feedback
5. IF API rate limits are reached THEN the system SHALL implement appropriate retry logic and user notification
6. WHEN errors occur THEN the system SHALL log them appropriately for debugging and monitoring