import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';

/// Service for managing accessibility features and compliance
class AccessibilityService {
  static final AccessibilityService _instance = AccessibilityService._();
  static AccessibilityService get instance => _instance;
  
  bool _isHighContrastEnabled = false;
  bool _isLargeTextEnabled = false;
  bool _isScreenReaderEnabled = false;
  double _textScaleFactor = 1.0;
  
  AccessibilityService._();
  
  /// Initialize accessibility service and detect system settings
  Future<void> initialize() async {
    await _detectSystemAccessibilitySettings();
    _setupAccessibilityListeners();
  }
  
  /// Detect current system accessibility settings
  Future<void> _detectSystemAccessibilitySettings() async {
    try {
      // Check if screen reader is enabled
      _isScreenReaderEnabled = SemanticsBinding.instance.accessibilityFeatures.accessibleNavigation;
      
      // Check if high contrast is enabled
      _isHighContrastEnabled = SemanticsBinding.instance.accessibilityFeatures.highContrast;
      
      // Check if large text is enabled
      _isLargeTextEnabled = SemanticsBinding.instance.accessibilityFeatures.boldText;
      
      // Get text scale factor
      _textScaleFactor = WidgetsBinding.instance.window.textScaleFactor;
      
    } catch (e) {
      debugPrint('Error detecting accessibility settings: $e');
    }
  }
  
  /// Set up listeners for accessibility changes
  void _setupAccessibilityListeners() {
    WidgetsBinding.instance.window.onAccessibilityFeaturesChanged = () {
      _detectSystemAccessibilitySettings();
    };
  }
  
  /// Whether high contrast mode is enabled
  bool get isHighContrastEnabled => _isHighContrastEnabled;
  
  /// Whether large text is enabled
  bool get isLargeTextEnabled => _isLargeTextEnabled;
  
  /// Whether screen reader is enabled
  bool get isScreenReaderEnabled => _isScreenReaderEnabled;
  
  /// Current text scale factor
  double get textScaleFactor => _textScaleFactor;
  
  /// Get accessible theme based on current settings
  ThemeData getAccessibleTheme({required bool isDarkMode}) {
    return ThemeData(
      brightness: isDarkMode ? Brightness.dark : Brightness.light,
      
      // High contrast colors
      primaryColor: _isHighContrastEnabled 
          ? (isDarkMode ? Colors.white : Colors.black)
          : const Color(0xFF3b82f6),
      
      backgroundColor: _isHighContrastEnabled
          ? (isDarkMode ? Colors.black : Colors.white)
          : (isDarkMode ? const Color(0xFF0f172a) : const Color(0xFFf8fafc)),
      
      // Accessible text theme
      textTheme: _getAccessibleTextTheme(isDarkMode),
      
      // Accessible button theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          minimumSize: const Size(48, 48), // Minimum touch target size
          textStyle: TextStyle(
            fontSize: _getAccessibleFontSize(16),
            fontWeight: _isLargeTextEnabled ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
      
      // Accessible input decoration
      inputDecorationTheme: InputDecorationTheme(
        contentPadding: const EdgeInsets.all(16),
        border: OutlineInputBorder(
          borderWidth: _isHighContrastEnabled ? 3.0 : 1.0,
        ),
      ),
    );
  }
  
  /// Get accessible text theme
  TextTheme _getAccessibleTextTheme(bool isDarkMode) {
    final baseColor = _isHighContrastEnabled
        ? (isDarkMode ? Colors.white : Colors.black)
        : (isDarkMode ? const Color(0xFFf8fafc) : const Color(0xFF1e293b));
    
    return TextTheme(
      headline1: TextStyle(
        fontSize: _getAccessibleFontSize(32),
        fontWeight: _isLargeTextEnabled ? FontWeight.bold : FontWeight.w600,
        color: baseColor,
      ),
      headline2: TextStyle(
        fontSize: _getAccessibleFontSize(28),
        fontWeight: _isLargeTextEnabled ? FontWeight.bold : FontWeight.w600,
        color: baseColor,
      ),
      headline3: TextStyle(
        fontSize: _getAccessibleFontSize(24),
        fontWeight: _isLargeTextEnabled ? FontWeight.bold : FontWeight.w600,
        color: baseColor,
      ),
      bodyText1: TextStyle(
        fontSize: _getAccessibleFontSize(16),
        fontWeight: _isLargeTextEnabled ? FontWeight.w500 : FontWeight.normal,
        color: baseColor,
      ),
      bodyText2: TextStyle(
        fontSize: _getAccessibleFontSize(14),
        fontWeight: _isLargeTextEnabled ? FontWeight.w500 : FontWeight.normal,
        color: baseColor,
      ),
      button: TextStyle(
        fontSize: _getAccessibleFontSize(16),
        fontWeight: FontWeight.w600,
        color: baseColor,
      ),
    );
  }
  
  /// Calculate accessible font size based on system settings
  double _getAccessibleFontSize(double baseSize) {
    double scaledSize = baseSize * _textScaleFactor;
    
    // Apply additional scaling for large text mode
    if (_isLargeTextEnabled) {
      scaledSize *= 1.2;
    }
    
    // Ensure minimum readable size
    return scaledSize.clamp(12.0, 48.0);
  }
  
  /// Create accessible semantics for widgets
  Widget makeAccessible({
    required Widget child,
    required String label,
    String? hint,
    bool isButton = false,
    bool isHeader = false,
    bool isLiveRegion = false,
    VoidCallback? onTap,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      button: isButton,
      header: isHeader,
      liveRegion: isLiveRegion,
      onTap: onTap,
      child: child,
    );
  }
  
  /// Announce message to screen reader
  void announceToScreenReader(String message) {
    if (_isScreenReaderEnabled) {
      SemanticsService.announce(message, TextDirection.ltr);
    }
  }
  
  /// Provide haptic feedback for accessibility
  void provideHapticFeedback({HapticFeedbackType type = HapticFeedbackType.selection}) {
    HapticFeedback.selectionClick();
  }
  
  /// Check if color contrast is sufficient
  bool hasGoodContrast(Color foreground, Color background) {
    final luminance1 = foreground.computeLuminance();
    final luminance2 = background.computeLuminance();
    
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    
    final contrastRatio = (lighter + 0.05) / (darker + 0.05);
    
    // WCAG AA standard requires 4.5:1 for normal text, 3:1 for large text
    return contrastRatio >= 4.5;
  }
  
  /// Get accessible colors with good contrast
  AccessibleColors getAccessibleColors({required bool isDarkMode}) {
    if (_isHighContrastEnabled) {
      return AccessibleColors(
        primary: isDarkMode ? Colors.white : Colors.black,
        secondary: isDarkMode ? Colors.grey[300]! : Colors.grey[700]!,
        background: isDarkMode ? Colors.black : Colors.white,
        surface: isDarkMode ? Colors.grey[900]! : Colors.grey[100]!,
        error: isDarkMode ? Colors.red[300]! : Colors.red[700]!,
        success: isDarkMode ? Colors.green[300]! : Colors.green[700]!,
        warning: isDarkMode ? Colors.orange[300]! : Colors.orange[700]!,
        text: isDarkMode ? Colors.white : Colors.black,
        textSecondary: isDarkMode ? Colors.grey[300]! : Colors.grey[700]!,
      );
    } else {
      return AccessibleColors(
        primary: const Color(0xFF3b82f6),
        secondary: const Color(0xFF64748b),
        background: isDarkMode ? const Color(0xFF0f172a) : const Color(0xFFf8fafc),
        surface: isDarkMode ? const Color(0xFF1e293b) : Colors.white,
        error: const Color(0xFFef4444),
        success: const Color(0xFF10b981),
        warning: const Color(0xFFf59e0b),
        text: isDarkMode ? const Color(0xFFf8fafc) : const Color(0xFF1e293b),
        textSecondary: isDarkMode ? const Color(0xFF94a3b8) : const Color(0xFF64748b),
      );
    }
  }
  
  /// Create accessible focus decoration
  BoxDecoration getAccessibleFocusDecoration({required bool isDarkMode}) {
    final colors = getAccessibleColors(isDarkMode: isDarkMode);
    
    return BoxDecoration(
      border: Border.all(
        color: colors.primary,
        width: _isHighContrastEnabled ? 3.0 : 2.0,
      ),
      borderRadius: BorderRadius.circular(4),
    );
  }
  
  /// Get minimum touch target size
  Size get minimumTouchTargetSize => const Size(48, 48);

  /// Check if touch target meets accessibility guidelines
  bool isTouchTargetAccessible(Size size) {
    return size.width >= minimumTouchTargetSize.width &&
           size.height >= minimumTouchTargetSize.height;
  }
}
}

/// Accessible color scheme
class AccessibleColors {
  final Color primary;
  final Color secondary;
  final Color background;
  final Color surface;
  final Color error;
  final Color success;
  final Color warning;
  final Color text;
  final Color textSecondary;
  
  const AccessibleColors({
    required this.primary,
    required this.secondary,
    required this.background,
    required this.surface,
    required this.error,
    required this.success,
    required this.warning,
    required this.text,
    required this.textSecondary,
  });
}
