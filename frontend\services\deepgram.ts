import { Platform } from 'react-native';
import { MobileAudioStreamer } from './mobileAudio';

interface WebSocketMessage {
  message_type: 'info' | 'session_started' | 'partial_transcript' | 'final_transcript' | 'processing' | 'groq_response' | 'timeout' | 'error';
  data: any;
}

interface GroqResponse {
  original_text: string;
  response_text: string;
  processing_time: number;
}

export class DeutschKorrektSTT {
  private ws: WebSocket | null = null;
  private mediaStreamer: MediaRecorder | null = null;
  private audioContext: AudioContext | null = null;
  private mobileAudioStreamer: MobileAudioStreamer | null = null;
  private isStreaming: boolean = false;
  private onPartialTranscript?: (text: string) => void;
  private onFinalTranscript?: (text: string) => void;
  private onProcessing?: () => void;
  private onGroqResponse?: (result: GroqResponse) => void;
  private onError?: (error: string) => void;
  private onTimeout?: () => void;
  private onInfo?: (message: string) => void;

  constructor(private backendUrl: string = 'wss://deutschkorrekt-backend-645996191396.europe-west3.run.app') {
    // Initialize mobile audio streamer for non-web platforms
    if (Platform.OS !== 'web') {
      this.mobileAudioStreamer = new MobileAudioStreamer();
    }
  }

  connect(callbacks: {
    onPartialTranscript?: (text: string) => void;
    onFinalTranscript?: (text: string) => void;
    onProcessing?: () => void;
    onGroqResponse?: (result: GroqResponse) => void;
    onError?: (error: string) => void;
    onTimeout?: () => void;
    onInfo?: (message: string) => void;
  }) {
    this.onPartialTranscript = callbacks.onPartialTranscript;
    this.onFinalTranscript = callbacks.onFinalTranscript;
    this.onProcessing = callbacks.onProcessing;
    this.onGroqResponse = callbacks.onGroqResponse;
    this.onError = callbacks.onError;
    this.onTimeout = callbacks.onTimeout;
    this.onInfo = callbacks.onInfo;

    // Connect to DeutschKorrekt FastAPI WebSocket endpoint
    const wsUrl = `${this.backendUrl}/stt`;
    console.log('🔗 Connecting to WebSocket:', wsUrl);

    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log('✅ DeutschKorrekt STT WebSocket connected');
      this.onInfo?.('Connected to Deutschkorrekt STT service');
    };

    this.ws.onmessage = (event) => {
      try {
        console.log('📨 Received message:', event.data);
        const rawMessage = JSON.parse(event.data);

        // Handle both message formats: "type" and "message_type"
        const messageType = rawMessage.message_type || rawMessage.type;
        const message = {
          message_type: messageType,
          data: rawMessage.data
        };

        switch (message.message_type) {
          case 'info':
            this.onInfo?.(message.data.message);
            break;
          case 'session_started':
            console.log('🎯 Session started:', message.data.session_id);
            break;
          case 'partial_transcript':
            console.log('🎯 Partial transcript received:', message.data.text);
            if (message.data.text) {
              this.onPartialTranscript?.(message.data.text);
            }
            break;
          case 'final_transcript':
            console.log('🎯 Final transcript received:', message.data.text);
            console.log('🎯 Auto-ended by Deepgram:', message.data.speech_final);
            if (message.data.text) {
              this.onFinalTranscript?.(message.data.text);
            }
            break;
          case 'processing':
            console.log('⚡ Processing with Groq...');
            this.onProcessing?.();
            break;
          case 'groq_response':
            console.log('🤖 Groq response received:', message.data);
            if (message.data) {
              this.onGroqResponse?.(message.data as GroqResponse);
            }
            break;
          case 'error':
            this.onError?.(message.data.message || 'Unknown error occurred');
            break;
          case 'timeout':
            this.onTimeout?.();
            this.onInfo?.(message.data.message);
            break;
        }
      } catch (error) {
        console.error('Error parsing DeutschKorrekt response:', error);
        this.onError?.('Error parsing server response');
      }
    };

    this.ws.onerror = (error) => {
      console.error('DeutschKorrekt WebSocket error:', error);
      this.onError?.('Connection error occurred');
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code);
    };
  }

  async startRealTimeStreaming() {
    try {
      this.isStreaming = true;
      console.log('🎤 Audio streaming started');

      // Platform-specific implementation
      if (Platform.OS === 'web') {
        await this.startWebStreaming();
      } else {
        await this.startMobileStreaming();
      }

    } catch (error) {
      console.error('Error starting real-time streaming:', error);
      this.onError?.('Failed to access microphone');
    }
  }

  private async startWebStreaming() {
    try {
      // Get microphone access
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
        }
      });

      // Create MediaRecorder for real-time streaming
      this.mediaStreamer = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus',
        audioBitsPerSecond: 16000,
      });

      this.mediaStreamer.ondataavailable = (event) => {
        if (event.data.size > 0 && this.ws?.readyState === WebSocket.OPEN) {
          // Send audio data to WebSocket
          this.ws.send(event.data);
        }
      };

      // Start recording with small time slices for real-time streaming
      this.mediaStreamer.start(100); // 100ms chunks

    } catch (error) {
      console.error('Error starting web streaming:', error);
      throw error;
    }
  }

  private async startMobileStreaming() {
    if (!this.mobileAudioStreamer) {
      throw new Error('Mobile audio streamer not initialized');
    }

    try {
      await this.mobileAudioStreamer.startStreaming((audioData) => {
        // Send audio data to WebSocket
        if (this.ws?.readyState === WebSocket.OPEN) {
          this.ws.send(audioData);
        }
      });
    } catch (error) {
      console.error('Error starting mobile streaming:', error);
      throw error;
    }
  }

  stopRealTimeStreaming() {
    console.log('🛑 Audio streaming stopped');
    this.isStreaming = false;

    if (Platform.OS === 'web') {
      // Stop web streaming
      if (this.mediaStreamer && this.mediaStreamer.state !== 'inactive') {
        this.mediaStreamer.stop();
      }
      
      // Stop all audio tracks
      if (this.mediaStreamer?.stream) {
        this.mediaStreamer.stream.getTracks().forEach(track => track.stop());
      }
    } else {
      // Stop mobile streaming
      this.mobileAudioStreamer?.stopStreaming();
    }
  }

  disconnect() {
    this.stopRealTimeStreaming();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}