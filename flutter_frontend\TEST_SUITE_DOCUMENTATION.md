# DeutschKorrekt Test Suite Documentation

This document provides comprehensive information about the test suite for the DeutschKorrekt Flutter application.

## Test Structure Overview

The test suite is organized into four main categories:

1. **Unit Tests** - Test individual components in isolation
2. **Widget Tests** - Test UI components and their interactions
3. **Integration Tests** - Test component interactions and workflows
4. **End-to-End Tests** - Test complete user journeys

## Test Categories

### Unit Tests (`test/`)

#### Services Tests
- `test/services/audio_focus_service_test.dart` - Audio focus management
- `test/services/audio_isolate_processor_test.dart` - Audio processing in isolates
- `test/services/websocket_connection_pool_test.dart` - WebSocket connection pooling
- `test/services/realtime_audio_service_test.dart` - Real-time audio recording
- `test/services/websocket_service_test.dart` - WebSocket communication
- `test/services/permissions_service_test.dart` - Permission handling
- `test/services/app_lifecycle_service_test.dart` - App lifecycle management

#### Models Tests
- `test/models/correction_result_test.dart` - Correction result data model
- `test/models/message_test.dart` - Message data model
- `test/models/app_error_test.dart` - Error handling model
- `test/models/websocket_message_test.dart` - WebSocket message model

#### Providers Tests
- `test/providers/audio_provider_test.dart` - Audio state management
- `test/providers/conversation_provider_test.dart` - Conversation state management
- `test/providers/processing_provider_test.dart` - Processing state management

### Widget Tests (`test/widgets/`)

#### UI Components
- `test/widgets/correction_result_test.dart` - Correction result display
- `test/widgets/message_bubble_test.dart` - Chat message bubbles
- `test/widgets/record_button_test.dart` - Recording button component
- `test/widgets/processing_indicator_test.dart` - Processing status indicator
- `test/widgets/error_dialog_test.dart` - Error display dialogs

#### Screens
- `test/widgets/home_screen_test.dart` - Main application screen
- `test/widgets/settings_screen_test.dart` - Settings and configuration
- `test/widgets/conversation_screen_test.dart` - Chat conversation view

### Integration Tests (`test/integration/`)

#### Workflows
- `test/integration/audio_recording_integration_test.dart` - Complete recording workflow
- `test/integration/websocket_communication_test.dart` - Backend communication
- `test/integration/error_handling_integration_test.dart` - Error scenarios
- `test/integration/app_lifecycle_integration_test.dart` - Lifecycle management

### End-to-End Tests (`integration_test/`)

#### User Journeys
- `integration_test/complete_user_journey_test.dart` - Full app usage flow
- `integration_test/performance_test.dart` - Performance benchmarks
- `integration_test/accessibility_test.dart` - Accessibility compliance

## Running Tests

### Prerequisites

1. **Flutter SDK** - Latest stable version
2. **Dependencies** - Run `flutter pub get`
3. **Test Dependencies** - Mockito, integration_test package
4. **Device/Emulator** - For integration and E2E tests

### Command Line Usage

#### Unit Tests
```bash
# Run all unit tests
flutter test

# Run specific test file
flutter test test/services/audio_focus_service_test.dart

# Run tests with coverage
flutter test --coverage

# Run tests in specific directory
flutter test test/services/
```

#### Widget Tests
```bash
# Run all widget tests
flutter test test/widgets/

# Run specific widget test
flutter test test/widgets/correction_result_test.dart

# Run with verbose output
flutter test test/widgets/ --verbose
```

#### Integration Tests
```bash
# Run integration tests on connected device
flutter test integration_test/

# Run on specific device
flutter test integration_test/ -d <device_id>

# Run with performance profiling
flutter test integration_test/ --profile
```

#### End-to-End Tests
```bash
# Run E2E tests
flutter drive --driver=test_driver/integration_test.dart --target=integration_test/complete_user_journey_test.dart

# Run on specific platform
flutter drive --driver=test_driver/integration_test.dart --target=integration_test/complete_user_journey_test.dart -d android

# Run with screenshots
flutter drive --driver=test_driver/integration_test.dart --target=integration_test/complete_user_journey_test.dart --screenshot
```

### IDE Integration

#### VS Code
1. Install Flutter extension
2. Use Test Explorer for visual test running
3. Set breakpoints in test files for debugging
4. Use `Ctrl+Shift+P` → "Flutter: Run Tests" for quick access

#### Android Studio
1. Right-click test files to run
2. Use gutter icons for individual test execution
3. View test results in integrated console
4. Debug tests with full IDE support

## Test Configuration

### Mock Setup

Tests use Mockito for mocking dependencies:

```dart
@GenerateMocks([RealtimeAudioService, WebSocketService])
import 'test_file.mocks.dart';
```

### Test Data

Common test data is provided through helper functions:

```dart
// Generate test audio data
Uint8List generateTestAudioData(int sampleCount);

// Create test correction results
CorrectionResult createTestCorrectionResult();

// Generate test messages
Message createTestMessage({bool isUser = true});
```

### Environment Setup

#### Test Environment Variables
```bash
export FLUTTER_TEST_TIMEOUT=60s
export FLUTTER_TEST_DEVICE=emulator-5554
```

#### Test Configuration Files
- `test/test_config.dart` - Global test configuration
- `test/helpers/test_helpers.dart` - Common test utilities
- `test/mocks/mock_services.dart` - Shared mock implementations

## Coverage Requirements

### Minimum Coverage Targets
- **Unit Tests**: 90% line coverage
- **Widget Tests**: 85% widget coverage
- **Integration Tests**: 80% workflow coverage
- **Overall**: 85% total coverage

### Coverage Reports

Generate coverage reports:

```bash
# Generate coverage data
flutter test --coverage

# Generate HTML report
genhtml coverage/lcov.info -o coverage/html

# View coverage report
open coverage/html/index.html
```

### Coverage Exclusions

Files excluded from coverage requirements:
- Generated files (`*.g.dart`, `*.freezed.dart`)
- Main entry points (`main.dart`)
- Platform-specific code with conditional compilation
- Third-party package wrappers

## Continuous Integration

### GitHub Actions

```yaml
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter pub get
      - run: flutter test --coverage
      - run: flutter test integration_test/
```

### Test Automation

#### Pre-commit Hooks
- Run unit tests before commit
- Check test coverage thresholds
- Lint test files for style compliance

#### Pull Request Checks
- All tests must pass
- Coverage must meet minimum thresholds
- No new test failures introduced

## Performance Testing

### Benchmarks

Performance tests measure:
- App startup time
- Audio processing latency
- Memory usage during recording
- UI responsiveness under load
- WebSocket connection performance

### Performance Thresholds

```dart
// Example performance assertions
expect(startupTime, lessThan(Duration(seconds: 3)));
expect(audioLatency, lessThan(Duration(milliseconds: 100)));
expect(memoryUsage, lessThan(100 * 1024 * 1024)); // 100MB
```

## Accessibility Testing

### Automated Checks
- Semantic labels presence
- Contrast ratio compliance
- Touch target size validation
- Screen reader compatibility

### Manual Testing Checklist
- [ ] VoiceOver/TalkBack navigation
- [ ] Keyboard-only navigation
- [ ] High contrast mode support
- [ ] Large text scaling support

## Debugging Tests

### Common Issues

#### Flaky Tests
- Use `pumpAndSettle()` for async operations
- Add proper delays for animations
- Mock time-dependent operations

#### Memory Leaks
- Dispose providers and controllers
- Cancel stream subscriptions
- Clear static references

#### Platform Differences
- Use platform-specific test conditions
- Mock platform channels appropriately
- Handle permission differences

### Debug Tools

```dart
// Enable debug output
debugPrint('Test debug info: $value');

// Dump widget tree
debugDumpApp();

// Print render tree
debugDumpRenderTree();
```

## Best Practices

### Test Organization
1. Group related tests with `group()`
2. Use descriptive test names
3. Follow AAA pattern (Arrange, Act, Assert)
4. Keep tests independent and isolated

### Mock Usage
1. Mock external dependencies
2. Verify important interactions
3. Use realistic test data
4. Reset mocks between tests

### Assertion Guidelines
1. Test one concept per test
2. Use specific matchers
3. Provide meaningful error messages
4. Test both positive and negative cases

### Maintenance
1. Update tests with code changes
2. Remove obsolete tests
3. Refactor common test code
4. Keep test dependencies updated

## Troubleshooting

### Common Test Failures

#### Permission Issues
```bash
# Grant microphone permission for tests
adb shell pm grant com.example.deutschkorrekt android.permission.RECORD_AUDIO
```

#### Network Issues
```bash
# Check WebSocket connectivity
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" wss://deutschkorrekt-backend-************.europe-west3.run.app/stt
```

#### Device Issues
```bash
# List available devices
flutter devices

# Clear device cache
flutter clean
```

### Support Resources

- [Flutter Testing Documentation](https://docs.flutter.dev/testing)
- [Mockito Package Documentation](https://pub.dev/packages/mockito)
- [Integration Test Package](https://pub.dev/packages/integration_test)
- [DeutschKorrekt GitHub Issues](https://github.com/jlasquetyreyes/deutschkorrekt/issues)
