"""
Health check and warm-up endpoints
"""
import asyncio
from fastapi import APIRouter
from services.stt_service import DeepgramService
from services.language_service import LanguageService
from config.settings import settings

router = APIRouter()

@router.get("/health")
async def health_check():
    """Basic health check"""
    return {"status": "healthy", "timestamp": asyncio.get_event_loop().time()}

@router.get("/warmup")
async def warmup():
    """Warm up all services to reduce cold start latency"""
    try:
        # Pre-initialize services
        api_key = settings.DEEPGRAM_API_KEY or settings.deepgram_api_key
        if api_key:
            stt_service = DeepgramService(api_key=api_key, enable_multilingual=False)
        
        language_service = LanguageService()
        
        return {"status": "warmed", "services": ["deepgram", "groq"]}
    except Exception as e:
        return {"status": "error", "error": str(e)}