/// User profile model for DeutschKorrekt app
class UserProfile {
  final String email;
  final String plan;
  final DateTime dateJoined;
  final DateTime datePlan;
  final int maxCredits;
  final int currentCredits;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const UserProfile({
    required this.email,
    required this.plan,
    required this.dateJoined,
    required this.datePlan,
    required this.maxCredits,
    required this.currentCredits,
    this.createdAt,
    this.updatedAt,
  });

  /// Create UserProfile from JSON
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      email: json['email'] as String,
      plan: json['plan'] as String,
      dateJoined: DateTime.parse(json['date_joined'] as String),
      datePlan: DateTime.parse(json['date_plan'] as String),
      maxCredits: json['max_credits'] as int,
      currentCredits: json['current_credits'] as int,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  /// Convert UserProfile to JSON
  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'plan': plan,
      'date_joined': dateJoined.toIso8601String(),
      'date_plan': datePlan.toIso8601String(),
      'max_credits': maxCredits,
      'current_credits': currentCredits,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }

  /// Calculate the next refresh date based on plan start date
  DateTime get nextRefreshDate {
    final now = DateTime.now();
    final planDay = datePlan.day;
    
    // Start with current month
    var nextRefresh = DateTime(now.year, now.month, planDay);
    
    // If the refresh date for this month has passed, move to next month
    if (nextRefresh.isBefore(now) || nextRefresh.isAtSameMomentAs(now)) {
      // Handle month overflow
      if (now.month == 12) {
        nextRefresh = DateTime(now.year + 1, 1, planDay);
      } else {
        nextRefresh = DateTime(now.year, now.month + 1, planDay);
      }
    }
    
    // Handle edge case where the target day doesn't exist in the target month
    // (e.g., January 31st -> February 31st doesn't exist)
    final targetMonth = nextRefresh.month;
    final targetYear = nextRefresh.year;
    final lastDayOfTargetMonth = DateTime(targetYear, targetMonth + 1, 0).day;
    
    if (planDay > lastDayOfTargetMonth) {
      nextRefresh = DateTime(targetYear, targetMonth, lastDayOfTargetMonth);
    }
    
    return nextRefresh;
  }

  /// Check if credits need to be refreshed
  bool get needsRefresh {
    final now = DateTime.now();
    final refreshDate = DateTime(now.year, now.month, datePlan.day);
    
    // If current date is on or after the refresh date for this month
    // and the plan date is from a previous month
    return now.isAfter(refreshDate) && 
           (datePlan.month != now.month || datePlan.year != now.year);
  }

  /// Check if user has credits available
  bool get hasCreditsAvailable => currentCredits > 0;

  /// Get credits usage percentage
  double get creditsUsagePercentage {
    if (maxCredits == 0) return 0.0;
    return (maxCredits - currentCredits) / maxCredits;
  }

  /// Get remaining credits percentage
  double get remainingCreditsPercentage {
    if (maxCredits == 0) return 0.0;
    return currentCredits / maxCredits;
  }

  /// Create a copy of UserProfile with updated fields
  UserProfile copyWith({
    String? email,
    String? plan,
    DateTime? dateJoined,
    DateTime? datePlan,
    int? maxCredits,
    int? currentCredits,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfile(
      email: email ?? this.email,
      plan: plan ?? this.plan,
      dateJoined: dateJoined ?? this.dateJoined,
      datePlan: datePlan ?? this.datePlan,
      maxCredits: maxCredits ?? this.maxCredits,
      currentCredits: currentCredits ?? this.currentCredits,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Create a new trial user profile
  factory UserProfile.createTrial(String email) {
    final now = DateTime.now();
    return UserProfile(
      email: email,
      plan: 'Trial',
      dateJoined: now,
      datePlan: now,
      maxCredits: 20,
      currentCredits: 20,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProfile &&
        other.email == email &&
        other.plan == plan &&
        other.dateJoined == dateJoined &&
        other.datePlan == datePlan &&
        other.maxCredits == maxCredits &&
        other.currentCredits == currentCredits;
  }

  @override
  int get hashCode {
    return Object.hash(
      email,
      plan,
      dateJoined,
      datePlan,
      maxCredits,
      currentCredits,
    );
  }

  @override
  String toString() {
    return 'UserProfile(email: $email, plan: $plan, credits: $currentCredits/$maxCredits)';
  }
}