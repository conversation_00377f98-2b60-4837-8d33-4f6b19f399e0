"""
WebSocket connection manager for handling real-time communication.
"""

import logging
import json
import traceback
from typing import Dict, List, Any, Optional
import asyncio
import time

from fastapi import WebSocket, WebSocketDisconnect, status

from models.data_models import WebSocketMessage
from config.logging_config import get_logger

logger = get_logger(__name__)

class WebSocketError(Exception):
    """Base exception for WebSocket errors."""
    pass

class ConnectionClosedError(WebSocketError):
    """Exception raised when a WebSocket connection is closed unexpectedly."""
    pass

class MessageDeliveryError(WebSocketError):
    """Exception raised when a message cannot be delivered."""
    pass

class WebSocketManager:
    """
    Manages WebSocket connections and message broadcasting.
    Handles connection lifecycle, state tracking, and error handling.
    """
    
    def __init__(self):
        """Initialize the WebSocket manager with an empty connections dictionary."""
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_times: Dict[str, float] = {}
        self.message_counts: Dict[str, int] = {}
        logger.info("WebSocket manager initialized")
    
    async def connect(self, websocket: WebSocket, client_id: str) -> None:
        """
        Accept a WebSocket connection and store it.
        
        Args:
            websocket: The WebSocket connection to accept
            client_id: A unique identifier for the client
        """
        try:
            await websocket.accept()
            self.active_connections[client_id] = websocket
            self.connection_times[client_id] = time.time()
            self.message_counts[client_id] = 0
            logger.info(f"WebSocket connection established: {client_id}")
        except Exception as e:
            logger.error(f"Error accepting WebSocket connection for {client_id}: {str(e)}", exc_info=True)
            raise ConnectionClosedError(f"Failed to accept connection: {str(e)}") from e
    
    def disconnect(self, client_id: str) -> None:
        """
        Remove a WebSocket connection.
        
        Args:
            client_id: The client ID to disconnect
        """
        if client_id in self.active_connections:
            # Calculate connection duration
            if client_id in self.connection_times:
                duration = time.time() - self.connection_times[client_id]
                message_count = self.message_counts.get(client_id, 0)
                logger.info(f"WebSocket connection removed: {client_id} (duration: {duration:.2f}s, messages: {message_count})")
                
                # Clean up tracking data
                del self.connection_times[client_id]
                if client_id in self.message_counts:
                    del self.message_counts[client_id]
            else:
                logger.info(f"WebSocket connection removed: {client_id}")
                
            # Remove the connection
            del self.active_connections[client_id]
    
    async def send_message(self, client_id: str, message: WebSocketMessage) -> bool:
        """
        Send a message to a specific client.
        
        Args:
            client_id: The client ID to send the message to
            message: The message to send
            
        Returns:
            bool: True if the message was sent, False otherwise
        """
        if client_id in self.active_connections:
            try:
                start_time = time.time()
                await self.active_connections[client_id].send_json(message.to_dict())
                
                # Update message count
                if client_id in self.message_counts:
                    self.message_counts[client_id] += 1
                
                # Log message delivery time for performance monitoring
                duration = time.time() - start_time
                if duration > 0.1:  # Only log slow messages
                    logger.debug(f"Slow message delivery to {client_id}: {duration:.3f}s (type: {message.message_type})")
                
                return True
            except Exception as e:
                logger.error(f"Error sending message to client {client_id}: {str(e)}")
                logger.debug(f"Message that failed: {message.message_type}")
                
                # Clean up the connection
                self.disconnect(client_id)
                return False
        else:
            logger.warning(f"Attempted to send message to non-existent client: {client_id}")
            return False
    
    async def send_text(self, client_id: str, message: str) -> bool:
        """
        Send a text message to a specific client.
        
        Args:
            client_id: The client ID to send the message to
            message: The text message to send
            
        Returns:
            bool: True if the message was sent, False otherwise
        """
        if client_id in self.active_connections:
            try:
                start_time = time.time()
                await self.active_connections[client_id].send_text(message)
                
                # Update message count
                if client_id in self.message_counts:
                    self.message_counts[client_id] += 1
                
                # Log message delivery time for performance monitoring
                duration = time.time() - start_time
                if duration > 0.1:  # Only log slow messages
                    logger.debug(f"Slow text delivery to {client_id}: {duration:.3f}s")
                
                return True
            except Exception as e:
                logger.error(f"Error sending text to client {client_id}: {str(e)}")
                
                # Clean up the connection
                self.disconnect(client_id)
                return False
        else:
            logger.warning(f"Attempted to send text to non-existent client: {client_id}")
            return False
    
    async def broadcast(self, message: WebSocketMessage) -> None:
        """
        Broadcast a message to all connected clients.
        
        Args:
            message: The message to broadcast
        """
        start_time = time.time()
        disconnected_clients = []
        success_count = 0
        
        for client_id, connection in self.active_connections.items():
            try:
                await connection.send_json(message.to_dict())
                success_count += 1
                
                # Update message count
                if client_id in self.message_counts:
                    self.message_counts[client_id] += 1
                    
            except Exception as e:
                logger.error(f"Error broadcasting to client {client_id}: {str(e)}")
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
        
        # Log broadcast performance
        duration = time.time() - start_time
        total_clients = len(self.active_connections) + len(disconnected_clients)
        logger.debug(f"Broadcast completed in {duration:.3f}s: {success_count}/{total_clients} successful")
    
    async def send_error(self, client_id: str, error_message: str, error_code: Optional[str] = None) -> bool:
        """
        Send an error message to a specific client.
        
        Args:
            client_id: The client ID to send the error to
            error_message: The error message
            error_code: Optional error code
            
        Returns:
            bool: True if the error was sent, False otherwise
        """
        error_data = {
            "message": error_message
        }
        
        if error_code:
            error_data["code"] = error_code
            
        message = WebSocketMessage(
            message_type="error",
            data=error_data
        )
        
        # Log the error being sent
        logger.warning(f"Sending error to client {client_id}: {error_code or 'UNKNOWN'} - {error_message}")
        
        return await self.send_message(client_id, message)
    
    def is_connected(self, client_id: str) -> bool:
        """
        Check if a client is connected.
        
        Args:
            client_id: The client ID to check
            
        Returns:
            bool: True if the client is connected, False otherwise
        """
        return client_id in self.active_connections
    
    def get_connection_count(self) -> int:
        """
        Get the number of active connections.
        
        Returns:
            int: The number of active connections
        """
        return len(self.active_connections)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the active connections.
        
        Returns:
            Dict[str, Any]: Connection statistics
        """
        current_time = time.time()
        
        # Calculate connection durations
        durations = []
        for client_id, connect_time in self.connection_times.items():
            if client_id in self.active_connections:
                durations.append(current_time - connect_time)
        
        # Calculate message counts
        message_counts = list(self.message_counts.values())
        
        return {
            "active_connections": len(self.active_connections),
            "avg_duration": sum(durations) / len(durations) if durations else 0,
            "max_duration": max(durations) if durations else 0,
            "avg_messages": sum(message_counts) / len(message_counts) if message_counts else 0,
            "total_messages": sum(message_counts) if message_counts else 0
        }