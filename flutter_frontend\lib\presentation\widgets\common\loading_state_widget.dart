import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/gradients.dart';
import '../../../core/constants/text_styles.dart';

/// Enhanced loading state widget with progress indicators and user feedback
class LoadingStateWidget extends StatefulWidget {
  final String message;
  final String? subMessage;
  final bool showProgress;
  final double? progress; // 0.0 to 1.0, null for indeterminate
  final IconData? icon;
  final Color? color;
  final VoidCallback? onCancel;
  final bool showCancelButton;
  final Duration animationDuration;
  
  const LoadingStateWidget({
    super.key,
    required this.message,
    this.subMessage,
    this.showProgress = true,
    this.progress,
    this.icon,
    this.color,
    this.onCancel,
    this.showCancelButton = false,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  State<LoadingStateWidget> createState() => _LoadingStateWidgetState();
}

class _LoadingStateWidgetState extends State<LoadingStateWidget>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));
    
    _fadeController.forward();
    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildLoadingIndicator(),
                const SizedBox(height: 24),
                _buildMessage(),
                if (widget.subMessage != null) ...[
                  const SizedBox(height: 8),
                  _buildSubMessage(),
                ],
                if (widget.showProgress) ...[
                  const SizedBox(height: 20),
                  _buildProgressIndicator(),
                ],
                if (widget.showCancelButton) ...[
                  const SizedBox(height: 24),
                  _buildCancelButton(),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingIndicator() {
    final color = widget.color ?? AppColors.primaryBlue;
    
    if (widget.icon != null) {
      return AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(50),
                border: Border.all(
                  color: color.withOpacity(0.3),
                  width: 2,
                ),
              ),
              child: Icon(
                widget.icon!,
                size: 32,
                color: color,
              ),
            ),
          );
        },
      );
    } else {
      return AnimatedBuilder(
        animation: _rotationAnimation,
        builder: (context, child) {
          return Transform.rotate(
            angle: _rotationAnimation.value * 2 * 3.14159,
            child: Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    color,
                    color.withOpacity(0.3),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(32),
              ),
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                backgroundColor: Colors.white.withOpacity(0.3),
              ),
            ),
          );
        },
      );
    }
  }

  Widget _buildMessage() {
    return Text(
      widget.message,
      style: AppTextStyles.heading3.copyWith(
        color: AppColors.lightText,
        fontWeight: FontWeight.w600,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildSubMessage() {
    return Text(
      widget.subMessage!,
      style: AppTextStyles.bodyText2.copyWith(
        color: AppColors.lightText.withOpacity(0.7),
        height: 1.4,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildProgressIndicator() {
    final color = widget.color ?? AppColors.primaryBlue;
    
    if (widget.progress != null) {
      // Determinate progress
      return Column(
        children: [
          LinearProgressIndicator(
            value: widget.progress,
            backgroundColor: AppColors.slate600.withOpacity(0.3),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 4,
          ),
          const SizedBox(height: 8),
          Text(
            '${(widget.progress! * 100).round()}%',
            style: AppTextStyles.caption.copyWith(
              color: AppColors.lightText.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    } else {
      // Indeterminate progress
      return LinearProgressIndicator(
        backgroundColor: AppColors.slate600.withOpacity(0.3),
        valueColor: AlwaysStoppedAnimation<Color>(color),
        minHeight: 4,
      );
    }
  }

  Widget _buildCancelButton() {
    return TextButton(
      onPressed: widget.onCancel,
      style: TextButton.styleFrom(
        foregroundColor: AppColors.lightText.withOpacity(0.7),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
      child: Text(
        'Cancel',
        style: AppTextStyles.button.copyWith(
          color: AppColors.lightText.withOpacity(0.7),
        ),
      ),
    );
  }

  /// Show loading overlay
  static OverlayEntry showOverlay({
    required BuildContext context,
    required String message,
    String? subMessage,
    bool showProgress = true,
    double? progress,
    IconData? icon,
    Color? color,
    VoidCallback? onCancel,
    bool showCancelButton = false,
    bool barrierDismissible = false,
  }) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;
    
    overlayEntry = OverlayEntry(
      builder: (context) => Material(
        color: Colors.black.withOpacity(0.7),
        child: GestureDetector(
          onTap: barrierDismissible ? () => overlayEntry.remove() : null,
          child: Container(
            width: double.infinity,
            height: double.infinity,
            child: Center(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 32),
                decoration: BoxDecoration(
                  color: AppColors.slate800,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: AppColors.slate600.withOpacity(0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: LoadingStateWidget(
                  message: message,
                  subMessage: subMessage,
                  showProgress: showProgress,
                  progress: progress,
                  icon: icon,
                  color: color,
                  onCancel: onCancel != null 
                      ? () {
                          onCancel();
                          overlayEntry.remove();
                        }
                      : null,
                  showCancelButton: showCancelButton,
                ),
              ),
            ),
          ),
        ),
      ),
    );
    
    overlay.insert(overlayEntry);
    return overlayEntry;
  }

  /// Show loading dialog
  static Future<T?> showDialog<T>({
    required BuildContext context,
    required String message,
    String? subMessage,
    bool showProgress = true,
    double? progress,
    IconData? icon,
    Color? color,
    VoidCallback? onCancel,
    bool showCancelButton = false,
    bool barrierDismissible = false,
  }) {
    return showGeneralDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: Colors.black.withOpacity(0.7),
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return Center(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 32),
            decoration: BoxDecoration(
              color: AppColors.slate800,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.slate600.withOpacity(0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: LoadingStateWidget(
              message: message,
              subMessage: subMessage,
              showProgress: showProgress,
              progress: progress,
              icon: icon,
              color: color,
              onCancel: onCancel != null 
                  ? () {
                      onCancel();
                      Navigator.of(context).pop();
                    }
                  : null,
              showCancelButton: showCancelButton,
            ),
          ),
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: Tween<double>(begin: 0.8, end: 1.0).animate(
              CurvedAnimation(parent: animation, curve: Curves.elasticOut),
            ),
            child: child,
          ),
        );
      },
    );
  }
}
