"""
Google Cloud Text-to-Speech Client Service
Implements Google Cloud TTS API integration according to:
- https://cloud.google.com/text-to-speech/docs
- https://cloud.google.com/text-to-speech/docs/reference/rest
- https://github.com/googleapis/python-texttospeech

This service provides enterprise-grade TTS functionality with proper error handling,
authentication, and voice configuration for mobile playback.
"""

import logging
import time
from typing import Optional, Dict, Any
from google.cloud import texttospeech
from google.api_core import exceptions as gcp_exceptions
from config.settings import settings

logger = logging.getLogger(__name__)

class GoogleTTSClient:
    """
    Google Cloud Text-to-Speech client with enterprise-grade configuration.
    
    Features:
    - German voice (de-DE-Chirp3-HD-Aoede) optimized for mobile
    - MP3 audio format at 24kHz for optimal mobile playback
    - Configurable speech speed (0.9 for natural delivery)
    - Comprehensive error handling and logging
    - Authentication via Google Cloud credentials
    """

    def __init__(self):
        """Initialize Google TTS client with production-ready configuration."""
        self.client: Optional[texttospeech.TextToSpeechClient] = None
        self.voice_config: Optional[texttospeech.VoiceSelectionParams] = None
        self.audio_config: Optional[texttospeech.AudioConfig] = None
        
        # Configuration from settings
        self.voice_name = settings.tts_voice_name
        self.speech_speed = settings.tts_speech_speed
        self.audio_format = settings.tts_audio_format
        self.sample_rate = settings.tts_sample_rate
        self.max_text_length = settings.tts_max_text_length
        
        logger.info(f"🔧 Initializing Google TTS Client with voice: {self.voice_name}")
        self._initialize_client()

    def _initialize_client(self) -> None:
        """
        Initialize Google Cloud TTS client and configure voice/audio settings.
        
        Raises:
            Exception: If client initialization fails
        """
        try:
            # Initialize Google Cloud TTS client
            # Authentication handled via GOOGLE_APPLICATION_CREDENTIALS env var
            # or Google Cloud default credentials in production
            logger.info("🚀 Creating Google Cloud TTS client...")
            self.client = texttospeech.TextToSpeechClient()
            logger.info("✅ Google Cloud TTS client created successfully")

            # Configure voice settings
            self._configure_voice_settings()
            
            # Configure audio settings
            self._configure_audio_settings()
            
            logger.info(f"✅ Google TTS Client initialized successfully")
            logger.info(f"📋 Voice: {self.voice_name}, Speed: {self.speech_speed}")
            logger.info(f"🎵 Audio: {self.audio_format}, Sample Rate: {self.sample_rate}Hz")

        except Exception as e:
            logger.error(f"❌ Failed to initialize Google TTS client: {str(e)}")
            logger.error(f"💡 Ensure GOOGLE_APPLICATION_CREDENTIALS is set or running on Google Cloud")
            raise Exception(f"Google TTS client initialization failed: {str(e)}")

    def _configure_voice_settings(self) -> None:
        """
        Configure voice selection parameters for German TTS.
        
        Uses de-DE-Chirp3-HD-Aoede voice which provides:
        - High-definition audio quality
        - Natural German pronunciation
        - Optimized for conversational content
        """
        try:
            self.voice_config = texttospeech.VoiceSelectionParams(
                language_code="de-DE",
                name=self.voice_name,
                ssml_gender=texttospeech.SsmlVoiceGender.FEMALE
            )
            logger.info(f"✅ Voice configuration set: {self.voice_name} (de-DE, Female)")
            
        except Exception as e:
            logger.error(f"❌ Failed to configure voice settings: {str(e)}")
            raise

    def _configure_audio_settings(self) -> None:
        """
        Configure audio output settings optimized for mobile playback.
        
        Settings:
        - MP3 format for broad mobile compatibility
        - 24kHz sample rate for high quality with reasonable file size
        - Configurable speech speed (default 0.9 for natural delivery)
        """
        try:
            # Map string format to Google Cloud enum
            audio_encoding_map = {
                "MP3": texttospeech.AudioEncoding.MP3,
                "LINEAR16": texttospeech.AudioEncoding.LINEAR16,
                "OGG_OPUS": texttospeech.AudioEncoding.OGG_OPUS,
                "MULAW": texttospeech.AudioEncoding.MULAW,
                "ALAW": texttospeech.AudioEncoding.ALAW
            }
            
            audio_encoding = audio_encoding_map.get(self.audio_format.upper())
            if not audio_encoding:
                raise ValueError(f"Unsupported audio format: {self.audio_format}")

            self.audio_config = texttospeech.AudioConfig(
                audio_encoding=audio_encoding,
                sample_rate_hertz=self.sample_rate,
                speaking_rate=self.speech_speed
            )
            
            logger.info(f"✅ Audio configuration set: {self.audio_format}, {self.sample_rate}Hz, speed={self.speech_speed}")
            
        except Exception as e:
            logger.error(f"❌ Failed to configure audio settings: {str(e)}")
            raise

    async def generate_speech(self, text: str) -> Dict[str, Any]:
        """
        Generate speech audio from text using Google Cloud TTS.
        
        Args:
            text (str): Text to convert to speech (max length from settings)
            
        Returns:
            Dict[str, Any]: Dictionary containing:
                - audio_data (bytes): Generated audio data
                - content_type (str): MIME type for audio
                - duration_seconds (float): Estimated audio duration
                - processing_time (float): Time taken to generate audio
                
        Raises:
            ValueError: If text is invalid or too long
            Exception: If TTS generation fails
        """
        if not self.client:
            raise Exception("Google TTS client not initialized")

        start_time = time.time()
        
        try:
            # Validate input text
            self._validate_text_input(text)
            
            logger.info(f"🎤 Generating speech for text: '{text[:50]}...' ({len(text)} chars)")
            
            # Create synthesis input
            synthesis_input = texttospeech.SynthesisInput(text=text)
            
            # Make TTS API request
            logger.info("📡 Calling Google Cloud TTS API...")
            response = self.client.synthesize_speech(
                input=synthesis_input,
                voice=self.voice_config,
                audio_config=self.audio_config
            )
            
            processing_time = time.time() - start_time
            
            # Estimate audio duration (rough calculation)
            # Average speaking rate: ~150 words per minute
            word_count = len(text.split())
            estimated_duration = (word_count / 150) * 60 / self.speech_speed
            
            logger.info(f"✅ Speech generated successfully in {processing_time:.2f}s")
            logger.info(f"📊 Audio size: {len(response.audio_content)} bytes")
            logger.info(f"⏱️ Estimated duration: {estimated_duration:.1f}s")
            
            return {
                "audio_data": response.audio_content,
                "content_type": self._get_content_type(),
                "duration_seconds": estimated_duration,
                "processing_time": processing_time
            }
            
        except gcp_exceptions.InvalidArgument as e:
            logger.error(f"❌ Invalid argument for TTS request: {str(e)}")
            raise ValueError(f"Invalid TTS request: {str(e)}")
            
        except gcp_exceptions.PermissionDenied as e:
            logger.error(f"❌ Permission denied for TTS API: {str(e)}")
            raise Exception(f"TTS API permission denied: {str(e)}")
            
        except gcp_exceptions.ResourceExhausted as e:
            logger.error(f"❌ TTS API quota exceeded: {str(e)}")
            raise Exception(f"TTS API quota exceeded: {str(e)}")
            
        except gcp_exceptions.ServiceUnavailable as e:
            logger.error(f"❌ TTS API service unavailable: {str(e)}")
            raise Exception(f"TTS API service unavailable: {str(e)}")
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Unexpected error in TTS generation: {str(e)}")
            logger.error(f"⏱️ Failed after {processing_time:.2f}s")
            raise Exception(f"TTS generation failed: {str(e)}")

    def _validate_text_input(self, text: str) -> None:
        """
        Validate text input for TTS generation.
        
        Args:
            text (str): Text to validate
            
        Raises:
            ValueError: If text is invalid
        """
        if not text or not text.strip():
            raise ValueError("Text cannot be empty")
            
        if len(text) > self.max_text_length:
            raise ValueError(f"Text too long: {len(text)} chars (max: {self.max_text_length})")
            
        # Basic content sanitization
        if any(char in text for char in ['<', '>', '&']):
            logger.warning("⚠️ Text contains HTML-like characters, consider sanitization")

    def _get_content_type(self) -> str:
        """
        Get MIME content type for configured audio format.
        
        Returns:
            str: MIME content type
        """
        content_type_map = {
            "MP3": "audio/mpeg",
            "LINEAR16": "audio/wav",
            "OGG_OPUS": "audio/ogg",
            "MULAW": "audio/basic",
            "ALAW": "audio/basic"
        }
        
        return content_type_map.get(self.audio_format.upper(), "audio/mpeg")

    def get_client_info(self) -> Dict[str, Any]:
        """
        Get information about the TTS client configuration.
        
        Returns:
            Dict[str, Any]: Client configuration information
        """
        return {
            "voice_name": self.voice_name,
            "language_code": "de-DE",
            "speech_speed": self.speech_speed,
            "audio_format": self.audio_format,
            "sample_rate": self.sample_rate,
            "max_text_length": self.max_text_length,
            "client_initialized": self.client is not None
        }

# Create a global TTS client instance
google_tts_client = GoogleTTSClient()