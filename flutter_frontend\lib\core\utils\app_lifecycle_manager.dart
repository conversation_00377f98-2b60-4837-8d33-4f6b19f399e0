import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import '../../data/models/app_error.dart';
import '../../data/services/audio_service.dart';

/// Manager for handling app lifecycle events during audio recording sessions
class AppLifecycleManager {
  static AppLifecycleManager? _instance;
  
  /// Singleton instance
  static AppLifecycleManager get instance {
    _instance ??= AppLifecycleManager._();
    return _instance!;
  }
  
  final AudioService _audioService = AudioService.instance;
  
  StreamController<AppLifecycleState>? _lifecycleController;
  StreamController<AppError>? _errorController;
  
  AppLifecycleState _currentState = AppLifecycleState.resumed;
  bool _wasRecordingBeforeBackground = false;
  bool _isInitialized = false;
  bool _isDisposed = false;
  Timer? _backgroundTimer;
  
  AppLifecycleManager._();
  
  /// Stream of lifecycle state changes
  Stream<AppLifecycleState> get lifecycleStream => 
      _lifecycleController?.stream ?? const Stream.empty();
  
  /// Stream of lifecycle-related errors
  Stream<AppError> get errorStream => 
      _errorController?.stream ?? const Stream.empty();
  
  /// Get current app lifecycle state
  AppLifecycleState get currentState => _currentState;
  
  /// Check if app is in background
  bool get isInBackground => 
      _currentState == AppLifecycleState.paused ||
      _currentState == AppLifecycleState.inactive;
  
  /// Check if app is in foreground
  bool get isInForeground => _currentState == AppLifecycleState.resumed;
  
  /// Initialize the lifecycle manager
  Future<void> initialize() async {
    if (_isDisposed || _isInitialized) return;
    
    _lifecycleController = StreamController<AppLifecycleState>.broadcast();
    _errorController = StreamController<AppError>.broadcast();
    
    _isInitialized = true;
    print('AppLifecycleManager initialized');
  }
  
  /// Handle app lifecycle state changes
  Future<void> handleLifecycleChange(AppLifecycleState state) async {
    if (_isDisposed) return;
    
    final previousState = _currentState;
    _currentState = state;
    
    print('App lifecycle changed: $previousState -> $state');
    
    try {
      switch (state) {
        case AppLifecycleState.paused:
          await _handleAppPaused();
          break;
        case AppLifecycleState.inactive:
          await _handleAppInactive();
          break;
        case AppLifecycleState.resumed:
          await _handleAppResumed(previousState);
          break;
        case AppLifecycleState.detached:
          await _handleAppDetached();
          break;
        case AppLifecycleState.hidden:
          await _handleAppHidden();
          break;
      }
      
      _lifecycleController?.add(state);
      
    } catch (e) {
      final error = AppError.unknown(
        details: 'Error handling lifecycle change: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
    }
  }
  
  /// Handle app paused state (background)
  Future<void> _handleAppPaused() async {
    print('App paused - handling audio recording');
    
    // Check if currently recording
    if (_audioService.isRecording) {
      _wasRecordingBeforeBackground = true;
      
      if (Platform.isAndroid) {
        // On Android, pause recording to avoid audio focus issues
        await _pauseRecordingForBackground();
      } else if (Platform.isIOS) {
        // On iOS, continue recording but prepare for potential interruption
        await _prepareForBackgroundRecording();
      }
    }
    
    // Start background timer for cleanup
    _startBackgroundTimer();
  }
  
  /// Handle app inactive state (transitioning)
  Future<void> _handleAppInactive() async {
    print('App inactive - preparing for potential background');
    
    // Prepare for potential background state
    if (_audioService.isRecording) {
      await _prepareForBackgroundRecording();
    }
  }
  
  /// Handle app resumed state (foreground)
  Future<void> _handleAppResumed(AppLifecycleState previousState) async {
    print('App resumed from $previousState');
    
    // Cancel background timer
    _backgroundTimer?.cancel();
    
    // Handle audio recording restoration
    if (_wasRecordingBeforeBackground) {
      await _restoreRecordingFromBackground();
      _wasRecordingBeforeBackground = false;
    }
    
    // Request audio focus on Android
    if (Platform.isAndroid) {
      await _requestAudioFocus();
    }
  }
  
  /// Handle app detached state (terminating)
  Future<void> _handleAppDetached() async {
    print('App detached - cleaning up resources');
    
    // Stop any ongoing recording
    if (_audioService.isRecording) {
      await _audioService.stopRecording();
    }
    
    // Clean up resources
    await _cleanupResources();
  }
  
  /// Handle app hidden state (iOS specific)
  Future<void> _handleAppHidden() async {
    print('App hidden - handling iOS background state');
    
    if (_audioService.isRecording) {
      _wasRecordingBeforeBackground = true;
      await _prepareForBackgroundRecording();
    }
  }
  
  /// Pause recording for background state
  Future<void> _pauseRecordingForBackground() async {
    try {
      await _audioService.pauseRecording();
      print('Recording paused for background');
    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Failed to pause recording for background: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
    }
  }
  
  /// Prepare for background recording (iOS)
  Future<void> _prepareForBackgroundRecording() async {
    try {
      // On iOS, we can continue recording in background for a limited time
      // Configure audio session for background recording
      if (Platform.isIOS) {
        await _configureIOSBackgroundAudio();
      }
      
      print('Prepared for background recording');
    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Failed to prepare for background recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
    }
  }
  
  /// Restore recording from background
  Future<void> _restoreRecordingFromBackground() async {
    try {
      if (Platform.isAndroid) {
        // Resume paused recording
        await _audioService.resumeRecording();
      } else if (Platform.isIOS) {
        // Check if recording is still active, restart if needed
        if (!_audioService.isRecording) {
          await _audioService.startRecording();
        }
      }
      
      print('Recording restored from background');
    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Failed to restore recording from background: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
    }
  }
  
  /// Configure iOS background audio session
  Future<void> _configureIOSBackgroundAudio() async {
    if (!Platform.isIOS) return;
    
    try {
      // Use method channel to configure AVAudioSession for background recording
      const platform = MethodChannel('deutschkorrekt/audio');
      await platform.invokeMethod('configureBackgroundAudio');
    } catch (e) {
      print('Failed to configure iOS background audio: $e');
    }
  }
  
  /// Request audio focus on Android
  Future<void> _requestAudioFocus() async {
    if (!Platform.isAndroid) return;
    
    try {
      // Use method channel to request audio focus
      const platform = MethodChannel('deutschkorrekt/audio');
      await platform.invokeMethod('requestAudioFocus');
    } catch (e) {
      print('Failed to request audio focus: $e');
    }
  }
  
  /// Start background timer for cleanup
  void _startBackgroundTimer() {
    _backgroundTimer?.cancel();
    
    // Clean up after 30 seconds in background
    _backgroundTimer = Timer(const Duration(seconds: 30), () async {
      if (isInBackground && _audioService.isRecording) {
        print('Background timer expired - stopping recording');
        
        try {
          await _audioService.stopRecording();
          _wasRecordingBeforeBackground = false;
          
          final error = AppError.audioRecordingFailed(
            details: 'Recording stopped due to extended background time',
          );
          _errorController?.add(error);
        } catch (e) {
          print('Error stopping recording in background: $e');
        }
      }
    });
  }
  
  /// Clean up resources
  Future<void> _cleanupResources() async {
    try {
      _backgroundTimer?.cancel();
      
      // Release audio focus on Android
      if (Platform.isAndroid) {
        await _releaseAudioFocus();
      }
      
      // Clean up iOS audio session
      if (Platform.isIOS) {
        await _cleanupIOSAudioSession();
      }
      
      print('Resources cleaned up');
    } catch (e) {
      print('Error cleaning up resources: $e');
    }
  }
  
  /// Release audio focus on Android
  Future<void> _releaseAudioFocus() async {
    if (!Platform.isAndroid) return;
    
    try {
      const platform = MethodChannel('deutschkorrekt/audio');
      await platform.invokeMethod('releaseAudioFocus');
    } catch (e) {
      print('Failed to release audio focus: $e');
    }
  }
  
  /// Clean up iOS audio session
  Future<void> _cleanupIOSAudioSession() async {
    if (!Platform.isIOS) return;
    
    try {
      const platform = MethodChannel('deutschkorrekt/audio');
      await platform.invokeMethod('cleanupAudioSession');
    } catch (e) {
      print('Failed to cleanup iOS audio session: $e');
    }
  }
  
  /// Handle audio interruption (iOS specific)
  Future<void> handleAudioInterruption(bool interrupted) async {
    if (_isDisposed) return;
    
    print('Audio interruption: $interrupted');
    
    try {
      if (interrupted) {
        // Audio was interrupted (phone call, etc.)
        if (_audioService.isRecording) {
          _wasRecordingBeforeBackground = true;
          await _audioService.pauseRecording();
        }
      } else {
        // Audio interruption ended
        if (_wasRecordingBeforeBackground) {
          await _audioService.resumeRecording();
          _wasRecordingBeforeBackground = false;
        }
      }
    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Error handling audio interruption: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
    }
  }
  
  /// Handle audio focus change (Android specific)
  Future<void> handleAudioFocusChange(String focusChange) async {
    if (_isDisposed) return;
    
    print('Audio focus change: $focusChange');
    
    try {
      switch (focusChange) {
        case 'AUDIOFOCUS_LOSS':
        case 'AUDIOFOCUS_LOSS_TRANSIENT':
          // Lost audio focus, pause recording
          if (_audioService.isRecording) {
            _wasRecordingBeforeBackground = true;
            await _audioService.pauseRecording();
          }
          break;
        case 'AUDIOFOCUS_GAIN':
          // Regained audio focus, resume recording
          if (_wasRecordingBeforeBackground) {
            await _audioService.resumeRecording();
            _wasRecordingBeforeBackground = false;
          }
          break;
        case 'AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK':
          // Can continue recording at lower volume
          // For now, we'll pause to be safe
          if (_audioService.isRecording) {
            await _audioService.pauseRecording();
          }
          break;
      }
    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Error handling audio focus change: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
    }
  }
  
  /// Get lifecycle manager statistics
  Map<String, dynamic> getStats() {
    return {
      'currentState': _currentState.toString(),
      'isInBackground': isInBackground,
      'isInForeground': isInForeground,
      'wasRecordingBeforeBackground': _wasRecordingBeforeBackground,
      'isInitialized': _isInitialized,
      'hasBackgroundTimer': _backgroundTimer?.isActive ?? false,
    };
  }
  
  /// Dispose the lifecycle manager
  Future<void> dispose() async {
    if (_isDisposed) return;
    
    _isDisposed = true;
    
    _backgroundTimer?.cancel();
    await _cleanupResources();
    
    await _lifecycleController?.close();
    await _errorController?.close();
    
    _lifecycleController = null;
    _errorController = null;
    
    print('AppLifecycleManager disposed');
  }
}