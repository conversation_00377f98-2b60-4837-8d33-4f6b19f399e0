import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/core/utils/sentence_extractor.dart';

void main() {
  group('SentenceExtractor', () {
    group('extractFirstSentenceFromBraces', () {
      test('should extract first sentence from simple braces', () {
        const text = 'Here is the answer: {Hello world! This is a test.}';
        final result = SentenceExtractor.extractFirstSentenceFromBraces(text);
        expect(result, equals('Hello world! This is a test.'));
      });

      test('should handle text without braces', () {
        const text = 'This text has no braces';
        final result = SentenceExtractor.extractFirstSentenceFromBraces(text);
        expect(result, isNull);
      });

      test('should handle empty text', () {
        const text = '';
        final result = SentenceExtractor.extractFirstSentenceFromBraces(text);
        expect(result, isNull);
      });

      test('should handle nested braces correctly', () {
        const text = 'Answer: {This is {nested} content! More text.}';
        final result = SentenceExtractor.extractFirstSentenceFromBraces(text);
        expect(result, equals('This is {nested} content! More text.'));
      });

      test('should handle German text with special characters', () {
        const text = 'Antwort: {Äpfel sind schön! Öl ist gut.}';
        final result = SentenceExtractor.extractFirstSentenceFromBraces(text);
        expect(result, equals('Äpfel sind schön! Öl ist gut.'));
      });

      test('should handle text without sentence endings', () {
        const text = 'Answer: {This has no punctuation}';
        final result = SentenceExtractor.extractFirstSentenceFromBraces(text);
        expect(result, equals('This has no punctuation'));
      });

      test('should handle empty braces', () {
        const text = 'Answer: {}';
        final result = SentenceExtractor.extractFirstSentenceFromBraces(text);
        expect(result, isNull);
      });

      test('should handle whitespace-only braces', () {
        const text = 'Answer: {   \n\t   }';
        final result = SentenceExtractor.extractFirstSentenceFromBraces(text);
        expect(result, isNull);
      });

      test('should handle unmatched opening brace', () {
        const text = 'Answer: {This has no closing brace';
        final result = SentenceExtractor.extractFirstSentenceFromBraces(text);
        expect(result, isNull);
      });

      test('should handle multiple brace pairs and extract from first', () {
        const text = 'First: {Hello world!} Second: {Goodbye world!}';
        final result = SentenceExtractor.extractFirstSentenceFromBraces(text);
        expect(result, equals('Hello world!'));
      });
    });

    group('isValidForTTS', () {
      test('should validate normal sentences', () {
        expect(SentenceExtractor.isValidForTTS('Hello world!'), isTrue);
        expect(SentenceExtractor.isValidForTTS('Guten Tag!'), isTrue);
      });

      test('should reject null or empty sentences', () {
        expect(SentenceExtractor.isValidForTTS(null), isFalse);
        expect(SentenceExtractor.isValidForTTS(''), isFalse);
        expect(SentenceExtractor.isValidForTTS('   '), isFalse);
      });

      test('should reject sentences that are too short', () {
        expect(SentenceExtractor.isValidForTTS('A'), isFalse);
        expect(SentenceExtractor.isValidForTTS('!'), isFalse);
      });

      test('should reject sentences without letters', () {
        expect(SentenceExtractor.isValidForTTS('123!'), isFalse);
        expect(SentenceExtractor.isValidForTTS('!!!'), isFalse);
      });

      test('should reject sentences that are too long', () {
        final longText = 'A' * 501;
        expect(SentenceExtractor.isValidForTTS(longText), isFalse);
      });

      test('should accept sentences with German characters', () {
        expect(SentenceExtractor.isValidForTTS('Äpfel sind schön!'), isTrue);
        expect(SentenceExtractor.isValidForTTS('Größe ist wichtig.'), isTrue);
      });

      test('should accept sentences at the length limit', () {
        final maxLengthText = 'A' * 500;
        expect(SentenceExtractor.isValidForTTS(maxLengthText), isTrue);
      });
    });

    group('formatForDisplay', () {
      test('should format text with braces for display', () {
        const original = 'Here is the answer: {Hello world! This is a test.}';
        final result = SentenceExtractor.formatForDisplay(original);
        expect(result, equals('Here is the answer: Hello world! This is a test.'));
      });

      test('should return original text when no braces found', () {
        const original = 'This text has no braces';
        final result = SentenceExtractor.formatForDisplay(original);
        expect(result, equals(original));
      });

      test('should handle empty text', () {
        const original = '';
        final result = SentenceExtractor.formatForDisplay(original);
        expect(result, equals(''));
      });

      test('should handle multiple brace pairs', () {
        const original = 'First: {Hello!} Second: {World!}';
        final result = SentenceExtractor.formatForDisplay(original);
        expect(result, equals('First: Hello! Second: World!'));
      });

      test('should handle nested braces in display formatting', () {
        const original = 'Answer: {This is {nested} content! More text.}';
        final result = SentenceExtractor.formatForDisplay(original);
        expect(result, equals('Answer: This is {nested} content! More text.'));
      });

      test('should handle German text in display formatting', () {
        const original = 'Antwort: {Äpfel sind schön! Öl ist gut.}';
        final result = SentenceExtractor.formatForDisplay(original);
        expect(result, equals('Antwort: Äpfel sind schön! Öl ist gut.'));
      });

      test('should handle braces with no extractable content', () {
        const original = 'Answer: {}';
        final result = SentenceExtractor.formatForDisplay(original);
        expect(result, equals('Answer:')); // Empty braces are removed
      });

      test('should preserve text structure while cleaning braces', () {
        const original = 'Before text {Clean this up!} After text';
        final result = SentenceExtractor.formatForDisplay(original);
        expect(result, equals('Before text Clean this up! After text'));
      });
    });

    group('curlyBraceRegex', () {
      test('should provide access to the regex pattern', () {
        final regex = SentenceExtractor.curlyBraceRegex;
        expect(regex, isA<RegExp>());
        
        // Test that the regex works as expected
        const text = 'Test {content} here';
        final matches = regex.allMatches(text);
        expect(matches.length, equals(1));
        expect(matches.first.group(1), equals('content'));
      });

      test('should handle nested braces in regex', () {
        final regex = SentenceExtractor.curlyBraceRegex;
        const text = 'Test {outer {inner} content} here';
        final matches = regex.allMatches(text);
        expect(matches.length, equals(1));
        expect(matches.first.group(1), equals('outer {inner} content'));
      });
    });
  });
}