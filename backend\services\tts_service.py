"""
Text-to-Speech Service
High-level TTS service that provides text validation, content sanitization,
and interfaces with Google Cloud TTS client for audio generation.

This service acts as the main interface for TTS functionality in the application,
providing enterprise-grade error handling, input validation, and logging.
"""

import logging
import re
import time
from typing import Dict, Any, Optional
from .google_tts_client import google_tts_client
from config.settings import settings

logger = logging.getLogger(__name__)

class TTSService:
    """
    High-level Text-to-Speech service with comprehensive validation and error handling.
    
    Features:
    - Text validation and sanitization
    - Length limits and content filtering
    - Integration with Google Cloud TTS
    - Comprehensive error handling
    - Performance monitoring and logging
    """

    def __init__(self):
        """Initialize TTS service with configuration from settings."""
        self.max_text_length = settings.tts_max_text_length
        self.google_tts = google_tts_client
        
        logger.info(f"🔧 TTS Service initialized with max length: {self.max_text_length}")

    async def generate_speech(self, text: str, request_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate speech audio from text with comprehensive validation and error handling.
        
        Args:
            text (str): Text to convert to speech
            request_id (str, optional): Request ID for logging and tracking
            
        Returns:
            Dict[str, Any]: Dictionary containing:
                - success (bool): Whether generation was successful
                - audio_data (bytes): Generated audio data (if successful)
                - content_type (str): MIME type for audio (if successful)
                - duration_seconds (float): Estimated audio duration (if successful)
                - processing_time (float): Time taken to generate audio
                - error_message (str): Error description (if failed)
                - error_code (str): Error code for client handling (if failed)
                
        Raises:
            Exception: For unexpected system errors
        """
        start_time = time.time()
        request_log = f"[{request_id}]" if request_id else ""
        
        try:
            logger.info(f"🎤 {request_log} TTS request received: '{text[:50]}...' ({len(text)} chars)")
            
            # Step 1: Validate and sanitize input text
            validation_result = self._validate_and_sanitize_text(text)
            if not validation_result["valid"]:
                processing_time = time.time() - start_time
                logger.warning(f"⚠️ {request_log} Text validation failed: {validation_result['error']}")
                
                return {
                    "success": False,
                    "error_message": validation_result["error"],
                    "error_code": "INVALID_INPUT",
                    "processing_time": processing_time
                }
            
            sanitized_text = validation_result["sanitized_text"]
            logger.info(f"✅ {request_log} Text validation passed")
            
            # Step 2: Generate speech using Google TTS client
            logger.info(f"🚀 {request_log} Calling Google TTS for speech generation...")
            tts_result = await self.google_tts.generate_speech(sanitized_text)
            
            processing_time = time.time() - start_time
            
            logger.info(f"✅ {request_log} Speech generated successfully in {processing_time:.2f}s")
            logger.info(f"📊 {request_log} Audio size: {len(tts_result['audio_data'])} bytes")
            
            return {
                "success": True,
                "audio_data": tts_result["audio_data"],
                "content_type": tts_result["content_type"],
                "duration_seconds": tts_result["duration_seconds"],
                "processing_time": processing_time,
                "original_text": text,
                "sanitized_text": sanitized_text
            }
            
        except ValueError as e:
            # Input validation errors
            processing_time = time.time() - start_time
            logger.error(f"❌ {request_log} Input validation error: {str(e)}")
            
            return {
                "success": False,
                "error_message": str(e),
                "error_code": "INVALID_INPUT",
                "processing_time": processing_time
            }
            
        except Exception as e:
            # Handle Google TTS API errors and other system errors
            processing_time = time.time() - start_time
            error_message = str(e)
            
            # Categorize errors for better client handling
            if "quota exceeded" in error_message.lower():
                error_code = "QUOTA_EXCEEDED"
                logger.error(f"❌ {request_log} TTS quota exceeded: {error_message}")
            elif "permission denied" in error_message.lower():
                error_code = "PERMISSION_DENIED"
                logger.error(f"❌ {request_log} TTS permission denied: {error_message}")
            elif "service unavailable" in error_message.lower():
                error_code = "SERVICE_UNAVAILABLE"
                logger.error(f"❌ {request_log} TTS service unavailable: {error_message}")
            else:
                error_code = "INTERNAL_ERROR"
                logger.error(f"❌ {request_log} Unexpected TTS error: {error_message}")
            
            return {
                "success": False,
                "error_message": f"TTS generation failed: {error_message}",
                "error_code": error_code,
                "processing_time": processing_time
            }

    def _validate_and_sanitize_text(self, text: str) -> Dict[str, Any]:
        """
        Validate and sanitize input text for TTS generation.
        
        Args:
            text (str): Text to validate and sanitize
            
        Returns:
            Dict[str, Any]: Validation result with sanitized text
        """
        try:
            # Basic validation
            if not text or not isinstance(text, str):
                return {
                    "valid": False,
                    "error": "Text must be a non-empty string"
                }
            
            # Trim whitespace
            text = text.strip()
            
            if not text:
                return {
                    "valid": False,
                    "error": "Text cannot be empty or only whitespace"
                }
            
            # Length validation
            if len(text) > self.max_text_length:
                return {
                    "valid": False,
                    "error": f"Text too long: {len(text)} characters (maximum: {self.max_text_length})"
                }
            
            # Content sanitization
            sanitized_text = self._sanitize_text_content(text)
            
            # Final validation after sanitization
            if not sanitized_text.strip():
                return {
                    "valid": False,
                    "error": "Text contains no valid content after sanitization"
                }
            
            return {
                "valid": True,
                "sanitized_text": sanitized_text,
                "original_length": len(text),
                "sanitized_length": len(sanitized_text)
            }
            
        except Exception as e:
            logger.error(f"❌ Error in text validation: {str(e)}")
            return {
                "valid": False,
                "error": f"Text validation failed: {str(e)}"
            }

    def _sanitize_text_content(self, text: str) -> str:
        """
        Sanitize text content for safe TTS processing.
        
        Args:
            text (str): Text to sanitize
            
        Returns:
            str: Sanitized text safe for TTS
        """
        try:
            # Remove or escape potentially problematic characters
            sanitized = text
            
            # Remove HTML tags if present
            sanitized = re.sub(r'<[^>]+>', '', sanitized)
            
            # Remove excessive whitespace
            sanitized = re.sub(r'\s+', ' ', sanitized)
            
            # Remove control characters except newlines and tabs
            sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', sanitized)
            
            # Normalize quotes for better TTS pronunciation
            sanitized = sanitized.replace('"', '"').replace('"', '"')
            sanitized = sanitized.replace(''', "'").replace(''', "'")
            
            # Remove excessive punctuation that might confuse TTS
            sanitized = re.sub(r'[!]{2,}', '!', sanitized)
            sanitized = re.sub(r'[?]{2,}', '?', sanitized)
            sanitized = re.sub(r'[.]{3,}', '...', sanitized)
            
            # Trim and normalize spacing
            sanitized = sanitized.strip()
            
            return sanitized
            
        except Exception as e:
            logger.error(f"❌ Error in text sanitization: {str(e)}")
            # Return original text if sanitization fails
            return text

    def get_service_info(self) -> Dict[str, Any]:
        """
        Get information about the TTS service configuration and status.
        
        Returns:
            Dict[str, Any]: Service information
        """
        try:
            google_tts_info = self.google_tts.get_client_info()
            
            return {
                "service_name": "TTS Service",
                "max_text_length": self.max_text_length,
                "google_tts_client": google_tts_info,
                "features": [
                    "Text validation and sanitization",
                    "German voice (de-DE-Chirp3-HD-Aoede)",
                    "MP3 audio output optimized for mobile",
                    "Comprehensive error handling",
                    "Performance monitoring"
                ]
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting service info: {str(e)}")
            return {
                "service_name": "TTS Service",
                "status": "Error retrieving service information",
                "error": str(e)
            }

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check of the TTS service.
        
        Returns:
            Dict[str, Any]: Health check results
        """
        try:
            start_time = time.time()
            
            # Test with a simple German phrase
            test_text = "Hallo, das ist ein Test."
            
            logger.info("🏥 Performing TTS service health check...")
            result = await self.generate_speech(test_text, request_id="HEALTH_CHECK")
            
            check_time = time.time() - start_time
            
            if result["success"]:
                logger.info(f"✅ TTS service health check passed in {check_time:.2f}s")
                return {
                    "healthy": True,
                    "response_time": check_time,
                    "audio_size": len(result["audio_data"]),
                    "message": "TTS service is operational"
                }
            else:
                logger.warning(f"⚠️ TTS service health check failed: {result['error_message']}")
                return {
                    "healthy": False,
                    "response_time": check_time,
                    "error": result["error_message"],
                    "error_code": result["error_code"]
                }
                
        except Exception as e:
            check_time = time.time() - start_time
            logger.error(f"❌ TTS service health check error: {str(e)}")
            return {
                "healthy": False,
                "response_time": check_time,
                "error": f"Health check failed: {str(e)}"
            }

# Create a global TTS service instance
tts_service = TTSService()