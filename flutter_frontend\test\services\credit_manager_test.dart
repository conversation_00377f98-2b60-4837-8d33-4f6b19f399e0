import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/data/services/credit_manager.dart';

void main() {
  group('CreditManager', () {
    late CreditManager creditManager;

    setUp(() {
      creditManager = CreditManager();
    });

    group('Date calculations', () {
      test('should calculate next refresh date correctly', () {
        // Plan started on 15th of January
        final planStartDate = DateTime(2024, 1, 15);
        final nextRefresh = creditManager.calculateNextRefreshDate(planStartDate);
        
        // Should be 15th of next month
        expect(nextRefresh.day, equals(15));
        expect(nextRefresh.isAfter(DateTime.now()), isTrue);
      });

      test('should handle month-end edge cases', () {
        // Plan started on January 31st
        final planStartDate = DateTime(2024, 1, 31);
        final nextRefresh = creditManager.calculateNextRefreshDate(planStartDate);
        
        // February doesn't have 31 days, should adjust to last day of February
        expect(nextRefresh.month, equals(2));
        expect(nextRefresh.day, lessThanOrEqualTo(29)); // 2024 is a leap year
      });

      test('should handle year rollover', () {
        // Plan started on December 15th
        final planStartDate = DateTime(2023, 12, 15);
        final nextRefresh = creditManager.calculateNextRefreshDate(planStartDate);
        
        // Should roll over to next year if current date is past December 15th
        if (DateTime.now().isAfter(DateTime(DateTime.now().year, 12, 15))) {
          expect(nextRefresh.year, equals(DateTime.now().year + 1));
          expect(nextRefresh.month, equals(1));
          expect(nextRefresh.day, equals(15));
        }
      });
    });

    group('Credit refresh logic', () {
      test('should determine if credits need refresh', () {
        final now = DateTime.now();
        
        // Plan from previous month should need refresh
        final oldPlanDate = DateTime(now.year, now.month - 1, 15);
        expect(creditManager.shouldRefreshCredits(oldPlanDate), isTrue);
        
        // Plan from current month should not need refresh (unless past refresh day)
        final currentPlanDate = DateTime(now.year, now.month, now.day + 1);
        expect(creditManager.shouldRefreshCredits(currentPlanDate), isFalse);
      });

      test('should handle edge case for same day refresh', () {
        final now = DateTime.now();
        final todayPlanDate = DateTime(now.year, now.month, now.day);
        
        // Plan date on same day should trigger refresh
        expect(creditManager.shouldRefreshCredits(todayPlanDate), isTrue);
      });
    });

    group('Credit result types', () {
      test('should create success result', () {
        final mockProfile = UserProfile(
          email: '<EMAIL>',
          plan: 'Trial',
          dateJoined: DateTime.now(),
          datePlan: DateTime.now(),
          maxCredits: 20,
          currentCredits: 19,
        );

        final result = CreditResult.success(mockProfile);
        
        expect(result.success, isTrue);
        expect(result.profile, equals(mockProfile));
        expect(result.errorMessage, isNull);
        expect(result.wasRefreshed, isFalse);
        expect(result.type, equals(CreditResultType.success));
      });

      test('should create success result with refresh', () {
        final mockProfile = UserProfile(
          email: '<EMAIL>',
          plan: 'Trial',
          dateJoined: DateTime.now(),
          datePlan: DateTime.now(),
          maxCredits: 20,
          currentCredits: 20,
        );

        final result = CreditResult.success(mockProfile, wasRefreshed: true);
        
        expect(result.success, isTrue);
        expect(result.wasRefreshed, isTrue);
        expect(result.type, equals(CreditResultType.success));
      });

      test('should create insufficient credits result', () {
        const message = 'No credits available';
        final result = CreditResult.insufficientCredits(message);
        
        expect(result.success, isFalse);
        expect(result.errorMessage, equals(message));
        expect(result.profile, isNull);
        expect(result.type, equals(CreditResultType.insufficientCredits));
      });

      test('should create error result', () {
        const message = 'Database error';
        final result = CreditResult.error(message);
        
        expect(result.success, isFalse);
        expect(result.errorMessage, equals(message));
        expect(result.profile, isNull);
        expect(result.type, equals(CreditResultType.error));
      });
    });

    group('Credit check result', () {
      test('should create credit check result with all fields', () {
        final nextRefresh = DateTime.now().add(const Duration(days: 15));
        
        final result = CreditCheckResult(
          canConsume: true,
          reason: 'Credits available',
          currentCredits: 15,
          maxCredits: 20,
          nextRefreshDate: nextRefresh,
          needsRefresh: false,
        );
        
        expect(result.canConsume, isTrue);
        expect(result.reason, equals('Credits available'));
        expect(result.currentCredits, equals(15));
        expect(result.maxCredits, equals(20));
        expect(result.nextRefreshDate, equals(nextRefresh));
        expect(result.needsRefresh, isFalse);
      });
    });

    group('Credit status', () {
      test('should create credit status with all information', () {
        final nextRefresh = DateTime.now().add(const Duration(days: 10));
        
        final status = CreditStatus(
          email: '<EMAIL>',
          currentCredits: 15,
          maxCredits: 20,
          plan: 'Trial',
          nextRefreshDate: nextRefresh,
          needsRefresh: false,
          daysUntilRefresh: 10,
          creditsUsagePercentage: 0.25,
          remainingCreditsPercentage: 0.75,
          status: 'Credits available',
        );
        
        expect(status.email, equals('<EMAIL>'));
        expect(status.currentCredits, equals(15));
        expect(status.maxCredits, equals(20));
        expect(status.plan, equals('Trial'));
        expect(status.daysUntilRefresh, equals(10));
        expect(status.formattedStatus, equals('15 of 20 credits remaining'));
      });

      test('should format status message correctly', () {
        // Status with credits
        var status = CreditStatus(
          email: '<EMAIL>',
          currentCredits: 15,
          maxCredits: 20,
          plan: 'Trial',
          nextRefreshDate: DateTime.now().add(const Duration(days: 10)),
          needsRefresh: false,
          daysUntilRefresh: 10,
          creditsUsagePercentage: 0.25,
          remainingCreditsPercentage: 0.75,
          status: 'Credits available',
        );
        
        expect(status.formattedStatus, equals('15 of 20 credits remaining'));
        
        // Status needing refresh
        status = CreditStatus(
          email: '<EMAIL>',
          currentCredits: 0,
          maxCredits: 20,
          plan: 'Trial',
          nextRefreshDate: DateTime.now(),
          needsRefresh: true,
          daysUntilRefresh: 0,
          creditsUsagePercentage: 1.0,
          remainingCreditsPercentage: 0.0,
          status: 'Credits ready for refresh',
        );
        
        expect(status.formattedStatus, equals('Credits refresh today!'));
        
        // Status with no credits, refresh tomorrow
        status = CreditStatus(
          email: '<EMAIL>',
          currentCredits: 0,
          maxCredits: 20,
          plan: 'Trial',
          nextRefreshDate: DateTime.now().add(const Duration(days: 1)),
          needsRefresh: false,
          daysUntilRefresh: 1,
          creditsUsagePercentage: 1.0,
          remainingCreditsPercentage: 0.0,
          status: 'No credits available',
        );
        
        expect(status.formattedStatus, equals('Credits refresh tomorrow'));
      });

      test('should provide appropriate status colors', () {
        // Green for refresh ready
        var status = CreditStatus(
          email: '<EMAIL>',
          currentCredits: 0,
          maxCredits: 20,
          plan: 'Trial',
          nextRefreshDate: DateTime.now(),
          needsRefresh: true,
          daysUntilRefresh: 0,
          creditsUsagePercentage: 1.0,
          remainingCreditsPercentage: 0.0,
          status: 'Credits ready for refresh',
        );
        
        expect(status.statusColor.value, equals(0xFF4CAF50)); // Green
        
        // Red for no credits
        status = CreditStatus(
          email: '<EMAIL>',
          currentCredits: 0,
          maxCredits: 20,
          plan: 'Trial',
          nextRefreshDate: DateTime.now().add(const Duration(days: 5)),
          needsRefresh: false,
          daysUntilRefresh: 5,
          creditsUsagePercentage: 1.0,
          remainingCreditsPercentage: 0.0,
          status: 'No credits available',
        );
        
        expect(status.statusColor.value, equals(0xFFF44336)); // Red
        
        // Orange for low credits (20% or less)
        status = CreditStatus(
          email: '<EMAIL>',
          currentCredits: 4,
          maxCredits: 20,
          plan: 'Trial',
          nextRefreshDate: DateTime.now().add(const Duration(days: 5)),
          needsRefresh: false,
          daysUntilRefresh: 5,
          creditsUsagePercentage: 0.8,
          remainingCreditsPercentage: 0.2,
          status: 'Low credits',
        );
        
        expect(status.statusColor.value, equals(0xFFFF9800)); // Orange
        
        // Green for sufficient credits
        status = CreditStatus(
          email: '<EMAIL>',
          currentCredits: 15,
          maxCredits: 20,
          plan: 'Trial',
          nextRefreshDate: DateTime.now().add(const Duration(days: 5)),
          needsRefresh: false,
          daysUntilRefresh: 5,
          creditsUsagePercentage: 0.25,
          remainingCreditsPercentage: 0.75,
          status: 'Credits available',
        );
        
        expect(status.statusColor.value, equals(0xFF4CAF50)); // Green
      });
    });
  });
}