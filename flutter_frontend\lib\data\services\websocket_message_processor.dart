import 'dart:async';
import 'dart:convert';
import '../models/websocket_message.dart';
import '../models/message.dart';
import '../models/groq_response.dart';
import '../models/correction_result.dart';
import '../models/app_error.dart';
import '../../core/utils/websocket_utils.dart';

/// Enhanced WebSocket message processor for real-time UI updates
class WebSocketMessageProcessor {
  final StreamController<Message> _messageController;
  final StreamController<AppError> _errorController;
  final StreamController<ProcessingState> _processingController;
  
  String? _currentMessageId;
  String _partialTranscript = '';
  bool _isProcessing = false;
  DateTime? _sessionStartTime;
  int _messageSequence = 0;
  
  WebSocketMessageProcessor({
    required StreamController<Message> messageController,
    required StreamController<AppError> errorController,
    required StreamController<ProcessingState> processingController,
  }) : _messageController = messageController,
       _errorController = errorController,
       _processingController = processingController;
  
  /// Process incoming WebSocket message with enhanced real-time handling
  Future<void> processMessage(WebSocketMessage wsMessage) async {
    try {
      _messageSequence++;
      
      switch (wsMessage.messageType) {
        case WebSocketMessageType.partialTranscript:
          await _handlePartialTranscript(wsMessage);
          break;
        case WebSocketMessageType.finalTranscript:
          await _handleFinalTranscript(wsMessage);
          break;
        case WebSocketMessageType.processing:
          await _handleProcessingMessage(wsMessage);
          break;
        case WebSocketMessageType.groqResponse:
          await _handleGroqResponse(wsMessage);
          break;
        case WebSocketMessageType.error:
          await _handleBackendError(wsMessage);
          break;
        case WebSocketMessageType.timeout:
          await _handleTimeout(wsMessage);
          break;
        case WebSocketMessageType.info:
          await _handleInfoMessage(wsMessage);
          break;
        case WebSocketMessageType.sessionStarted:
          await _handleSessionStarted(wsMessage);
          break;
      }
    } catch (e) {
      _handleProcessingError(e, wsMessage);
    }
  }
  
  /// Handle partial transcript with real-time UI updates
  Future<void> _handlePartialTranscript(WebSocketMessage wsMessage) async {
    final transcript = wsMessage.transcript;
    final confidence = wsMessage.data['confidence'] as double? ?? 0.0;
    final isInterim = wsMessage.data['is_interim'] as bool? ?? true;
    final wordCount = wsMessage.data['word_count'] as int? ?? 0;
    
    if (transcript.isNotEmpty && _currentMessageId != null) {
      _partialTranscript = transcript;
      
      // Create or update streaming message with enhanced formatting
      final message = Message.user(
        id: _currentMessageId!,
        text: _formatPartialTranscript(transcript, confidence, isInterim),
        isAudio: true,
        isStreaming: true,
      );
      
      _messageController.add(message);
      
      // Update processing state with enhanced information
      _processingController.add(ProcessingState(
        type: ProcessingType.transcribing,
        progress: confidence,
        message: _getTranscriptionStatusMessage(confidence, wordCount, isInterim),
        metadata: {
          'confidence': confidence,
          'word_count': wordCount,
          'is_interim': isInterim,
          'transcript_length': transcript.length,
          'message_id': _currentMessageId!,
        },
      ));
    }
  }
  
  /// Handle final transcript with state transitions
  Future<void> _handleFinalTranscript(WebSocketMessage wsMessage) async {
    final transcript = wsMessage.transcript;
    final confidence = wsMessage.data['confidence'] as double? ?? 1.0;
    final processingTime = wsMessage.data['processing_time'] as double? ?? 0.0;
    
    if (transcript.isNotEmpty && _currentMessageId != null) {
      // Create final user message
      final message = Message.user(
        id: _currentMessageId!,
        text: transcript,
        isAudio: true,
        isStreaming: false,
      );
      
      _messageController.add(message);
      
      // Update processing state to show completion
      _processingController.add(ProcessingState(
        type: ProcessingType.transcriptionComplete,
        progress: 1.0,
        message: 'Transcription complete (${processingTime.toStringAsFixed(2)}s)',
        metadata: {
          'confidence': confidence,
          'processing_time': processingTime,
          'transcript_length': transcript.length,
        },
      ));
      
      // Clear current message tracking
      _partialTranscript = '';
      _currentMessageId = null;
    }
  }
  
  /// Handle processing messages with enhanced UI indicators
  Future<void> _handleProcessingMessage(WebSocketMessage wsMessage) async {
    final processingType = wsMessage.data['processing_type'] as String? ?? 'ai_agents';
    final estimatedTime = wsMessage.data['estimated_time'] as double? ?? 3.0;
    final stage = wsMessage.data['stage'] as String? ?? 'analyzing';
    
    _isProcessing = true;
    
    // Create processing message with enhanced indicators
    final processingMessage = Message.ai(
      id: 'processing_${DateTime.now().millisecondsSinceEpoch}',
      text: _getProcessingMessage(processingType, stage),
      isProcessing: true,
    );
    
    _messageController.add(processingMessage);
    
    // Update processing state with detailed information
    _processingController.add(ProcessingState(
      type: ProcessingType.aiProcessing,
      progress: 0.0,
      message: _getProcessingStatusMessage(processingType, stage),
      estimatedTimeRemaining: estimatedTime,
      metadata: {
        'processing_type': processingType,
        'stage': stage,
        'started_at': DateTime.now().toIso8601String(),
      },
    ));
    
    // Start processing progress simulation
    _simulateProcessingProgress(estimatedTime);
  }
  
  /// Handle Groq AI responses with correction result processing
  Future<void> _handleGroqResponse(WebSocketMessage wsMessage) async {
    try {
      final groqResponse = GroqResponse.fromJson(wsMessage.data);
      final processingTime = wsMessage.data['processing_time'] as double? ?? 0.0;
      final agentType = wsMessage.data['agent_type'] as String? ?? 'correction';
      
      // Create AI message with correction results
      final message = Message.ai(
        id: groqResponse.id,
        text: groqResponse.text,
        correctionResult: groqResponse.correctionResult,
      );
      
      _messageController.add(message);
      
      // Update processing state to completion
      _processingController.add(ProcessingState(
        type: ProcessingType.aiComplete,
        progress: 1.0,
        message: 'AI processing complete (${processingTime.toStringAsFixed(2)}s)',
        metadata: {
          'agent_type': agentType,
          'processing_time': processingTime,
          'has_corrections': groqResponse.correctionResult?.hasCorrections ?? false,
          'suggestions_count': groqResponse.correctionResult?.suggestions.length ?? 0,
          'explanations_count': groqResponse.correctionResult?.explanations.length ?? 0,
        },
      ));
      
      _isProcessing = false;
      
    } catch (e) {
      _handleProcessingError(e, wsMessage, 'Failed to parse Groq response');
    }
  }
  
  /// Handle backend errors with proper styling
  Future<void> _handleBackendError(WebSocketMessage wsMessage) async {
    final errorCode = wsMessage.data['error_code'] as String? ?? 'UNKNOWN';
    final errorMessage = wsMessage.error;
    final isRecoverable = wsMessage.data['recoverable'] as bool? ?? true;
    final retryAfter = wsMessage.data['retry_after'] as int? ?? 5;
    
    final appError = AppError.backendError(
      details: errorMessage,
    );
    
    _errorController.add(appError);
    
    // Update processing state to show error
    _processingController.add(ProcessingState(
      type: ProcessingType.error,
      progress: 0.0,
      message: 'Error: $errorMessage',
      metadata: {
        'error_code': errorCode,
        'is_recoverable': isRecoverable,
        'retry_after': retryAfter,
      },
    ));
    
    _isProcessing = false;
  }
  
  /// Handle timeout messages with session cleanup
  Future<void> _handleTimeout(WebSocketMessage wsMessage) async {
    final timeoutType = wsMessage.data['timeout_type'] as String? ?? 'transcription';
    final timeoutDuration = wsMessage.data['timeout_duration'] as int? ?? 30;
    
    final appError = AppError.transcriptionTimeout(
      details: 'Request timed out after ${timeoutDuration}s',
    );
    
    _errorController.add(appError);
    
    // Update processing state to show timeout
    _processingController.add(ProcessingState(
      type: ProcessingType.timeout,
      progress: 0.0,
      message: 'Request timed out (${timeoutDuration}s)',
      metadata: {
        'timeout_type': timeoutType,
        'timeout_duration': timeoutDuration,
      },
    ));
    
    // Clean up current session state
    await _cleanupSession();
  }
  
  /// Handle info messages
  Future<void> _handleInfoMessage(WebSocketMessage wsMessage) async {
    final infoType = wsMessage.data['info_type'] as String? ?? 'general';
    final message = wsMessage.text;
    
    print('Backend info [$infoType]: $message');
    
    // Update processing state for important info messages
    if (infoType == 'session_info' || infoType == 'connection_info') {
      _processingController.add(ProcessingState(
        type: ProcessingType.info,
        progress: 1.0,
        message: message,
        metadata: {
          'info_type': infoType,
          'timestamp': DateTime.now().toIso8601String(),
        },
      ));
    }
  }
  
  /// Handle session started messages
  Future<void> _handleSessionStarted(WebSocketMessage wsMessage) async {
    final sessionId = wsMessage.data['session_id'] as String? ?? '';
    final serverVersion = wsMessage.data['server_version'] as String? ?? '';
    
    _sessionStartTime = DateTime.now();
    
    print('Session started: $sessionId (Server: $serverVersion)');
    
    _processingController.add(ProcessingState(
      type: ProcessingType.sessionStarted,
      progress: 1.0,
      message: 'Session started successfully',
      metadata: {
        'session_id': sessionId,
        'server_version': serverVersion,
        'started_at': _sessionStartTime!.toIso8601String(),
      },
    ));
  }
  
  /// Start new message tracking for recording session
  void startNewMessage(String messageId) {
    _currentMessageId = messageId;
    _partialTranscript = '';
    _messageSequence = 0;
    
    _processingController.add(ProcessingState(
      type: ProcessingType.recordingStarted,
      progress: 0.0,
      message: 'Recording started...',
      metadata: {
        'message_id': messageId,
        'started_at': DateTime.now().toIso8601String(),
      },
    ));
  }
  
  /// Clean up session state
  Future<void> _cleanupSession() async {
    _currentMessageId = null;
    _partialTranscript = '';
    _isProcessing = false;
    _sessionStartTime = null;
    _messageSequence = 0;
    
    _processingController.add(ProcessingState(
      type: ProcessingType.sessionEnded,
      progress: 1.0,
      message: 'Session ended',
      metadata: {
        'ended_at': DateTime.now().toIso8601String(),
      },
    ));
  }
  
  /// Format partial transcript with confidence and interim indicators
  String _formatPartialTranscript(String transcript, double confidence, bool isInterim) {
    final confidencePercent = (confidence * 100).toInt();
    final indicator = isInterim ? '●' : '◐';
    
    if (confidence < 0.5) {
      return '$indicator $transcript (${confidencePercent}%)';
    } else if (confidence < 0.8) {
      return '$indicator $transcript';
    } else {
      return '$indicator $transcript';
    }
  }
  
  /// Get processing message based on type and stage
  String _getProcessingMessage(String processingType, String stage) {
    switch (processingType) {
      case 'ai_agents':
        return '⚡ Processing with AI agents...';
      case 'grammar_check':
        return '📝 Checking grammar and corrections...';
      case 'translation':
        return '🌐 Translating and analyzing...';
      case 'pronunciation':
        return '🗣️ Analyzing pronunciation...';
      default:
        return '⚡ Processing with AI agents...';
    }
  }
  
  /// Get detailed processing status message
  String _getProcessingStatusMessage(String processingType, String stage) {
    switch (stage) {
      case 'analyzing':
        return 'Analyzing your speech...';
      case 'correcting':
        return 'Finding grammar corrections...';
      case 'suggesting':
        return 'Generating suggestions...';
      case 'finalizing':
        return 'Finalizing response...';
      default:
        return 'Processing...';
    }
  }
  
  /// Get transcription status message with enhanced information
  String _getTranscriptionStatusMessage(double confidence, int wordCount, bool isInterim) {
    final confidencePercent = (confidence * 100).toInt();
    
    if (isInterim) {
      if (wordCount > 0) {
        return '● Live transcription... ($wordCount words, ${confidencePercent}% confidence)';
      } else {
        return '● Listening...';
      }
    } else {
      return '◐ Processing transcript... (${confidencePercent}% confidence)';
    }
  }
  
  /// Simulate processing progress for better UX
  void _simulateProcessingProgress(double estimatedTime) {
    final steps = 10;
    final stepDuration = Duration(milliseconds: (estimatedTime * 1000 / steps).round());
    
    Timer.periodic(stepDuration, (timer) {
      if (!_isProcessing) {
        timer.cancel();
        return;
      }
      
      final progress = (timer.tick / steps).clamp(0.0, 0.9);
      
      _processingController.add(ProcessingState(
        type: ProcessingType.aiProcessing,
        progress: progress,
        message: 'Processing... ${(progress * 100).toInt()}%',
      ));
      
      if (timer.tick >= steps) {
        timer.cancel();
      }
    });
  }
  
  /// Handle processing errors
  void _handleProcessingError(dynamic error, WebSocketMessage wsMessage, [String? context]) {
    final errorMessage = context ?? 'Error processing WebSocket message';
    final appError = AppError.backendError(
      details: '$errorMessage: $error',
      originalException: error is Exception ? error : Exception(error.toString()),
    );
    
    _errorController.add(appError);
    
    _processingController.add(ProcessingState(
      type: ProcessingType.error,
      progress: 0.0,
      message: errorMessage,
      metadata: {
        'original_message': wsMessage.toString(),
        'error': error.toString(),
      },
    ));
  }
  
  /// Get current processing statistics
  Map<String, dynamic> getStats() {
    return {
      'current_message_id': _currentMessageId,
      'partial_transcript_length': _partialTranscript.length,
      'is_processing': _isProcessing,
      'session_start_time': _sessionStartTime?.toIso8601String(),
      'message_sequence': _messageSequence,
    };
  }
}

/// Processing state model for real-time UI updates
class ProcessingState {
  final ProcessingType type;
  final double progress;
  final String message;
  final double? estimatedTimeRemaining;
  final Map<String, dynamic>? metadata;
  final DateTime timestamp;
  
  ProcessingState({
    required this.type,
    required this.progress,
    required this.message,
    this.estimatedTimeRemaining,
    this.metadata,
  }) : timestamp = DateTime.now();
  
  @override
  String toString() {
    return 'ProcessingState(type: $type, progress: $progress, message: $message)';
  }
}

/// Processing type enumeration
enum ProcessingType {
  recordingStarted,
  transcribing,
  transcriptionComplete,
  aiProcessing,
  aiComplete,
  sessionStarted,
  sessionEnded,
  error,
  timeout,
  info,
}