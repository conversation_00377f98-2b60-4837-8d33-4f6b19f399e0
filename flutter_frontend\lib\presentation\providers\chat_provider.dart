import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../../data/models/message.dart';
import '../../data/models/chat_state.dart';
import '../../data/models/app_error.dart';
import '../../data/repositories/chat_repository.dart';
import '../../data/services/websocket_message_processor.dart';
import '../../data/services/tts_audio_service.dart';
import 'settings_provider.dart';
import 'profile_provider.dart';
import 'audio_provider.dart';

/// Provider for managing chat state and conversation
class ChatProvider extends ChangeNotifier {
  final ChatRepository _chatRepository = ChatRepository.instance;
  final TTSAudioService _ttsService = TTSAudioService();
  
  ChatState _state = ChatState.initial();
  StreamSubscription? _messageSubscription;
  StreamSubscription? _errorSubscription;
  StreamSubscription? _connectionSubscription;
  StreamSubscription? _recordingSubscription;
  StreamSubscription? _processingSubscription;
  
  bool _isDisposed = false;
  
  // Dependencies
  SettingsProvider? _settingsProvider;
  ProfileProvider? _profileProvider;
  AudioProvider? _audioProvider;
  
  // Processing state
  ProcessingState? _currentProcessingState;
  
  // Getters
  ChatState get state => _state;
  List<Message> get messages => _state.messages;
  String? get currentMessageId => _state.currentMessageId;
  bool get isConnected => _state.isConnected;
  bool get isRecording => _state.recordingState == RecordingState.listening;
  bool get isProcessing => _state.recordingState == RecordingState.processing;
  bool get isConnecting => _state.connectionState == ConnectionState.connecting;
  String get partialText => _state.partialText;
  int get retryCount => _state.retryCount;
  AppError? get lastError => _state.lastError;
  bool get hasError => _state.hasError;
  bool get canRetry => _state.canRetry;
  ProcessingState? get currentProcessingState => _currentProcessingState;
  
  /// Initialize the chat provider
  Future<void> initialize() async {
    if (_isDisposed) return;
    
    try {
      await _chatRepository.initialize();
      _setupSubscriptions();
      await _initializeChat();
      
      _state = _state.copyWith(isInitialized: true);
      notifyListeners();
      
    } catch (e) {
      _handleError(AppError.unknown(
        details: 'Failed to initialize chat: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      ));
    }
  }
  
  /// Set up subscriptions to repository streams
  void _setupSubscriptions() {
    _messageSubscription = _chatRepository.messageStream.listen(
      _handleNewMessage,
      onError: _handleError,
    );
    
    _errorSubscription = _chatRepository.errorStream.listen(
      _handleError,
    );
    
    _connectionSubscription = _chatRepository.connectionStream.listen(
      _handleConnectionChange,
    );
    
    _recordingSubscription = _chatRepository.recordingStream.listen(
      _handleRecordingChange,
    );
    
    _processingSubscription = _chatRepository.processingStream.listen(
      _handleProcessingStateChange,
    );
  }
  
  /// Initialize chat with welcome message matching Expo version
  Future<void> _initializeChat() async {
    final welcomeMessage = Message.ai(
      id: 'welcome_${DateTime.now().millisecondsSinceEpoch}',
      text: 'Hallo! Ich bin DeutschKorrekt, dein persönlicher Sprachtrainer für die deutsche Sprache. **Sprechen Sie einfach** und ich helfe Ihnen dabei, Ihre **Aussprache** und **Grammatik** zu verbessern! Drücken Sie den Mikrofon-Button und beginnen Sie zu sprechen.',
    );
    
    _addMessage(welcomeMessage);
  }
  
  /// Handle new messages from repository
  void _handleNewMessage(Message message) {
    if (_isDisposed) return;
    
    // Check if this is an update to an existing message
    final existingIndex = _state.messages.indexWhere((m) => m.id == message.id);
    
    List<Message> updatedMessages;
    if (existingIndex != -1) {
      // Update existing message
      updatedMessages = List.from(_state.messages);
      updatedMessages[existingIndex] = message;
    } else {
      // Add new message
      updatedMessages = List.from(_state.messages);
      
      // If this is a new AI message with an extracted sentence, trigger TTS cache cleanup
      // but keep the extracted sentences for UI display
      if (!message.isUser && message.extractedSentence != null) {
        final previousAIMessagesWithSentences = <String>[];
        
        // Collect IDs of previous AI messages with sentences for cache cleanup
        // but DON'T clear the extractedSentence from the UI
        for (int i = 0; i < updatedMessages.length; i++) {
          if (!updatedMessages[i].isUser && updatedMessages[i].extractedSentence != null) {
            previousAIMessagesWithSentences.add(updatedMessages[i].id);
            if (kDebugMode) {
              debugPrint('TTS: Found previous AI message ${updatedMessages[i].id} with extracted sentence for cache cleanup');
            }
          }
        }
        
        // Trigger TTS cache cleanup for new Groq response (async, don't wait)
        // This clears the audio cache but keeps the UI elements
        _handleNewGroqResponseCacheCleanup(
          newMessageId: message.id,
          previousMessagesCleared: previousAIMessagesWithSentences,
        );
      }
      
      updatedMessages.add(message);
    }
    
    _state = _state.copyWith(
      messages: updatedMessages,
      lastActivity: DateTime.now(),
    );
    
    // Update recording state based on message type
    if (message.isUser && message.isStreaming) {
      _state = _state.copyWith(
        recordingState: RecordingState.listening,
        currentMessageId: message.id,
        partialText: message.text,
      );
    } else if (message.isUser && !message.isStreaming) {
      _state = _state.copyWith(
        recordingState: RecordingState.processing,
        partialText: '',
      );
    } else if (!message.isUser && message.isProcessing) {
      _state = _state.copyWith(
        recordingState: RecordingState.processing,
      );
    } else if (!message.isUser && !message.isProcessing) {
      _state = _state.copyWith(
        recordingState: RecordingState.idle,
        currentMessageId: null,
      );
    }
    
    notifyListeners();
  }
  
  /// Handle errors from repository
  void _handleError(dynamic error) {
    if (_isDisposed) return;
    
    AppError appError;
    if (error is AppError) {
      appError = error;
    } else {
      appError = AppError.unknown(
        details: error.toString(),
        originalException: error is Exception ? error : Exception(error.toString()),
      );
    }
    
    _state = _state.copyWith(
      lastError: appError,
      connectionState: appError.type == AppErrorType.websocketConnectionFailed 
          ? ConnectionState.error 
          : _state.connectionState,
      recordingState: appError.type == AppErrorType.audioRecordingFailed 
          ? RecordingState.error 
          : _state.recordingState,
    );
    
    notifyListeners();
  }
  
  /// Handle connection state changes
  void _handleConnectionChange(bool connected) {
    if (_isDisposed) return;
    
    _state = _state.copyWith(
      connectionState: connected ? ConnectionState.connected : ConnectionState.disconnected,
      retryCount: connected ? 0 : _state.retryCount,
    );
    
    notifyListeners();
  }
  
  /// Handle recording state changes
  void _handleRecordingChange(bool recording) {
    if (_isDisposed) return;
    
    if (!recording && _state.recordingState == RecordingState.listening) {
      _state = _state.copyWith(
        recordingState: RecordingState.processing,
      );
      notifyListeners();
    }
  }
  
  /// Handle processing state changes from WebSocket message processor
  void _handleProcessingStateChange(ProcessingState processingState) {
    if (_isDisposed) return;
    
    _currentProcessingState = processingState;
    
    // Update chat state based on processing type
    switch (processingState.type) {
      case ProcessingType.recordingStarted:
        _state = _state.copyWith(
          recordingState: RecordingState.connecting,
        );
        break;
      case ProcessingType.transcribing:
        _state = _state.copyWith(
          recordingState: RecordingState.listening,
        );
        break;
      case ProcessingType.transcriptionComplete:
        _state = _state.copyWith(
          recordingState: RecordingState.processing,
        );
        break;
      case ProcessingType.aiProcessing:
        _state = _state.copyWith(
          recordingState: RecordingState.processing,
        );
        break;
      case ProcessingType.aiComplete:
      case ProcessingType.sessionEnded:
        _state = _state.copyWith(
          recordingState: RecordingState.idle,
          currentMessageId: null,
        );
        break;
      case ProcessingType.error:
        _state = _state.copyWith(
          recordingState: RecordingState.error,
        );
        break;
      default:
        break;
    }
    
    notifyListeners();
  }
  
  /// Handle TTS cache cleanup when new Groq response arrives
  void _handleNewGroqResponseCacheCleanup({
    required String newMessageId,
    required List<String> previousMessagesCleared,
  }) {
    if (kDebugMode) {
      debugPrint('TTS: Starting cache cleanup for new Groq response $newMessageId');
      debugPrint('TTS: Cleared ${previousMessagesCleared.length} previous AI messages with sentences');
    }
    
    // Run cache cleanup asynchronously without blocking the UI
    _ttsService.onNewGroqResponse().then((cleanupResult) {
      if (cleanupResult['success'] == true) {
        if (kDebugMode) {
          debugPrint('TTS: Cache cleanup completed successfully for new Groq response $newMessageId');
          debugPrint('TTS: Cleanup stats - freed ${cleanupResult['freedFiles']} files (${cleanupResult['freedSpaceKB']}KB) in ${cleanupResult['cleanupDuration']}ms');
        }
        
        // Log successful cleanup for monitoring
        _logTTSCacheOperation('CACHE_CLEANUP_SUCCESS', {
          'newMessageId': newMessageId,
          'previousMessagesCleared': previousMessagesCleared,
          'clearedCount': previousMessagesCleared.length,
          'cleanupStats': cleanupResult,
          'timestamp': DateTime.now().toIso8601String(),
        });
      } else {
        if (kDebugMode) {
          debugPrint('TTS: Cache cleanup failed for new Groq response $newMessageId: ${cleanupResult['error']}');
        }
        
        // Log cleanup failure for monitoring
        _logTTSError('CACHE_CLEANUP_FAILED', cleanupResult['error'] ?? 'Unknown cleanup error', {
          'newMessageId': newMessageId,
          'previousMessagesCleared': previousMessagesCleared,
          'clearedCount': previousMessagesCleared.length,
          'cleanupStats': cleanupResult,
          'context': 'new_groq_response',
          'timestamp': DateTime.now().toIso8601String(),
        });
      }
      
    }).catchError((error) {
      if (kDebugMode) {
        debugPrint('TTS: Unexpected error during cache cleanup for new Groq response $newMessageId: $error');
      }
      
      // Log unexpected error for monitoring
      _logTTSError('CACHE_CLEANUP_EXCEPTION', error.toString(), {
        'newMessageId': newMessageId,
        'previousMessagesCleared': previousMessagesCleared,
        'clearedCount': previousMessagesCleared.length,
        'context': 'new_groq_response_exception',
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      // Don't propagate error - cache cleanup failure shouldn't break the chat flow
    });
  }
  
  /// Log TTS cache operations for debugging and monitoring
  void _logTTSCacheOperation(String operationType, Map<String, dynamic> context) {
    final logEntry = {
      'type': 'TTS_CACHE_OPERATION',
      'operationType': operationType,
      'context': context,
      'timestamp': DateTime.now().toIso8601String(),
      'messagesCount': messages.length,
      'isConnected': isConnected,
    };
    
    if (kDebugMode) {
      debugPrint('TTS Cache Operation Log: ${json.encode(logEntry)}');
    }
    
    // In production, this could be sent to a logging service like Firebase Analytics,
    // Sentry, or other monitoring platforms
  }

  /// Log TTS-related errors for debugging and monitoring
  void _logTTSError(String errorCode, String errorMessage, Map<String, dynamic> context) {
    final logEntry = {
      'type': 'TTS_ERROR',
      'errorCode': errorCode,
      'errorMessage': errorMessage,
      'context': context,
      'timestamp': DateTime.now().toIso8601String(),
      'messagesCount': messages.length,
      'isConnected': isConnected,
    };
    
    if (kDebugMode) {
      debugPrint('TTS Error Log: ${json.encode(logEntry)}');
    }
    
    // In production, this could be sent to a logging service like Firebase Analytics,
    // Sentry, or other monitoring platforms
  }

  /// Add a message to the conversation
  void _addMessage(Message message) {
    final updatedMessages = [..._state.messages, message];
    _state = _state.copyWith(
      messages: updatedMessages,
      lastActivity: DateTime.now(),
    );
    notifyListeners();
  }
  
  /// Start recording
  Future<void> startRecording() async {
    if (_isDisposed || isRecording) return;
    
    try {
      _state = _state.copyWith(
        recordingState: RecordingState.connecting,
        lastError: null,
      );
      notifyListeners();
      
      await _chatRepository.startRecording();
      
    } catch (e) {
      _handleError(AppError.audioRecordingFailed(
        details: 'Failed to start recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      ));
    }
  }
  
  /// Stop recording
  Future<void> stopRecording() async {
    if (_isDisposed || !isRecording) return;
    
    try {
      await _chatRepository.stopRecording();
      
    } catch (e) {
      _handleError(AppError.audioRecordingFailed(
        details: 'Failed to stop recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      ));
    }
  }
  
  /// Connect to backend
  Future<void> connect() async {
    if (_isDisposed || isConnected) return;
    
    try {
      _state = _state.copyWith(
        connectionState: ConnectionState.connecting,
        lastError: null,
      );
      notifyListeners();
      
      await _chatRepository.connect();
      
    } catch (e) {
      _handleError(AppError.websocketConnectionFailed(
        details: 'Failed to connect: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      ));
    }
  }
  
  /// Disconnect from backend
  Future<void> disconnect() async {
    if (_isDisposed) return;
    
    try {
      await _chatRepository.disconnect();
      
      _state = _state.copyWith(
        connectionState: ConnectionState.disconnected,
        recordingState: RecordingState.idle,
      );
      notifyListeners();
      
    } catch (e) {
      _handleError(AppError.websocketConnectionFailed(
        details: 'Failed to disconnect: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      ));
    }
  }
  
  /// Retry connection
  Future<void> retryConnection() async {
    if (_isDisposed || !canRetry) return;
    
    _state = _state.copyWith(
      retryCount: _state.retryCount + 1,
      connectionState: ConnectionState.reconnecting,
      lastError: null,
    );
    notifyListeners();
    
    await connect();
  }
  
  /// Send a text message
  void sendTextMessage(String text) {
    if (_isDisposed || text.trim().isEmpty) return;
    
    _chatRepository.sendTextMessage(text.trim());
  }
  
  /// Clear error state
  void clearError() {
    if (_isDisposed) return;
    
    _state = _state.copyWith(lastError: null);
    notifyListeners();
  }
  
  /// Clear all messages
  void clearMessages() {
    if (_isDisposed) return;
    
    _state = _state.copyWith(
      messages: const [],
      currentMessageId: null,
      partialText: '',
      recordingState: RecordingState.idle,
    );
    notifyListeners();
    
    // Re-add welcome message
    _initializeChat();
  }
  
  /// Reset chat state
  void reset() {
    if (_isDisposed) return;
    
    _state = ChatState.initial();
    notifyListeners();
    
    _initializeChat();
  }
  
  /// Update dependencies from other providers
  void updateDependencies(
    SettingsProvider? settingsProvider,
    ProfileProvider? profileProvider,
    AudioProvider? audioProvider,
  ) {
    _settingsProvider = settingsProvider;
    _profileProvider = profileProvider;
    _audioProvider = audioProvider;
    
    // Update chat repository with new audio configuration if available
    if (_audioProvider != null && _settingsProvider != null) {
      _chatRepository.updateAudioConfig(_settingsProvider!.audioConfig);
    }
    
    // Update profile statistics when messages are processed
    if (_profileProvider != null && messages.isNotEmpty) {
      final aiMessages = messages.where((m) => !m.isUser).length;
      
      if (aiMessages > 0) {
        // Update corrections count in profile
        _profileProvider!.incrementCorrections();
      }
    }
  }
  
  /// Get chat statistics
  Map<String, dynamic> getStats() {
    return {
      'state': _state.toString(),
      'messagesCount': messages.length,
      'repositoryStats': _chatRepository.getStats(),
      'hasSettingsProvider': _settingsProvider != null,
      'hasProfileProvider': _profileProvider != null,
      'hasAudioProvider': _audioProvider != null,
    };
  }
  
  @override
  void dispose() {
    _isDisposed = true;
    
    _messageSubscription?.cancel();
    _errorSubscription?.cancel();
    _connectionSubscription?.cancel();
    _recordingSubscription?.cancel();
    _processingSubscription?.cancel();
    
    // Dispose TTS service
    _ttsService.dispose();
    
    super.dispose();
  }
}