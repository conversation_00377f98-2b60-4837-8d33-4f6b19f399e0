import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import '../models/app_error.dart';

/// Service for handling app permissions, especially microphone access
class PermissionsService {
  static PermissionsService? _instance;
  
  /// Singleton instance
  static PermissionsService get instance {
    _instance ??= PermissionsService._();
    return _instance!;
  }
  
  PermissionsService._();
  
  /// Request microphone permission
  Future<bool> requestMicrophonePermission() async {
    try {
      final status = await Permission.microphone.request();
      return status == PermissionStatus.granted;
    } catch (e) {
      print('Error requesting microphone permission: $e');
      return false;
    }
  }
  
  /// Check current microphone permission status
  Future<PermissionStatus> getMicrophonePermissionStatus() async {
    try {
      return await Permission.microphone.status;
    } catch (e) {
      print('Error checking microphone permission: $e');
      return PermissionStatus.denied;
    }
  }
  
  /// Check if microphone permission is granted
  Future<bool> hasMicrophonePermission() async {
    final status = await getMicrophonePermissionStatus();
    return status == PermissionStatus.granted;
  }
  
  /// Check if microphone permission is permanently denied
  Future<bool> isMicrophonePermissionPermanentlyDenied() async {
    final status = await getMicrophonePermissionStatus();
    return status == PermissionStatus.permanentlyDenied;
  }
  
  /// Open app settings for permission management
  Future<bool> openAppSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      print('Error opening app settings: $e');
      return false;
    }
  }
  
  /// Show permission dialog with proper messaging
  Future<bool> showPermissionDialog() async {
    final status = await getMicrophonePermissionStatus();
    
    if (status == PermissionStatus.permanentlyDenied) {
      // User needs to go to settings
      return false;
    }
    
    if (await shouldShowPermissionRationale()) {
      // Show rationale before requesting
      return true;
    }
    
    return await requestMicrophonePermission();
  }
  
  /// Get detailed permission guidance for users
  Map<String, String> getPermissionGuidance() {
    return {
      'title': 'Microphone Access Required',
      'message': getPlatformSpecificRationale(),
      'primaryAction': 'Grant Permission',
      'secondaryAction': 'Cancel',
      'settingsAction': 'Open Settings',
    };
  }
  
  /// Check if permission was denied multiple times
  Future<bool> wasPermissionDeniedMultipleTimes() async {
    final status = await getMicrophonePermissionStatus();
    return status == PermissionStatus.permanentlyDenied;
  }
  
  /// Get step-by-step instructions for enabling permissions
  List<String> getPermissionInstructions() {
    if (Platform.isAndroid) {
      return [
        'Open your device Settings',
        'Navigate to Apps or Application Manager',
        'Find and select DeutschKorrekt',
        'Tap on Permissions',
        'Enable Microphone permission',
        'Return to the app and try again',
      ];
    } else if (Platform.isIOS) {
      return [
        'Open your device Settings',
        'Scroll down and find DeutschKorrekt',
        'Tap on DeutschKorrekt',
        'Enable Microphone permission',
        'Return to the app and try again',
      ];
    } else {
      return [
        'Please enable microphone permission in your system settings',
        'Return to the app and try again',
      ];
    }
  }
  
  /// Get user-friendly permission status message
  String getPermissionStatusMessage(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Microphone access granted';
      case PermissionStatus.denied:
        return 'Microphone access denied. Please grant permission to use voice features.';
      case PermissionStatus.restricted:
        return 'Microphone access restricted by system settings.';
      case PermissionStatus.limited:
        return 'Microphone access limited. Some features may not work properly.';
      case PermissionStatus.permanentlyDenied:
        return 'Microphone access permanently denied. Please enable it in app settings.';
      case PermissionStatus.provisional:
        return 'Microphone access provisional. Please confirm permission.';
      default:
        return 'Unknown microphone permission status.';
    }
  }
  
  /// Handle permission request result and return appropriate error if needed
  Future<AppError?> handleMicrophonePermissionResult() async {
    final status = await getMicrophonePermissionStatus();
    
    switch (status) {
      case PermissionStatus.granted:
        return null; // No error
      case PermissionStatus.denied:
        return AppError.microphonePermissionDenied(
          details: 'User denied microphone permission',
        );
      case PermissionStatus.permanentlyDenied:
        return AppError.microphonePermissionDenied(
          details: 'Microphone permission permanently denied. User must enable it in settings.',
        );
      case PermissionStatus.restricted:
        return AppError.microphonePermissionDenied(
          details: 'Microphone access restricted by system settings',
        );
      default:
        return AppError.microphonePermissionDenied(
          details: 'Microphone permission not available: $status',
        );
    }
  }
  
  /// Request permission with proper error handling
  Future<bool> requestMicrophonePermissionWithHandling() async {
    try {
      // Check if already granted
      if (await hasMicrophonePermission()) {
        return true;
      }
      
      // Check if permanently denied
      if (await isMicrophonePermissionPermanentlyDenied()) {
        return false;
      }
      
      // Request permission
      return await requestMicrophonePermission();
    } catch (e) {
      print('Error in requestMicrophonePermissionWithHandling: $e');
      return false;
    }
  }
  
  /// Get platform-specific permission rationale
  String getPlatformSpecificRationale() {
    if (Platform.isAndroid) {
      return 'DeutschKorrekt needs microphone access to record your speech for German language learning and correction. Your audio is processed securely and not stored permanently.';
    } else if (Platform.isIOS) {
      return 'This app needs access to your microphone to record speech for German language learning and AI-powered corrections.';
    } else {
      return 'Microphone access is required for voice recording features.';
    }
  }
  
  /// Check if should show permission rationale (Android specific)
  Future<bool> shouldShowPermissionRationale() async {
    if (!Platform.isAndroid) return false;
    
    try {
      final status = await Permission.microphone.status;
      return status == PermissionStatus.denied;
    } catch (e) {
      return false;
    }
  }
}