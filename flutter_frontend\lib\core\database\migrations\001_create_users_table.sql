-- Create Users table for storing user profile information
CREATE TABLE IF NOT EXISTS users (
  email TEXT PRIMARY KEY,
  plan TEXT NOT NULL DEFAULT 'Trial',
  date_joined TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  date_plan TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  max_credits INTEGER NOT NULL DEFAULT 20,
  current_credits INTEGER NOT NULL DEFAULT 20,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add constraints
ALTER TABLE users ADD CONSTRAINT check_max_credits_positive CHECK (max_credits >= 0);
ALTER TABLE users ADD CONSTRAINT check_current_credits_non_negative CHECK (current_credits >= 0);
ALTER TABLE users ADD CONSTRAINT check_current_credits_not_exceed_max CHECK (current_credits <= max_credits);
ALTER TABLE users ADD CONSTRAINT check_valid_plan CHECK (plan IN ('Trial', 'Basic', 'Premium', 'Enterprise'));

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_plan ON users(plan);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();