import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/services/websocket_message_processor.dart';

/// Widget for displaying real-time processing states with animations
class ProcessingStateWidget extends StatefulWidget {
  final ProcessingState processingState;
  final bool showProgress;
  final bool showEstimatedTime;
  final EdgeInsets? padding;
  
  const ProcessingStateWidget({
    super.key,
    required this.processingState,
    this.showProgress = true,
    this.showEstimatedTime = true,
    this.padding,
  });

  @override
  State<ProcessingStateWidget> createState() => _ProcessingStateWidgetState();
}

class _ProcessingStateWidgetState extends State<ProcessingStateWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _progressAnimation;
  
  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.processingState.progress,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _startAnimation();
  }
  
  @override
  void didUpdateWidget(ProcessingStateWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.processingState.progress != widget.processingState.progress) {
      _progressAnimation = Tween<double>(
        begin: oldWidget.processingState.progress,
        end: widget.processingState.progress,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ));
      
      _animationController.forward(from: 0.0);
    }
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  void _startAnimation() {
    if (_shouldPulse()) {
      _animationController.repeat(reverse: true);
    } else {
      _animationController.forward();
    }
  }
  
  bool _shouldPulse() {
    return widget.processingState.type == ProcessingType.transcribing ||
           widget.processingState.type == ProcessingType.aiProcessing ||
           widget.processingState.type == ProcessingType.recordingStarted;
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding ?? const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getBackgroundColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getBackgroundColor().withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Main status row
          Row(
            children: [
              // Animated icon
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _shouldPulse() ? _pulseAnimation.value : 1.0,
                    child: Icon(
                      _getIcon(),
                      color: _getIconColor(),
                      size: 16,
                    ),
                  );
                },
              ),
              
              const SizedBox(width: 8),
              
              // Status message
              Expanded(
                child: Text(
                  widget.processingState.message,
                  style: AppTextStyles.captionText.copyWith(
                    color: _getTextColor(),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              
              // Estimated time
              if (widget.showEstimatedTime && 
                  widget.processingState.estimatedTimeRemaining != null)
                _buildEstimatedTime(),
            ],
          ),
          
          // Progress bar
          if (widget.showProgress && _shouldShowProgress()) ...[
            const SizedBox(height: 8),
            _buildProgressBar(),
          ],
          
          // Metadata display for debugging (only in debug mode)
          if (widget.processingState.metadata != null && 
              widget.processingState.metadata!.isNotEmpty) ...[
            const SizedBox(height: 6),
            _buildMetadata(),
          ],
        ],
      ),
    );
  }
  
  Widget _buildEstimatedTime() {
    final timeRemaining = widget.processingState.estimatedTimeRemaining!;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getBackgroundColor().withOpacity(0.2),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        '~${timeRemaining.toStringAsFixed(0)}s',
        style: AppTextStyles.captionText.copyWith(
          fontSize: 10,
          color: _getTextColor(),
        ),
      ),
    );
  }
  
  Widget _buildProgressBar() {
    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Progress bar
            Container(
              height: 4,
              decoration: BoxDecoration(
                color: _getBackgroundColor().withOpacity(0.2),
                borderRadius: BorderRadius.circular(2),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: _progressAnimation.value.clamp(0.0, 1.0),
                child: Container(
                  decoration: BoxDecoration(
                    color: _getBackgroundColor(),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
            ),
            
            // Progress percentage
            const SizedBox(height: 4),
            Text(
              '${(_progressAnimation.value * 100).toInt()}%',
              style: AppTextStyles.captionText.copyWith(
                fontSize: 10,
                color: _getTextColor().withOpacity(0.7),
              ),
            ),
          ],
        );
      },
    );
  }
  
  Widget _buildMetadata() {
    final metadata = widget.processingState.metadata!;
    final importantKeys = ['confidence', 'processing_time', 'error_code'];
    
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: metadata.entries
          .where((entry) => importantKeys.contains(entry.key))
          .map((entry) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.slate700.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '${entry.key}: ${entry.value}',
                  style: AppTextStyles.captionText.copyWith(
                    fontSize: 9,
                    color: AppColors.lightText.withOpacity(0.6),
                  ),
                ),
              ))
          .toList(),
    );
  }
  
  bool _shouldShowProgress() {
    return widget.processingState.type == ProcessingType.transcribing ||
           widget.processingState.type == ProcessingType.aiProcessing;
  }
  
  IconData _getIcon() {
    switch (widget.processingState.type) {
      case ProcessingType.recordingStarted:
        return Icons.mic;
      case ProcessingType.transcribing:
        return Icons.hearing;
      case ProcessingType.transcriptionComplete:
        return Icons.check_circle;
      case ProcessingType.aiProcessing:
        return Icons.psychology;
      case ProcessingType.aiComplete:
        return Icons.check_circle;
      case ProcessingType.sessionStarted:
        return Icons.play_circle;
      case ProcessingType.sessionEnded:
        return Icons.stop_circle;
      case ProcessingType.error:
        return Icons.error;
      case ProcessingType.timeout:
        return Icons.timer_off;
      case ProcessingType.info:
        return Icons.info;
    }
  }
  
  Color _getIconColor() {
    switch (widget.processingState.type) {
      case ProcessingType.recordingStarted:
      case ProcessingType.transcribing:
        return AppColors.infoBlue;
      case ProcessingType.transcriptionComplete:
      case ProcessingType.aiComplete:
        return AppColors.successGreen;
      case ProcessingType.aiProcessing:
        return AppColors.warningYellow;
      case ProcessingType.sessionStarted:
      case ProcessingType.sessionEnded:
        return AppColors.lightText;
      case ProcessingType.error:
        return AppColors.errorRed;
      case ProcessingType.timeout:
        return AppColors.warningYellow;
      case ProcessingType.info:
        return AppColors.infoBlue;
    }
  }
  
  Color _getBackgroundColor() {
    return _getIconColor();
  }
  
  Color _getTextColor() {
    return _getIconColor();
  }
}

/// Compact processing state indicator for smaller spaces
class CompactProcessingStateWidget extends StatelessWidget {
  final ProcessingState processingState;
  final double size;
  
  const CompactProcessingStateWidget({
    super.key,
    required this.processingState,
    this.size = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getColor().withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getIcon(),
            color: _getColor(),
            size: size,
          ),
          const SizedBox(width: 4),
          Text(
            _getShortMessage(),
            style: AppTextStyles.captionText.copyWith(
              fontSize: size - 4,
              color: _getColor(),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
  
  IconData _getIcon() {
    switch (processingState.type) {
      case ProcessingType.recordingStarted:
        return Icons.mic;
      case ProcessingType.transcribing:
        return Icons.hearing;
      case ProcessingType.transcriptionComplete:
        return Icons.check;
      case ProcessingType.aiProcessing:
        return Icons.psychology;
      case ProcessingType.aiComplete:
        return Icons.check;
      case ProcessingType.sessionStarted:
        return Icons.play_arrow;
      case ProcessingType.sessionEnded:
        return Icons.stop;
      case ProcessingType.error:
        return Icons.error;
      case ProcessingType.timeout:
        return Icons.timer_off;
      case ProcessingType.info:
        return Icons.info;
    }
  }
  
  String _getShortMessage() {
    switch (processingState.type) {
      case ProcessingType.recordingStarted:
        return 'Recording';
      case ProcessingType.transcribing:
        return 'Listening';
      case ProcessingType.transcriptionComplete:
        return 'Done';
      case ProcessingType.aiProcessing:
        return 'Processing';
      case ProcessingType.aiComplete:
        return 'Complete';
      case ProcessingType.sessionStarted:
        return 'Started';
      case ProcessingType.sessionEnded:
        return 'Ended';
      case ProcessingType.error:
        return 'Error';
      case ProcessingType.timeout:
        return 'Timeout';
      case ProcessingType.info:
        return 'Info';
    }
  }
  
  Color _getColor() {
    switch (processingState.type) {
      case ProcessingType.recordingStarted:
      case ProcessingType.transcribing:
        return AppColors.infoBlue;
      case ProcessingType.transcriptionComplete:
      case ProcessingType.aiComplete:
        return AppColors.successGreen;
      case ProcessingType.aiProcessing:
        return AppColors.warningYellow;
      case ProcessingType.sessionStarted:
      case ProcessingType.sessionEnded:
        return AppColors.lightText;
      case ProcessingType.error:
        return AppColors.errorRed;
      case ProcessingType.timeout:
        return AppColors.warningYellow;
      case ProcessingType.info:
        return AppColors.infoBlue;
    }
  }
}

/// Processing state overlay for full-screen processing indicators
class ProcessingStateOverlay extends StatelessWidget {
  final ProcessingState processingState;
  final VoidCallback? onCancel;
  
  const ProcessingStateOverlay({
    super.key,
    required this.processingState,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.slate800,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppColors.slate600.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Processing indicator
              ProcessingStateWidget(
                processingState: processingState,
                showProgress: true,
                showEstimatedTime: true,
              ),
              
              // Cancel button
              if (onCancel != null) ...[
                const SizedBox(height: 16),
                TextButton(
                  onPressed: onCancel,
                  child: Text(
                    'Cancel',
                    style: AppTextStyles.smallButtonText.copyWith(
                      color: AppColors.lightText,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}