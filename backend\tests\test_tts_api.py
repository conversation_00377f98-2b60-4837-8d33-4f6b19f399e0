"""
API endpoint tests for TTS service.
Tests the FastAPI TTS endpoints with comprehensive coverage of:
- TTS generation endpoint with various inputs
- Error handling and HTTP status codes
- Request/response validation
- Health check endpoint
- Service info endpoint
- Authentication and rate limiting scenarios
"""

import pytest
import json
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import Mock, patch, AsyncMock
from models.data_models import TTSRequest, TTSResponse, TTSErrorResponse, TTSHealthResponse


@pytest.fixture
def mock_tts_service():
    """Mock TTS service for API testing."""
    mock_service = Mock()
    
    # Mock successful response
    async def mock_generate_speech(text: str, request_id: str = None):
        return {
            "success": True,
            "audio_data": b"fake_mp3_audio_data",
            "content_type": "audio/mpeg",
            "duration_seconds": 2.5,
            "processing_time": 0.5,
            "original_text": text,
            "sanitized_text": text
        }
    
    mock_service.generate_speech = AsyncMock(side_effect=mock_generate_speech)
    
    # Mock health check
    async def mock_health_check():
        return {
            "healthy": True,
            "response_time": 0.5,
            "audio_size": 1024,
            "message": "TTS service is operational"
        }
    
    mock_service.health_check = AsyncMock(side_effect=mock_health_check)
    
    # Mock service info
    mock_service.get_service_info.return_value = {
        "service_name": "TTS Service",
        "max_text_length": 500,
        "google_tts_client": {"voice_name": "de-DE-Chirp3-HD-Aoede"},
        "features": ["Text validation", "German voice", "MP3 output"]
    }
    
    return mock_service


@pytest.fixture
def client(mock_tts_service):
    """Create test client with mocked TTS service."""
    with patch('main.tts_service', mock_tts_service):
        from main import app
        return TestClient(app)


class TestTTSAPI:
    """Test suite for TTS API endpoints."""

    def test_generate_speech_success(self, client):
        """Test successful TTS generation."""
        request_data = {
            "text": "Hallo, das ist ein Test.",
            "message_id": "test-message-123",
            "voice_config": "de-DE-Chirp3-HD-Aoede"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "audio/mpeg"
        assert response.headers["x-duration-seconds"] == "2.5"
        assert response.headers["x-processing-time"] == "0.5"
        assert "x-original-text-length" in response.headers
        assert "x-sanitized-text-length" in response.headers
        assert response.content == b"fake_mp3_audio_data"

    def test_generate_speech_minimal_request(self, client):
        """Test TTS generation with minimal required fields."""
        request_data = {
            "text": "Test text",
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "audio/mpeg"

    def test_generate_speech_empty_text(self, client, mock_tts_service):
        """Test TTS generation with empty text."""
        # Mock service to return validation error
        async def mock_failed_generate_speech(text: str, request_id: str = None):
            return {
                "success": False,
                "error_message": "Text cannot be empty or only whitespace",
                "error_code": "INVALID_INPUT",
                "processing_time": 0.0
            }
        
        mock_tts_service.generate_speech = AsyncMock(side_effect=mock_failed_generate_speech)
        
        request_data = {
            "text": "",
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 400
        response_data = response.json()
        assert response_data["error_code"] == "INVALID_INPUT"
        assert "empty" in response_data["error_message"].lower()

    def test_generate_speech_text_too_long(self, client, mock_tts_service):
        """Test TTS generation with text exceeding maximum length."""
        # Mock service to return validation error
        async def mock_failed_generate_speech(text: str, request_id: str = None):
            return {
                "success": False,
                "error_message": f"Text too long: {len(text)} characters (maximum: 500)",
                "error_code": "INVALID_INPUT",
                "processing_time": 0.0
            }
        
        mock_tts_service.generate_speech = AsyncMock(side_effect=mock_failed_generate_speech)
        
        request_data = {
            "text": "x" * 501,  # Exceeds maximum length
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 400
        response_data = response.json()
        assert response_data["error_code"] == "INVALID_INPUT"
        assert "too long" in response_data["error_message"].lower()

    def test_generate_speech_quota_exceeded(self, client, mock_tts_service):
        """Test TTS generation with quota exceeded error."""
        # Mock service to return quota error
        async def mock_quota_error(text: str, request_id: str = None):
            return {
                "success": False,
                "error_message": "TTS generation failed: TTS API quota exceeded",
                "error_code": "QUOTA_EXCEEDED",
                "processing_time": 0.5
            }
        
        mock_tts_service.generate_speech = AsyncMock(side_effect=mock_quota_error)
        
        request_data = {
            "text": "Test text",
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 429
        response_data = response.json()
        assert response_data["error_code"] == "QUOTA_EXCEEDED"
        assert response_data["retry_after"] == 60

    def test_generate_speech_permission_denied(self, client, mock_tts_service):
        """Test TTS generation with permission denied error."""
        # Mock service to return permission error
        async def mock_permission_error(text: str, request_id: str = None):
            return {
                "success": False,
                "error_message": "TTS generation failed: TTS API permission denied",
                "error_code": "PERMISSION_DENIED",
                "processing_time": 0.3
            }
        
        mock_tts_service.generate_speech = AsyncMock(side_effect=mock_permission_error)
        
        request_data = {
            "text": "Test text",
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 403
        response_data = response.json()
        assert response_data["error_code"] == "PERMISSION_DENIED"
        assert "permission denied" in response_data["error_message"].lower()

    def test_generate_speech_service_unavailable(self, client, mock_tts_service):
        """Test TTS generation with service unavailable error."""
        # Mock service to return service unavailable error
        async def mock_service_error(text: str, request_id: str = None):
            return {
                "success": False,
                "error_message": "TTS generation failed: TTS service unavailable",
                "error_code": "SERVICE_UNAVAILABLE",
                "processing_time": 1.0
            }
        
        mock_tts_service.generate_speech = AsyncMock(side_effect=mock_service_error)
        
        request_data = {
            "text": "Test text",
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 503
        response_data = response.json()
        assert response_data["error_code"] == "SERVICE_UNAVAILABLE"
        assert "service unavailable" in response_data["error_message"].lower()

    def test_generate_speech_internal_error(self, client, mock_tts_service):
        """Test TTS generation with internal error."""
        # Mock service to return internal error
        async def mock_internal_error(text: str, request_id: str = None):
            return {
                "success": False,
                "error_message": "TTS generation failed: Unexpected error",
                "error_code": "INTERNAL_ERROR",
                "processing_time": 0.2
            }
        
        mock_tts_service.generate_speech = AsyncMock(side_effect=mock_internal_error)
        
        request_data = {
            "text": "Test text",
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 500
        response_data = response.json()
        assert response_data["error_code"] == "INTERNAL_ERROR"
        assert "Unexpected error" in response_data["error_message"]

    def test_generate_speech_exception_handling(self, client, mock_tts_service):
        """Test TTS generation with unexpected exception."""
        # Mock service to raise exception
        mock_tts_service.generate_speech = AsyncMock(side_effect=Exception("Unexpected exception"))
        
        request_data = {
            "text": "Test text",
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 500
        response_data = response.json()
        assert response_data["error_code"] == "INTERNAL_ERROR"
        assert "Internal server error" in response_data["error_message"]

    def test_generate_speech_invalid_json(self, client):
        """Test TTS generation with invalid JSON."""
        response = client.post("/api/tts", data="invalid json")
        
        assert response.status_code == 422  # Unprocessable Entity

    def test_generate_speech_missing_required_fields(self, client):
        """Test TTS generation with missing required fields."""
        # Missing text field
        request_data = {
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        assert response.status_code == 422
        
        # Missing message_id field
        request_data = {
            "text": "Test text"
        }
        
        response = client.post("/api/tts", json=request_data)
        assert response.status_code == 422

    def test_generate_speech_invalid_field_types(self, client):
        """Test TTS generation with invalid field types."""
        request_data = {
            "text": 123,  # Should be string
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        assert response.status_code == 422

    def test_generate_speech_text_length_validation(self, client):
        """Test TTS generation with text length validation."""
        # Text too short (empty)
        request_data = {
            "text": "",
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        assert response.status_code == 422
        
        # Text too long (exceeds max_length in model)
        request_data = {
            "text": "x" * 501,
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        assert response.status_code == 422

    def test_generate_speech_with_correlation_id(self, client):
        """Test TTS generation with correlation ID header."""
        request_data = {
            "text": "Test text",
            "message_id": "test-123"
        }
        
        headers = {"X-Correlation-ID": "test-correlation-456"}
        response = client.post("/api/tts", json=request_data, headers=headers)
        
        assert response.status_code == 200
        assert response.headers["x-correlation-id"] == "test-correlation-456"

    def test_generate_speech_cache_headers(self, client):
        """Test TTS generation response includes appropriate cache headers."""
        request_data = {
            "text": "Test text",
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 200
        assert "cache-control" in response.headers
        assert "private" in response.headers["cache-control"]
        assert "max-age=3600" in response.headers["cache-control"]

    def test_generate_speech_content_disposition(self, client):
        """Test TTS generation response includes content disposition header."""
        request_data = {
            "text": "Test text",
            "message_id": "test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 200
        assert "content-disposition" in response.headers
        assert "attachment" in response.headers["content-disposition"]
        assert "tts_test-123.mp3" in response.headers["content-disposition"]

    def test_tts_health_check_success(self, client):
        """Test successful TTS health check."""
        response = client.get("/api/tts/health")
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["healthy"] is True
        assert response_data["response_time"] > 0
        assert response_data["message"] == "TTS service is operational"
        assert response_data["audio_size"] == 1024

    def test_tts_health_check_failure(self, client, mock_tts_service):
        """Test TTS health check failure."""
        # Mock health check to return failure
        async def mock_failed_health_check():
            return {
                "healthy": False,
                "response_time": 1.0,
                "error": "TTS service is down",
                "error_code": "SERVICE_UNAVAILABLE"
            }
        
        mock_tts_service.health_check = AsyncMock(side_effect=mock_failed_health_check)
        
        response = client.get("/api/tts/health")
        
        assert response.status_code == 200  # Health endpoint always returns 200
        response_data = response.json()
        assert response_data["healthy"] is False
        assert response_data["error"] == "TTS service is down"
        assert response_data["error_code"] == "SERVICE_UNAVAILABLE"

    def test_tts_health_check_exception(self, client, mock_tts_service):
        """Test TTS health check with exception."""
        # Mock health check to raise exception
        mock_tts_service.health_check = AsyncMock(side_effect=Exception("Health check error"))
        
        response = client.get("/api/tts/health")
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["healthy"] is False
        assert "Health check failed" in response_data["error"]
        assert "Health check error" in response_data["error"]

    def test_tts_service_info_success(self, client):
        """Test successful TTS service info retrieval."""
        response = client.get("/api/tts/info")
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["service_name"] == "TTS Service"
        assert response_data["max_text_length"] == 500
        assert "google_tts_client" in response_data
        assert "features" in response_data
        assert len(response_data["features"]) > 0

    def test_tts_service_info_error(self, client, mock_tts_service):
        """Test TTS service info with error."""
        # Mock service info to raise exception
        mock_tts_service.get_service_info.side_effect = Exception("Service info error")
        
        response = client.get("/api/tts/info")
        
        assert response.status_code == 200
        response_data = response.json()
        assert "error" in response_data
        assert "Service info error" in response_data["error"]

    def test_generate_speech_with_german_text(self, client):
        """Test TTS generation with German text containing special characters."""
        request_data = {
            "text": "Äpfel, Öl und Übergänge sind schön in München.",
            "message_id": "german-test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "audio/mpeg"

    def test_generate_speech_with_long_valid_text(self, client):
        """Test TTS generation with long but valid text."""
        # Create text that's close to but under the limit
        long_text = "Das ist ein langer deutscher Text. " * 12  # Should be under 500 chars
        
        request_data = {
            "text": long_text,
            "message_id": "long-text-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "audio/mpeg"

    def test_generate_speech_custom_voice_config(self, client):
        """Test TTS generation with custom voice configuration."""
        request_data = {
            "text": "Test with custom voice",
            "message_id": "custom-voice-123",
            "voice_config": "de-DE-Custom-Voice"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "audio/mpeg"

    def test_generate_speech_response_headers_completeness(self, client):
        """Test that all expected response headers are present."""
        request_data = {
            "text": "Test for headers",
            "message_id": "headers-test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 200
        
        # Check all expected headers are present
        expected_headers = [
            "content-type",
            "x-duration-seconds",
            "x-processing-time",
            "x-original-text-length",
            "x-sanitized-text-length",
            "content-disposition",
            "cache-control"
        ]
        
        for header in expected_headers:
            assert header in response.headers, f"Missing header: {header}"

    def test_api_cors_headers(self, client):
        """Test that CORS headers are properly set."""
        # Test preflight request
        response = client.options("/api/tts")
        
        # Should have CORS headers (configured in main.py)
        assert response.status_code in [200, 405]  # Some test clients handle OPTIONS differently

    def test_api_error_response_format(self, client, mock_tts_service):
        """Test that error responses follow the correct format."""
        # Mock service to return error
        async def mock_error_response(text: str, request_id: str = None):
            return {
                "success": False,
                "error_message": "Test error message",
                "error_code": "TEST_ERROR",
                "processing_time": 0.1
            }
        
        mock_tts_service.generate_speech = AsyncMock(side_effect=mock_error_response)
        
        request_data = {
            "text": "Test text",
            "message_id": "error-test-123"
        }
        
        response = client.post("/api/tts", json=request_data)
        
        assert response.status_code == 500  # TEST_ERROR maps to 500
        response_data = response.json()
        
        # Check error response structure
        assert "error_message" in response_data
        assert "error_code" in response_data
        assert "processing_time" in response_data
        assert response_data["error_message"] == "Test error message"
        assert response_data["error_code"] == "TEST_ERROR"