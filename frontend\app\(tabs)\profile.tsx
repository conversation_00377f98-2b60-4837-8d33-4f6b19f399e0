import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { User, Award, TrendingUp, Target } from 'lucide-react-native';

export default function ProfileScreen() {
  const stats = [
    { label: 'Korrekturen', value: '127', icon: Award },
    { label: 'Verbesserung', value: '89%', icon: TrendingUp },
    { label: 'Lernziele', value: '12/15', icon: Target },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.gradient}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Mein Profil</Text>
          </View>

          {/* Profile Card */}
          <View style={styles.profileCard}>
            <View style={styles.avatarContainer}>
              <LinearGradient
                colors={['#2563eb', '#1d4ed8']}
                style={styles.avatar}
              >
                <User size={40} color="#fff" />
              </LinearGradient>
            </View>
            <Text style={styles.userName}>Deutsch Lerner</Text>
            <Text style={styles.userLevel}>Fortgeschritten</Text>
          </View>

          {/* Stats */}
          <View style={styles.statsContainer}>
            <Text style={styles.sectionTitle}>Deine Fortschritte</Text>
            {stats.map((stat, index) => (
              <View key={index} style={styles.statItem}>
                <View style={styles.statIcon}>
                  <stat.icon size={24} color="#2563eb" />
                </View>
                <View style={styles.statContent}>
                  <Text style={styles.statLabel}>{stat.label}</Text>
                  <Text style={styles.statValue}>{stat.value}</Text>
                </View>
              </View>
            ))}
          </View>

          {/* Learning Goals */}
          <View style={styles.goalsContainer}>
            <Text style={styles.sectionTitle}>Lernziele</Text>
            <View style={styles.goalItem}>
              <View style={styles.goalProgress}>
                <View style={[styles.progressBar, { width: '80%' }]} />
              </View>
              <Text style={styles.goalText}>Verbesserung der Grammatik</Text>
            </View>
            <View style={styles.goalItem}>
              <View style={styles.goalProgress}>
                <View style={[styles.progressBar, { width: '60%' }]} />
              </View>
              <Text style={styles.goalText}>Erweiterung des Wortschatzes</Text>
            </View>
            <View style={styles.goalItem}>
              <View style={styles.goalProgress}>
                <View style={[styles.progressBar, { width: '90%' }]} />
              </View>
              <Text style={styles.goalText}>Aussprache verbessern</Text>
            </View>
          </View>

          {/* Recent Activity */}
          <View style={styles.activityContainer}>
            <Text style={styles.sectionTitle}>Letzte Aktivitäten</Text>
            <View style={styles.activityItem}>
              <Text style={styles.activityTime}>Heute, 14:30</Text>
              <Text style={styles.activityText}>Grammatikkorrektur: "Der" vs "Die"</Text>
            </View>
            <View style={styles.activityItem}>
              <Text style={styles.activityTime}>Gestern, 16:45</Text>
              <Text style={styles.activityText}>Sprachaufnahme korrigiert</Text>
            </View>
            <View style={styles.activityItem}>
              <Text style={styles.activityTime}>Gestern, 09:15</Text>
              <Text style={styles.activityText}>Wortschatz-Übung abgeschlossen</Text>
            </View>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    backgroundColor: '#ffffff',
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#2563eb',
  },
  profileCard: {
    alignItems: 'center',
    padding: 30,
    margin: 20,
    backgroundColor: '#ffffff',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  userLevel: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#2563eb',
  },
  statsContainer: {
    margin: 20,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  statIcon: {
    marginRight: 16,
  },
  statContent: {
    flex: 1,
  },
  statLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  goalsContainer: {
    margin: 20,
    marginTop: 0,
  },
  goalItem: {
    marginBottom: 16,
  },
  goalProgress: {
    height: 8,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#2563eb',
    borderRadius: 4,
  },
  goalText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
  activityContainer: {
    margin: 20,
    marginTop: 0,
    marginBottom: 40,
  },
  activityItem: {
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  activityTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#2563eb',
    marginBottom: 4,
  },
  activityText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6b7280',
  },
});