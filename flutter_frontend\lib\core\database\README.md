# Database Setup Instructions

This directory contains the database schema and migration files for the DeutschKorrekt app's Supabase integration.

## Setup Steps

### 1. Access Supabase Dashboard
1. Go to https://eaemndginhddncydpaix.supabase.co
2. Navigate to the SQL Editor

### 2. Run Migration Files
Execute the following SQL files in order:

#### Step 1: Create Users Table
```sql
-- Run the contents of 001_create_users_table.sql
```

#### Step 2: Create Sessions Table
```sql
-- Run the contents of 002_create_sessions_table.sql
```

#### Step 3: Set up Security Policies
```sql
-- Run the contents of 003_create_security_policies.sql
```

### 3. Verify Setup
After running the migrations, verify the setup by:

1. Checking that both tables exist in the Table Editor
2. Confirming that Row Level Security is enabled
3. Testing that the policies work correctly

## Table Schemas

### Users Table
- `email` (TEXT, PRIMARY KEY): User's email address
- `plan` (TEXT, DEFAULT 'Trial'): User's subscription plan
- `date_joined` (TIMESTAMPTZ): When user joined
- `date_plan` (TIMESTAMPTZ): When current plan started
- `max_credits` (INTEGER, DEFAULT 20): Maximum credits for plan
- `current_credits` (INTEGER, DEFAULT 20): Current available credits
- `created_at` (TIMESTAMPTZ): Record creation timestamp
- `updated_at` (TIMESTAMPTZ): Record update timestamp

### Sessions Table
- `session_id` (SERIAL, PRIMARY KEY): Unique session identifier
- `email` (TEXT, FOREIGN KEY): Reference to user
- `message` (TEXT): User's input message
- `response` (TEXT): System's response
- `datetime` (TIMESTAMPTZ): When session occurred
- `created_at` (TIMESTAMPTZ): Record creation timestamp

## Security Policies

Both tables have Row Level Security (RLS) enabled:

- **Users**: Users can only access their own profile data
- **Sessions**: Users can only view and create their own sessions
- **Immutability**: Sessions cannot be updated or deleted (audit trail)

## Environment Variables

Make sure to set the following environment variables:

- `SUPABASE_ANON_KEY_DEV`: Development anonymous key
- `SUPABASE_ANON_KEY_PROD`: Production anonymous key