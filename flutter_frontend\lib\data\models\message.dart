import 'package:flutter/foundation.dart';
import 'correction_result.dart';
import 'translation_result.dart';
import '../../core/utils/sentence_extractor.dart';

/// Sentinel value for copyWith method to distinguish between null and not provided
const Object _sentinel = Object();

/// Message model representing chat messages in the conversation
class Message {
  final String id;
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final bool isAudio;
  final bool isStreaming;
  final bool isProcessing;
  final CorrectionResult? correctionResult;
  final TranslationResult? translationResult;
  final String? extractedSentence;
  
  const Message({
    required this.id,
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.isAudio = false,
    this.isStreaming = false,
    this.isProcessing = false,
    this.correctionResult,
    this.translationResult,
    this.extractedSentence,
  });
  
  /// Create a copy of this message with updated properties
  Message copyWith({
    String? id,
    String? text,
    bool? isUser,
    DateTime? timestamp,
    bool? isAudio,
    bool? isStreaming,
    bool? isProcessing,
    CorrectionResult? correctionResult,
    TranslationResult? translationResult,
    Object? extractedSentence = _sentinel,
  }) {
    return Message(
      id: id ?? this.id,
      text: text ?? this.text,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp ?? this.timestamp,
      isAudio: isAudio ?? this.isAudio,
      isStreaming: isStreaming ?? this.isStreaming,
      isProcessing: isProcessing ?? this.isProcessing,
      correctionResult: correctionResult ?? this.correctionResult,
      translationResult: translationResult ?? this.translationResult,
      extractedSentence: extractedSentence == _sentinel 
          ? this.extractedSentence 
          : extractedSentence as String?,
    );
  }
  
  /// Create a user message
  factory Message.user({
    required String id,
    required String text,
    bool isAudio = false,
    bool isStreaming = false,
  }) {
    return Message(
      id: id,
      text: text,
      isUser: true,
      timestamp: DateTime.now(),
      isAudio: isAudio,
      isStreaming: isStreaming,
    );
  }
  
  /// Create an AI message
  factory Message.ai({
    required String id,
    required String text,
    bool isProcessing = false,
    CorrectionResult? correctionResult,
    TranslationResult? translationResult,
  }) {
    // First, extract sentence from curly braces for TTS functionality
    final extractedSentence = SentenceExtractor.extractFirstSentenceFromBraces(text);

    // Then, format the text for display by removing curly braces
    final displayText = SentenceExtractor.formatForDisplay(text);

    // Log sentence extraction for debugging (only in debug mode)
    if (kDebugMode) {
      debugPrint('TTS: Processing AI message $id');
      debugPrint('TTS: Original text: "${text.length > 200 ? text.substring(0, 200) + "..." : text}"');
      debugPrint('TTS: Display text: "${displayText.length > 200 ? displayText.substring(0, 200) + "..." : displayText}"');
      debugPrint('TTS: Contains { : ${text.contains("{")}');
      debugPrint('TTS: Contains } : ${text.contains("}")}');
      if (extractedSentence != null) {
        debugPrint('TTS: ✅ Extracted sentence: "$extractedSentence"');
      } else {
        debugPrint('TTS: ❌ No sentence found in curly braces');
      }
    }
    
    return Message(
      id: id,
      text: displayText, // Store the clean text without braces
      isUser: false,
      timestamp: DateTime.now(),
      isProcessing: isProcessing,
      correctionResult: correctionResult,
      translationResult: translationResult,
      extractedSentence: extractedSentence,
    );
  }
  
  @override
  String toString() {
    return 'Message(id: $id, text: $text, isUser: $isUser, isStreaming: $isStreaming)';
  }
}