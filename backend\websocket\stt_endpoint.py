"""
WebSocket endpoint for speech-to-text streaming.
"""

import logging
import asyncio
import json
import time
from typing import Dict, Optional

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, Depends
from starlette.websockets import WebSocketState

from services.stt_service import DeepgramService
from services.session_service import SessionService
from services.language_service import LanguageService
from websocket.manager import WebSocketManager
from config.settings import settings
from models.data_models import WebSocketMessage

logger = logging.getLogger(__name__)



# Create router
router = APIRouter()

# Create services
api_key = settings.DEEPGRAM_API_KEY or settings.deepgram_api_key
if not api_key:
    raise ValueError("Deepgram API key not found. Please set DEEPGRAM_API_KEY in environment variables or .env file")

stt_service = DeepgramService(api_key=api_key, max_duration=settings.max_recording_duration, enable_multilingual=False)
websocket_manager = WebSocketManager()
session_service = SessionService()
language_service = LanguageService()

async def process_text_with_language_service(client_id: str, text: str) -> None:
    """
    Process transcribed text with the language service for corrections/translations.
    
    Args:
        client_id: The client ID to send results to
        text: The transcribed text to process
    """
    try:
        # Send processing status
        await websocket_manager.send_message(
            client_id,
            WebSocketMessage(
                message_type="processing",
                data={"message": "Processing text with language AI..."}
            )
        )
        
        # Process the text with the language service
        result = await language_service.process_text(text)
        
        # Check if there was an error
        if "error" in result:
            await websocket_manager.send_error(
                client_id,
                f"Error processing text: {result['error']}",
                "LANGUAGE_PROCESSING_ERROR"
            )
            return
        
        # Send the raw Groq response - no parsing, no differentiation
        await websocket_manager.send_message(
            client_id,
            WebSocketMessage(
                message_type="groq_response",
                data={
                    "original_text": result["original_text"],
                    "response_text": result["processed_content"]["response_text"],
                    "processing_time": result["processing_time"]
                }
            )
        )
        
        logger.info(f"Processed text for client {client_id} with {result['agent_used']}")
        
    except Exception as e:
        logger.error(f"Error processing text with language service: {str(e)}", exc_info=True)
        await websocket_manager.send_error(
            client_id,
            f"Error processing text: {str(e)}",
            "LANGUAGE_PROCESSING_ERROR"
        )

@router.websocket("/stt")
async def stt_endpoint(websocket: WebSocket):
    """
    WebSocket endpoint for speech-to-text streaming.

    Receives audio data from the client, streams it to Deepgram for transcription,
    and sends the transcribed text back to the client in real-time.
    """
    client_id = None
    session_id = None
    
    try:
        # Generate client ID and register the connection with the WebSocket manager
        client_id = f"client_{int(time.time() * 1000)}"
        await websocket_manager.connect(websocket, client_id)
        logger.info(f"Client connected: {client_id}")
        
        # Send welcome message
        await websocket_manager.send_message(
            client_id,
            WebSocketMessage(
                message_type="info",
                data={"message": "Connected to Deutschkorrekt STT service"}
            )
        )
        
        # Start the STT streaming session
        try:
            session_id = await stt_service.create_session(websocket, client_id)
            logger.info(f"Started STT session: {session_id}")
            
            # Send session started message
            await websocket_manager.send_message(
                client_id,
                WebSocketMessage(
                    message_type="session_started",
                    data={"session_id": session_id}
                )
            )
            
            # Process incoming audio data
            start_time = time.time()
            while True:
                # Check if we've exceeded the maximum recording duration
                if time.time() - start_time > settings.max_recording_duration:
                    logger.info(f"Session {session_id} reached maximum duration of {settings.max_recording_duration} seconds - auto-processing")

                    # Use manual stop to get the current transcript and process with Groq
                    # This function handles the complete flow including sending Groq response
                    complete_text = await stt_service.manual_stop_session(session_id)
                    logger.info(f"✅ Timeout processing completed, transcript length: {len(complete_text)}")

                    break
                
                # Receive data from client (audio or text commands)
                try:
                    # Wait for any message (text or bytes)
                    message = await asyncio.wait_for(websocket.receive(), timeout=1.0)

                    # Check if it's a text message (command)
                    if 'text' in message:
                        text_command = message['text']
                        logger.info(f"📝 Received text command: {text_command}")

                        if text_command == "manual_stop":
                            logger.info(f"🛑 Manual stop requested by client {client_id}")
                            complete_text = await stt_service.manual_stop_session(session_id)
                            break
                        else:
                            logger.warning(f"❓ Unknown text command: {text_command}")

                    # Check if it's binary data (audio)
                    elif 'bytes' in message:
                        audio_data = message['bytes']
                        # Process the audio chunk
                        await stt_service.process_audio_chunk(session_id, audio_data)

                    else:
                        logger.warning(f"❓ Unknown message type: {message}")

                except asyncio.TimeoutError:
                    # No data received within timeout, check if client is still connected
                    if websocket.client_state == WebSocketState.DISCONNECTED:
                        logger.info(f"Client {client_id} disconnected")
                        break
                    continue
                    
                except WebSocketDisconnect:
                    logger.info(f"Client {client_id} disconnected")
                    break
                
        except Exception as e:
            logger.error(f"Error in STT session: {str(e)}")
            await websocket_manager.send_error(
                client_id,
                f"STT service error: {str(e)}",
                "STT_SERVICE_ERROR"
            )
            
    except WebSocketDisconnect:
        logger.info(f"Client disconnected during connection setup")
        
    except Exception as e:
        logger.error(f"Unhandled error in WebSocket connection: {str(e)}", exc_info=True)
        
    finally:
        # Clean up
        if client_id:
            websocket_manager.disconnect(client_id)
            
        if session_id:
            try:
                # Finalize the transcription (this will trigger Groq processing automatically)
                complete_text = await stt_service.finalize_transcription(session_id)
                logger.info(f"Finalized transcription for session {session_id}: {len(complete_text)} chars")
                
                # Send final transcript to client if we have text
                if complete_text.strip() and websocket_manager.is_connected(client_id):
                    await websocket_manager.send_message(
                        client_id,
                        WebSocketMessage(
                            message_type="final_transcript",
                            data={"text": complete_text, "complete_text": complete_text}
                        )
                    )
            except Exception as e:
                logger.error(f"Error finalizing transcription: {str(e)}")
                
        logger.info(f"WebSocket connection closed: {client_id}")
        
@router.websocket("/stt/stop/{session_id}")
async def stop_stt_session(websocket: WebSocket, session_id: str):
    """
    WebSocket endpoint to stop an ongoing STT session.
    
    Args:
        websocket: The WebSocket connection
        session_id: The session ID to stop
    """
    client_id = None
    
    try:
        # Generate client ID and register the connection with the WebSocket manager
        client_id = f"client_stop_{int(time.time() * 1000)}"
        await websocket_manager.connect(websocket, client_id)
        
        # Finalize the transcription
        complete_text = await stt_service.finalize_transcription(session_id)
        
        # Send the complete text back to the client
        await websocket_manager.send_message(
            client_id,
            WebSocketMessage(
                message_type="final_transcript",
                data={"text": complete_text}
            )
        )
        
        # Process text with language service if we have text to process
        if complete_text.strip():
            await process_text_with_language_service(client_id, complete_text)
        
        logger.info(f"Stopped STT session: {session_id}")
        
    except Exception as e:
        logger.error(f"Error stopping STT session: {str(e)}")
        if client_id and websocket_manager.is_connected(client_id):
            await websocket_manager.send_error(
                client_id,
                f"Error stopping STT session: {str(e)}",
                "STOP_SESSION_ERROR"
            )
        else:
            await websocket.send_json({
                "type": "error",
                "data": {
                    "message": f"Error stopping STT session: {str(e)}",
                    "code": "STOP_SESSION_ERROR"
                }
            })
        
    finally:
        if client_id:
            websocket_manager.disconnect(client_id)
        await websocket.close()