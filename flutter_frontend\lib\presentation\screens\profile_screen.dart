import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/profile_provider.dart';
import '../widgets/common/gradient_background.dart';
import '../../core/constants/colors.dart';
import '../../core/constants/gradients.dart';
import '../../core/constants/text_styles.dart';
import '../../data/models/profile_data.dart';

/// Profile screen showing user statistics and learning progress matching Expo version
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    
    // Initialize profile provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProfileProvider>().initialize();
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Header
              _buildHeader(context),
              
              // Profile content
              Expanded(
                child: Consumer<ProfileProvider>(
                  builder: (context, profileProvider, child) {
                    if (!profileProvider.isInitialized) {
                      return _buildLoadingState();
                    }
                    
                    return SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          // User info card
                          _buildUserInfoCard(profileProvider),
                          
                          const SizedBox(height: 16),
                          
                          // Statistics grid
                          _buildStatisticsGrid(profileProvider),
                          
                          const SizedBox(height: 16),
                          
                          // Learning goals
                          _buildLearningGoals(profileProvider),
                          
                          const SizedBox(height: 16),
                          
                          // Recent activity
                          _buildRecentActivity(profileProvider),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// Build header with close button
  Widget _buildHeader(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppGradients.headerGradient,
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(
                Icons.close,
                color: AppColors.lightText,
                size: 24,
              ),
              tooltip: 'Close',
            ),
            
            Expanded(
              child: Text(
                'Profile',
                style: AppTextStyles.headerTitle,
                textAlign: TextAlign.center,
              ),
            ),
            
            // Settings button for profile
            IconButton(
              onPressed: () => _showProfileSettings(context),
              icon: const Icon(
                Icons.edit,
                color: AppColors.lightText,
                size: 24,
              ),
              tooltip: 'Edit Profile',
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build loading state
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.infoBlue),
          ),
          SizedBox(height: 16),
          Text(
            'Loading profile...',
            style: AppTextStyles.messageText,
          ),
        ],
      ),
    );
  }
  
  /// Build user info card
  Widget _buildUserInfoCard(ProfileProvider profileProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppGradients.correctionContainerGradient,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.slate600.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Avatar
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: AppGradients.agentBadgeGradient,
              border: Border.all(
                color: AppColors.white.withOpacity(0.3),
                width: 2,
              ),
            ),
            child: profileProvider.avatarUrl.isNotEmpty
                ? ClipOval(
                    child: Image.network(
                      profileProvider.avatarUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(),
                    ),
                  )
                : _buildDefaultAvatar(),
          ),
          
          const SizedBox(height: 16),
          
          // User name
          Text(
            profileProvider.userName,
            style: AppTextStyles.modalTitle,
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          // User level with progress
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              gradient: AppGradients.agentBadgeGradient,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              profileProvider.userLevel,
              style: AppTextStyles.agentBadge,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Level progress bar
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Level Progress',
                    style: AppTextStyles.captionText,
                  ),
                  Text(
                    '${(profileProvider.getLevelProgress() * 100).toInt()}%',
                    style: AppTextStyles.captionText,
                  ),
                ],
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: profileProvider.getLevelProgress(),
                backgroundColor: AppColors.slate700,
                valueColor: const AlwaysStoppedAnimation<Color>(AppColors.successGreen),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Join date
          Text(
            'Member since ${_formatDate(profileProvider.joinDate)}',
            style: AppTextStyles.captionText,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  /// Build default avatar
  Widget _buildDefaultAvatar() {
    return const Icon(
      Icons.person,
      size: 40,
      color: AppColors.lightText,
    );
  }
  
  /// Build statistics grid
  Widget _buildStatisticsGrid(ProfileProvider profileProvider) {
    final stats = profileProvider.stats;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.slate800.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.slate600.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Statistics',
            style: AppTextStyles.modalSubtitle,
          ),
          
          const SizedBox(height: 16),
          
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: stats.length,
            itemBuilder: (context, index) {
              final stat = stats[index];
              return _buildStatCard(stat);
            },
          ),
        ],
      ),
    );
  }
  
  /// Build individual stat card
  Widget _buildStatCard(StatItem stat) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: AppGradients.correctionContainerGradient,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.slate600.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (stat.icon != null)
            Text(
              stat.icon!,
              style: const TextStyle(fontSize: 24),
            ),
          
          const SizedBox(height: 4),
          
          Text(
            stat.value,
            style: AppTextStyles.modalTitle.copyWith(fontSize: 18),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 2),
          
          Text(
            stat.label,
            style: AppTextStyles.captionText,
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
  
  /// Build learning goals section
  Widget _buildLearningGoals(ProfileProvider profileProvider) {
    final goals = profileProvider.learningGoals;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.slate800.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.slate600.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Learning Goals',
                style: AppTextStyles.modalSubtitle,
              ),
              Text(
                '${goals.where((g) => g.isCompleted).length}/${goals.length} completed',
                style: AppTextStyles.captionText,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          ...goals.map((goal) => _buildGoalCard(goal)),
        ],
      ),
    );
  }
  
  /// Build individual goal card
  Widget _buildGoalCard(LearningGoal goal) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: goal.isCompleted 
            ? LinearGradient(
                colors: [
                  AppColors.successGreen.withOpacity(0.2),
                  AppColors.successGreen.withOpacity(0.1),
                ],
              )
            : AppGradients.correctionContainerGradient,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: goal.isCompleted 
              ? AppColors.successGreen.withOpacity(0.3)
              : AppColors.slate600.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                goal.isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
                color: goal.isCompleted ? AppColors.successGreen : AppColors.slate600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  goal.title,
                  style: AppTextStyles.messageText.copyWith(
                    fontWeight: FontWeight.w600,
                    decoration: goal.isCompleted ? TextDecoration.lineThrough : null,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 4),
          
          Text(
            goal.description,
            style: AppTextStyles.captionText,
          ),
          
          const SizedBox(height: 8),
          
          // Progress bar
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: goal.progressPercentage,
                  backgroundColor: AppColors.slate700,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    goal.isCompleted ? AppColors.successGreen : AppColors.infoBlue,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${(goal.progressPercentage * 100).toInt()}%',
                style: AppTextStyles.captionText,
              ),
            ],
          ),
          
          const SizedBox(height: 4),
          
          Text(
            '${goal.daysRemaining} days remaining',
            style: AppTextStyles.captionText.copyWith(
              color: goal.daysRemaining < 7 ? AppColors.warningYellow : AppColors.lightText,
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build recent activity section
  Widget _buildRecentActivity(ProfileProvider profileProvider) {
    final activities = profileProvider.recentActivity.take(5).toList();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.slate800.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.slate600.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Activity',
            style: AppTextStyles.modalSubtitle,
          ),
          
          const SizedBox(height: 16),
          
          if (activities.isEmpty)
            Text(
              'No recent activity',
              style: AppTextStyles.captionText,
            )
          else
            ...activities.map((activity) => _buildActivityItem(activity)),
        ],
      ),
    );
  }
  
  /// Build individual activity item
  Widget _buildActivityItem(ActivityItem activity) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.slate700.withOpacity(0.3),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Icon(
            _getActivityIcon(activity.type),
            color: AppColors.infoBlue,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.description,
                  style: AppTextStyles.captionText.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  activity.timeAgo,
                  style: AppTextStyles.captionText.copyWith(
                    fontSize: 10,
                    color: AppColors.lightText.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// Get icon for activity type
  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'Grammar correction':
        return Icons.spellcheck;
      case 'Practice session':
        return Icons.mic;
      case 'Goal completed':
        return Icons.flag;
      case 'Level changed':
        return Icons.trending_up;
      case 'Profile updated':
        return Icons.person;
      default:
        return Icons.info;
    }
  }
  
  /// Format date for display
  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }
  
  /// Show profile settings dialog
  void _showProfileSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => _ProfileSettingsDialog(),
    );
  }
}

/// Profile settings dialog
class _ProfileSettingsDialog extends StatefulWidget {
  @override
  State<_ProfileSettingsDialog> createState() => _ProfileSettingsDialogState();
}

class _ProfileSettingsDialogState extends State<_ProfileSettingsDialog> {
  final TextEditingController _nameController = TextEditingController();
  String _selectedLevel = 'Beginner';
  
  @override
  void initState() {
    super.initState();
    
    final profileProvider = context.read<ProfileProvider>();
    _nameController.text = profileProvider.userName;
    _selectedLevel = profileProvider.userLevel;
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppColors.slate800,
      title: Text(
        'Edit Profile',
        style: AppTextStyles.modalTitle,
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Name field
          TextField(
            controller: _nameController,
            style: AppTextStyles.messageText,
            decoration: InputDecoration(
              labelText: 'Name',
              labelStyle: AppTextStyles.captionText,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Level dropdown
          DropdownButtonFormField<String>(
            value: _selectedLevel,
            style: AppTextStyles.messageText,
            dropdownColor: AppColors.slate700,
            decoration: InputDecoration(
              labelText: 'Level',
              labelStyle: AppTextStyles.captionText,
            ),
            items: context.read<ProfileProvider>().getAvailableLevels()
                .map((level) => DropdownMenuItem(
                  value: level,
                  child: Text(level),
                ))
                .toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedLevel = value;
                });
              }
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: AppTextStyles.smallButtonText.copyWith(
              color: AppColors.lightText,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () => _saveProfile(context),
          child: Text(
            'Save',
            style: AppTextStyles.smallButtonText,
          ),
        ),
      ],
    );
  }
  
  void _saveProfile(BuildContext context) {
    final profileProvider = context.read<ProfileProvider>();
    
    profileProvider.updateUserName(_nameController.text);
    profileProvider.updateUserLevel(_selectedLevel);
    
    Navigator.pop(context);
  }
}