import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/enhanced_profile_provider.dart';
import '../../../data/services/session_tracker.dart';
import 'profile_header.dart';
import 'profile_stats.dart';
import 'profile_credits.dart';
import 'profile_actions.dart';

/// Profile popup widget that displays user information and statistics
class ProfilePopup extends StatefulWidget {
  const ProfilePopup({super.key});

  @override
  State<ProfilePopup> createState() => _ProfilePopupState();
}

class _ProfilePopupState extends State<ProfilePopup> {
  final SessionTracker _sessionTracker = SessionTracker();
  SessionStats? _sessionStats;
  bool _isLoadingStats = false;

  @override
  void initState() {
    super.initState();
    _loadSessionStats();
  }

  Future<void> _loadSessionStats() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final userEmail = authProvider.currentUser?.email;
    
    if (userEmail == null) return;

    setState(() {
      _isLoadingStats = true;
    });

    try {
      final stats = await _sessionTracker.getUserSessionStats(userEmail);
      if (mounted) {
        setState(() {
          _sessionStats = stats;
        });
      }
    } catch (e) {
      print('Failed to load session stats: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingStats = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 400,
          maxHeight: 600,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with close button
            _buildHeader(context),
            
            // Scrollable content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile header with avatar and basic info
                    const ProfileHeader(),
                    
                    const SizedBox(height: 24),
                    
                    // Credits information
                    const ProfileCredits(),
                    
                    const SizedBox(height: 24),
                    
                    // Session statistics
                    ProfileStats(
                      sessionStats: _sessionStats,
                      isLoading: _isLoadingStats,
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Action buttons
                    const ProfileActions(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.person,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          const Text(
            'Profile',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
              fontFamily: 'Inter',
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}