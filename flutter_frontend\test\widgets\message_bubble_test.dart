import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:deutschkorrekt_flutter/presentation/widgets/chat/message_bubble.dart';
import 'package:deutschkorrekt_flutter/presentation/widgets/chat/audio_icon_widget.dart';
import 'package:deutschkorrekt_flutter/data/models/message.dart';
import 'package:deutschkorrekt_flutter/data/services/tts_audio_service.dart';

import 'message_bubble_test.mocks.dart';

// Generate mocks for testing
@GenerateMocks([TTSAudioService])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('MessageBubble Widget Tests', () {
    late MockTTSAudioService mockTtsService;
    
    setUp(() {
      mockTtsService = MockTTSAudioService();
    });
    
    group('Basic Rendering', () {
      testWidgets('should render user message without audio icon', (WidgetTester tester) async {
        final message = Message(
          id: 'test_user_message',
          text: 'Hello, this is a user message',
          isUser: true,
          timestamp: DateTime.now(),
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        expect(find.text('Hello, this is a user message'), findsOneWidget);
        expect(find.byType(AudioIconWidget), findsNothing);
      });
      
      testWidgets('should render AI message without audio icon when no extracted sentence', (WidgetTester tester) async {
        final message = Message(
          id: 'test_ai_message',
          text: 'Hello, this is an AI message',
          isUser: false,
          timestamp: DateTime.now(),
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        expect(find.text('Hello, this is an AI message'), findsOneWidget);
        expect(find.byType(AudioIconWidget), findsNothing);
      });
      
      testWidgets('should render AI message with audio icon when extracted sentence exists', (WidgetTester tester) async {
        final message = Message(
          id: 'test_ai_message_with_audio',
          text: 'Hello, this is an AI message with audio',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: 'Hello, this is an AI message!',
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        expect(find.text('Hello, this is an AI message with audio'), findsOneWidget);
        expect(find.byType(AudioIconWidget), findsOneWidget);
      });
      
      testWidgets('should not render audio icon for empty extracted sentence', (WidgetTester tester) async {
        final message = Message(
          id: 'test_ai_message_empty_sentence',
          text: 'Hello, this is an AI message',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: '',
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        expect(find.text('Hello, this is an AI message'), findsOneWidget);
        expect(find.byType(AudioIconWidget), findsNothing);
      });
    });
    
    group('Layout and Positioning', () {
      testWidgets('should position audio icon in top-right corner', (WidgetTester tester) async {
        final message = Message(
          id: 'test_positioning',
          text: 'Test message for audio icon positioning',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: 'Test sentence for audio!',
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        // Find the audio icon widget
        final audioIconFinder = find.byType(AudioIconWidget);
        expect(audioIconFinder, findsOneWidget);
        
        // Find the positioned widget that contains the audio icon
        final positionedFinder = find.ancestor(
          of: audioIconFinder,
          matching: find.byType(Positioned),
        );
        expect(positionedFinder, findsOneWidget);
        
        // Verify positioning
        final positioned = tester.widget<Positioned>(positionedFinder);
        expect(positioned.top, equals(8));
        expect(positioned.right, isNotNull);
      });
      
      testWidgets('should adjust text layout to accommodate audio icon', (WidgetTester tester) async {
        final message = Message(
          id: 'test_layout_adjustment',
          text: 'This is a longer message that should have its layout adjusted to accommodate the audio icon in the top-right corner',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: 'This is a longer message!',
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        // Verify that both text and audio icon are present
        expect(find.textContaining('This is a longer message'), findsOneWidget);
        expect(find.byType(AudioIconWidget), findsOneWidget);
        
        // Verify that the message bubble has appropriate padding
        final containerFinder = find.descendant(
          of: find.byType(MessageBubble),
          matching: find.byType(Container),
        );
        expect(containerFinder, findsWidgets);
      });
      
      testWidgets('should handle responsive layout on different screen sizes', (WidgetTester tester) async {
        final message = Message(
          id: 'test_responsive',
          text: 'Responsive layout test message',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: 'Responsive test!',
        );
        
        // Test with small screen size
        await tester.binding.setSurfaceSize(const Size(350, 600));
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        expect(find.byType(AudioIconWidget), findsOneWidget);
        
        // Test with large screen size
        await tester.binding.setSurfaceSize(const Size(800, 600));
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        expect(find.byType(AudioIconWidget), findsOneWidget);
        
        // Reset to default size
        await tester.binding.setSurfaceSize(null);
      });
    });
    
    group('Audio Icon Integration', () {
      testWidgets('should pass correct parameters to audio icon widget', (WidgetTester tester) async {
        final message = Message(
          id: 'test_audio_params',
          text: 'Test message for audio parameters',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: 'Test sentence for audio parameters!',
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        final audioIconWidget = tester.widget<AudioIconWidget>(
          find.byType(AudioIconWidget),
        );
        
        expect(audioIconWidget.text, equals('Test sentence for audio parameters!'));
        expect(audioIconWidget.messageId, equals('test_audio_params'));
        expect(audioIconWidget.initialState, equals(AudioIconState.idle));
        expect(audioIconWidget.isFirstTime, isTrue);
      });
      
      testWidgets('should handle audio icon tap interactions', (WidgetTester tester) async {
        final message = Message(
          id: 'test_audio_tap',
          text: 'Test message for audio tap',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: 'Test sentence for tap!',
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        // Find and tap the audio icon
        final audioIconFinder = find.byType(AudioIconWidget);
        expect(audioIconFinder, findsOneWidget);
        
        await tester.tap(audioIconFinder);
        await tester.pump();
        
        // Verify that the tap was handled (state should change)
        // Note: This test verifies the tap is registered, actual TTS functionality
        // is tested separately in integration tests
      });
    });
    
    group('Message States', () {
      testWidgets('should display streaming indicator when message is streaming', (WidgetTester tester) async {
        final message = Message(
          id: 'test_streaming',
          text: 'Streaming message',
          isUser: false,
          timestamp: DateTime.now(),
          isStreaming: true,
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        expect(find.text('Live transcription...'), findsOneWidget);
        expect(find.text('Streaming message'), findsOneWidget);
      });
      
      testWidgets('should display processing indicator when message is processing', (WidgetTester tester) async {
        final message = Message(
          id: 'test_processing',
          text: 'Processing message',
          isUser: false,
          timestamp: DateTime.now(),
          isProcessing: true,
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        expect(find.text('⚡ Processing with AI agents...'), findsOneWidget);
        expect(find.text('Processing message'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });
      
      testWidgets('should display correction results when available', (WidgetTester tester) async {
        final correctionResult = CorrectionResult(
          originalText: 'Ich bin gut',
          correctedText: 'Mir geht es gut',
          hasCorrections: true,
          suggestions: ['Use "Mir geht es gut" instead of "Ich bin gut"'],
          explanations: ['This is more natural German'],
        );
        
        final message = Message(
          id: 'test_correction',
          text: 'Message with correction',
          isUser: false,
          timestamp: DateTime.now(),
          correctionResult: correctionResult,
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        expect(find.text('German Correction Agent'), findsOneWidget);
        expect(find.text('Original: '), findsOneWidget);
        expect(find.text('Corrected: '), findsOneWidget);
        expect(find.text('Suggestions:'), findsOneWidget);
        expect(find.text('Explanations:'), findsOneWidget);
      });
    });
    
    group('Accessibility', () {
      testWidgets('should provide proper semantics for audio icon', (WidgetTester tester) async {
        final message = Message(
          id: 'test_accessibility',
          text: 'Accessibility test message',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: 'Accessibility test!',
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        final audioIconWidget = tester.widget<AudioIconWidget>(
          find.byType(AudioIconWidget),
        );
        
        expect(audioIconWidget.semanticsLabel, equals('Play audio for this message'));
      });
      
      testWidgets('should be accessible via screen readers', (WidgetTester tester) async {
        final message = Message(
          id: 'test_screen_reader',
          text: 'Screen reader test message',
          isUser: false,
          timestamp: DateTime.now(),
          extractedSentence: 'Screen reader test!',
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: message),
            ),
          ),
        );
        
        // Verify that the audio icon has proper semantics
        final semantics = tester.getSemantics(find.byType(AudioIconWidget));
        expect(semantics.hasFlag(SemanticsFlag.isButton), isTrue);
        expect(semantics.hasFlag(SemanticsFlag.isEnabled), isTrue);
      });
    });
    
    group('Timestamp and Avatar', () {
      testWidgets('should display timestamp when showTimestamp is true', (WidgetTester tester) async {
        final message = Message(
          id: 'test_timestamp',
          text: 'Message with timestamp',
          isUser: false,
          timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(
                message: message,
                showTimestamp: true,
              ),
            ),
          ),
        );
        
        expect(find.text('5m ago'), findsOneWidget);
      });
      
      testWidgets('should display avatar when showAvatar is true', (WidgetTester tester) async {
        final message = Message(
          id: 'test_avatar',
          text: 'Message with avatar',
          isUser: false,
          timestamp: DateTime.now(),
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(
                message: message,
                showAvatar: true,
              ),
            ),
          ),
        );
        
        expect(find.byType(CircleAvatar), findsOneWidget);
        expect(find.byIcon(Icons.smart_toy), findsOneWidget);
      });
      
      testWidgets('should display user avatar for user messages', (WidgetTester tester) async {
        final message = Message(
          id: 'test_user_avatar',
          text: 'User message with avatar',
          isUser: true,
          timestamp: DateTime.now(),
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(
                message: message,
                showAvatar: true,
              ),
            ),
          ),
        );
        
        expect(find.byType(CircleAvatar), findsOneWidget);
        expect(find.byIcon(Icons.person), findsOneWidget);
      });
    });
    
    group('Interaction Callbacks', () {
      testWidgets('should call onTap callback when message is tapped', (WidgetTester tester) async {
        bool tapCalled = false;
        
        final message = Message(
          id: 'test_tap_callback',
          text: 'Tappable message',
          isUser: false,
          timestamp: DateTime.now(),
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(
                message: message,
                onTap: () {
                  tapCalled = true;
                },
              ),
            ),
          ),
        );
        
        await tester.tap(find.byType(MessageBubble));
        await tester.pump();
        
        expect(tapCalled, isTrue);
      });
      
      testWidgets('should call onLongPress callback when message is long pressed', (WidgetTester tester) async {
        bool longPressCalled = false;
        
        final message = Message(
          id: 'test_long_press_callback',
          text: 'Long pressable message',
          isUser: false,
          timestamp: DateTime.now(),
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(
                message: message,
                onLongPress: () {
                  longPressCalled = true;
                },
              ),
            ),
          ),
        );
        
        await tester.longPress(find.byType(MessageBubble));
        await tester.pump();
        
        expect(longPressCalled, isTrue);
      });
    });
  });
  
  group('AnimatedMessageBubble Widget Tests', () {
    testWidgets('should animate message bubble entry', (WidgetTester tester) async {
      final message = Message(
        id: 'test_animated',
        text: 'Animated message',
        isUser: false,
        timestamp: DateTime.now(),
      );
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AnimatedMessageBubble(message: message),
          ),
        ),
      );
      
      // Initially, the message should be animating in
      expect(find.byType(SlideTransition), findsOneWidget);
      expect(find.byType(FadeTransition), findsOneWidget);
      
      // Complete the animation
      await tester.pumpAndSettle();
      
      // Message should be fully visible
      expect(find.text('Animated message'), findsOneWidget);
    });
    
    testWidgets('should animate from correct direction based on message type', (WidgetTester tester) async {
      final userMessage = Message(
        id: 'test_user_animated',
        text: 'User animated message',
        isUser: true,
        timestamp: DateTime.now(),
      );
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AnimatedMessageBubble(message: userMessage),
          ),
        ),
      );
      
      // Find the slide transition
      final slideTransition = tester.widget<SlideTransition>(
        find.byType(SlideTransition),
      );
      
      // User messages should slide from the right (positive x offset)
      expect(slideTransition.position, isNotNull);
      
      await tester.pumpAndSettle();
      expect(find.text('User animated message'), findsOneWidget);
    });
  });
  
  group('CompactMessageBubble Widget Tests', () {
    testWidgets('should render compact version of message bubble', (WidgetTester tester) async {
      final message = Message(
        id: 'test_compact',
        text: 'This is a compact message that should be displayed in a smaller format',
        isUser: false,
        timestamp: DateTime.now(),
        extractedSentence: 'Compact message!',
      );
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactMessageBubble(message: message),
          ),
        ),
      );
      
      expect(find.textContaining('This is a compact message'), findsOneWidget);
      expect(find.byType(AudioIconWidget), findsOneWidget);
    });
    
    testWidgets('should handle text overflow in compact mode', (WidgetTester tester) async {
      final message = Message(
        id: 'test_compact_overflow',
        text: 'This is a very long message that should be truncated in compact mode because it exceeds the maximum number of lines allowed for compact display and should show ellipsis',
        isUser: false,
        timestamp: DateTime.now(),
      );
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactMessageBubble(message: message),
          ),
        ),
      );
      
      // Message should be present but potentially truncated
      expect(find.textContaining('This is a very long message'), findsOneWidget);
    });
  });
}