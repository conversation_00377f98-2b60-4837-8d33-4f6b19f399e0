import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:deutschkorrekt_flutter/data/services/tts_audio_service.dart';
import 'package:deutschkorrekt_flutter/data/services/audio_cache_manager.dart';

import 'tts_audio_service_test.mocks.dart';

// Generate mocks for testing
@GenerateMocks([http.Client, AudioCacheManager])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('TTSAudioService', () {
    late TTSAudioService ttsService;
    late MockClient mockHttpClient;
    late MockAudioCacheManager mockCacheManager;
    
    setUp(() {
      mockHttpClient = MockClient();
      mockCacheManager = MockAudioCacheManager();
      ttsService = TTSAudioService(
        httpClient: mockHttpClient,
        cacheManager: mockCacheManager,
      );
    });
    
    tearDown(() {
      ttsService.dispose();
    });
    
    group('Initialization', () {
      test('should initialize with default values', () {
        final service = TTSAudioService();
        expect(service.isPlaying, isFalse);
        expect(service.currentlyPlayingMessageId, isNull);
        service.dispose();
      });
      
      test('should initialize with custom dependencies', () {
        final customClient = MockClient();
        final customCacheManager = MockAudioCacheManager();
        
        final service = TTSAudioService(
          httpClient: customClient,
          cacheManager: customCacheManager,
        );
        
        expect(service.isPlaying, isFalse);
        expect(service.currentlyPlayingMessageId, isNull);
        service.dispose();
      });
    });
    
    group('Cache-First Playback Logic', () {
      test('should play from cache when audio is cached', () async {
        const messageId = 'cached_message';
        const text = 'Hello world!';
        const cachedPath = '/path/to/cached/audio.mp3';
        
        // Mock cache hit
        when(mockCacheManager.getCachedAudio(messageId))
            .thenAnswer((_) async => cachedPath);
        
        final result = await ttsService.playTTS(text, messageId);
        
        expect(result.success, isTrue);
        expect(result.fromCache, isTrue);
        expect(result.cachedFilePath, equals(cachedPath));
        
        // Verify cache was checked
        verify(mockCacheManager.getCachedAudio(messageId)).called(1);
        
        // Verify no HTTP request was made
        verifyNever(mockHttpClient.post(any, headers: anyNamed('headers'), body: anyNamed('body')));
      });
      
      test('should fallback to backend when cache miss occurs', () async {
        const messageId = 'uncached_message';
        const text = 'Hello world!';
        final audioData = Uint8List.fromList([1, 2, 3, 4, 5]);
        
        // Mock cache miss
        when(mockCacheManager.getCachedAudio(messageId))
            .thenAnswer((_) async => null);
        
        // Mock successful backend response
        final responseBody = {
          'audio_data': base64.encode(audioData),
          'content_type': 'audio/mpeg',
          'duration_seconds': 2.5,
          'processing_time': 0.8,
        };
        
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode(responseBody),
          200,
        ));
        
        // Mock successful caching
        when(mockCacheManager.cacheAudio(messageId, audioData))
            .thenAnswer((_) async {});
        when(mockCacheManager.getCachedAudio(messageId))
            .thenAnswer((_) async => '/path/to/new/cached/audio.mp3');
        
        final result = await ttsService.playTTS(text, messageId);
        
        expect(result.success, isTrue);
        expect(result.fromCache, isFalse);
        
        // Verify cache was checked first
        verify(mockCacheManager.getCachedAudio(messageId)).called(greaterThan(0));
        
        // Verify HTTP request was made
        verify(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).called(1);
        
        // Verify audio was cached
        verify(mockCacheManager.cacheAudio(messageId, audioData)).called(1);
      });
      
      test('should handle cache errors gracefully and fallback to backend', () async {
        const messageId = 'cache_error_message';
        const text = 'Hello world!';
        final audioData = Uint8List.fromList([1, 2, 3, 4, 5]);
        
        // Mock cache error
        when(mockCacheManager.getCachedAudio(messageId))
            .thenThrow(Exception('Cache error'));
        
        // Mock successful backend response
        final responseBody = {
          'audio_data': base64.encode(audioData),
          'content_type': 'audio/mpeg',
          'duration_seconds': 2.5,
        };
        
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode(responseBody),
          200,
        ));
        
        // Mock successful caching after error recovery
        when(mockCacheManager.cacheAudio(messageId, audioData))
            .thenAnswer((_) async {});
        when(mockCacheManager.validateAndCleanCache())
            .thenAnswer((_) async {});
        
        final result = await ttsService.playTTS(text, messageId);
        
        expect(result.success, isTrue);
        expect(result.fromCache, isFalse);
        
        // Verify cache validation was attempted
        verify(mockCacheManager.validateAndCleanCache()).called(1);
      });
    });
    
    group('Backend API Communication', () {
      test('should make correct HTTP request to backend', () async {
        const messageId = 'api_test_message';
        const text = 'Test sentence for TTS.';
        final audioData = Uint8List.fromList([1, 2, 3, 4, 5]);
        
        // Mock cache miss
        when(mockCacheManager.getCachedAudio(messageId))
            .thenAnswer((_) async => null);
        
        // Mock successful backend response
        final responseBody = {
          'audio_data': base64.encode(audioData),
          'content_type': 'audio/mpeg',
          'duration_seconds': 3.2,
          'processing_time': 1.1,
        };
        
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode(responseBody),
          200,
        ));
        
        when(mockCacheManager.cacheAudio(any, any)).thenAnswer((_) async {});
        when(mockCacheManager.getCachedAudio(messageId))
            .thenAnswer((_) async => '/cached/path.mp3');
        
        await ttsService.playTTS(text, messageId);
        
        // Verify correct request was made
        final captured = verify(mockHttpClient.post(
          captureAny,
          headers: captureAnyNamed('headers'),
          body: captureAnyNamed('body'),
        )).captured;
        
        final uri = captured[0] as Uri;
        final headers = captured[1] as Map<String, String>;
        final body = captured[2] as String;
        
        expect(uri.toString(), contains('/api/tts'));
        expect(headers['Content-Type'], equals('application/json'));
        expect(headers['Accept'], equals('application/json'));
        
        final requestData = json.decode(body) as Map<String, dynamic>;
        expect(requestData['text'], equals(text));
        expect(requestData['message_id'], equals(messageId));
        expect(requestData['voice_config'], equals('de-DE-Chirp3-HD-Aoede'));
      });
      
      test('should handle HTTP timeout errors', () async {
        const messageId = 'timeout_message';
        const text = 'Test timeout.';
        
        when(mockCacheManager.getCachedAudio(messageId))
            .thenAnswer((_) async => null);
        
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenThrow(TimeoutException('Request timeout', const Duration(seconds: 10)));
        
        final result = await ttsService.playTTS(text, messageId);
        
        expect(result.success, isFalse);
        expect(result.errorCode, equals('TIMEOUT'));
        expect(result.canRetry, isTrue);
        expect(result.displayMessage, contains('timed out'));
      });
      
      test('should handle network errors', () async {
        const messageId = 'network_error_message';
        const text = 'Test network error.';
        
        when(mockCacheManager.getCachedAudio(messageId))
            .thenAnswer((_) async => null);
        
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenThrow(const SocketException('Network unreachable', osError: null, address: null, port: null));
        
        final result = await ttsService.playTTS(text, messageId);
        
        expect(result.success, isFalse);
        expect(result.errorCode, equals('NETWORK_ERROR'));
        expect(result.canRetry, isTrue);
        expect(result.displayMessage, contains('Network connection failed'));
      });
      
      test('should handle HTTP error responses', () async {
        const messageId = 'http_error_message';
        const text = 'Test HTTP error.';
        
        when(mockCacheManager.getCachedAudio(messageId))
            .thenAnswer((_) async => null);
        
        final errorResponse = {
          'error_message': 'Invalid text input',
          'error_code': 'INVALID_TEXT',
        };
        
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode(errorResponse),
          400,
        ));
        
        final result = await ttsService.playTTS(text, messageId);
        
        expect(result.success, isFalse);
        expect(result.errorCode, equals('INVALID_TEXT'));
        expect(result.errorMessage, equals('Invalid text input'));
      });
      
      test('should handle rate limiting with retry after', () async {
        const messageId = 'rate_limit_message';
        const text = 'Test rate limiting.';
        
        when(mockCacheManager.getCachedAudio(messageId))
            .thenAnswer((_) async => null);
        
        final rateLimitResponse = {
          'error_message': 'Rate limit exceeded',
          'error_code': 'RATE_LIMITED',
          'retry_after': 5,
        };
        
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode(rateLimitResponse),
          429,
        ));
        
        final result = await ttsService.playTTS(text, messageId);
        
        expect(result.success, isFalse);
        expect(result.errorCode, equals('RATE_LIMITED'));
        expect(result.displayMessage, contains('Too many requests'));
      });
      
      test('should retry failed requests up to maximum attempts', () async {
        const messageId = 'retry_message';
        const text = 'Test retry logic.';
        
        when(mockCacheManager.getCachedAudio(messageId))
            .thenAnswer((_) async => null);
        
        // Mock multiple failures followed by success
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => throw const SocketException('Network error', osError: null, address: null, port: null));
        
        final result = await ttsService.playTTS(text, messageId);
        
        expect(result.success, isFalse);
        expect(result.errorCode, equals('NETWORK_ERROR'));
        
        // Verify multiple retry attempts were made
        verify(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).called(3); // Default max retry attempts
      });
    });
    
    group('Audio Caching Integration', () {
      test('should cache audio after successful backend response', () async {
        const messageId = 'cache_integration_message';
        const text = 'Test caching integration.';
        final audioData = Uint8List.fromList([1, 2, 3, 4, 5]);
        
        when(mockCacheManager.getCachedAudio(messageId))
            .thenAnswer((_) async => null);
        
        final responseBody = {
          'audio_data': base64.encode(audioData),
          'content_type': 'audio/mpeg',
          'duration_seconds': 2.0,
        };
        
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode(responseBody),
          200,
        ));
        
        when(mockCacheManager.cacheAudio(messageId, audioData))
            .thenAnswer((_) async {});
        when(mockCacheManager.getCachedAudio(messageId))
            .thenAnswer((_) async => '/cached/audio.mp3');
        
        final result = await ttsService.playTTS(text, messageId);
        
        expect(result.success, isTrue);
        
        // Verify audio was cached with correct data
        verify(mockCacheManager.cacheAudio(messageId, audioData)).called(1);
      });
      
      test('should handle caching failures gracefully', () async {
        const messageId = 'cache_failure_message';
        const text = 'Test cache failure handling.';
        final audioData = Uint8List.fromList([1, 2, 3, 4, 5]);
        
        when(mockCacheManager.getCachedAudio(messageId))
            .thenAnswer((_) async => null);
        
        final responseBody = {
          'audio_data': base64.encode(audioData),
          'content_type': 'audio/mpeg',
          'duration_seconds': 2.0,
        };
        
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode(responseBody),
          200,
        ));
        
        // Mock caching failure
        when(mockCacheManager.cacheAudio(messageId, audioData))
            .thenThrow(Exception('Cache write error'));
        
        // Mock cache cleanup and retry
        when(mockCacheManager.validateAndCleanCache())
            .thenAnswer((_) async {});
        when(mockCacheManager.clearOldCache())
            .thenAnswer((_) async {});
        
        final result = await ttsService.playTTS(text, messageId);
        
        // Should still succeed even if caching fails
        expect(result.success, isTrue);
        expect(result.fromCache, isFalse);
        
        // Verify cache recovery attempts were made
        verify(mockCacheManager.validateAndCleanCache()).called(1);
        verify(mockCacheManager.clearOldCache()).called(1);
      });
    });
    
    group('Playback State Management', () {
      test('should track playing state correctly', () {
        expect(ttsService.isPlaying, isFalse);
        expect(ttsService.currentlyPlayingMessageId, isNull);
      });
      
      test('should stop current playback when starting new playback', () async {
        const messageId1 = 'message1';
        const messageId2 = 'message2';
        const text = 'Test text';
        
        // Mock cache hits for both messages
        when(mockCacheManager.getCachedAudio(messageId1))
            .thenAnswer((_) async => '/cached/audio1.mp3');
        when(mockCacheManager.getCachedAudio(messageId2))
            .thenAnswer((_) async => '/cached/audio2.mp3');
        
        // Start first playback
        await ttsService.playTTS(text, messageId1);
        
        // Start second playback (should stop first)
        await ttsService.playTTS(text, messageId2);
        
        // Both calls should succeed
        verify(mockCacheManager.getCachedAudio(messageId1)).called(1);
        verify(mockCacheManager.getCachedAudio(messageId2)).called(1);
      });
      
      test('should handle stop playback correctly', () async {
        await ttsService.stopPlayback();
        
        expect(ttsService.isPlaying, isFalse);
        expect(ttsService.currentlyPlayingMessageId, isNull);
      });
    });
    
    group('Cache Management Operations', () {
      test('should clear cached audio for specific message', () async {
        const messageId = 'clear_specific_message';
        
        when(mockCacheManager.deleteAudioFile(messageId))
            .thenAnswer((_) async => true);
        
        await ttsService.clearCachedAudio(messageId);
        
        verify(mockCacheManager.deleteAudioFile(messageId)).called(1);
      });
      
      test('should clear all cached audio', () async {
        when(mockCacheManager.clearOldCache())
            .thenAnswer((_) async {});
        
        await ttsService.clearAllCachedAudio();
        
        verify(mockCacheManager.clearOldCache()).called(1);
      });
      
      test('should handle new Groq response cleanup', () async {
        final cacheStats = {
          'fileCount': 5,
          'totalSizeBytes': 1024000,
        };
        
        when(mockCacheManager.getCacheStats())
            .thenAnswer((_) async => cacheStats);
        when(mockCacheManager.clearOldCache())
            .thenAnswer((_) async {});
        
        final result = await ttsService.onNewGroqResponse();
        
        expect(result['success'], isTrue);
        expect(result['cleanupDuration'], isA<int>());
        expect(result['timestamp'], isA<String>());
        
        verify(mockCacheManager.clearOldCache()).called(1);
        verify(mockCacheManager.getCacheStats()).called(2); // Before and after
      });
      
      test('should handle cleanup errors in new Groq response', () async {
        when(mockCacheManager.getCacheStats())
            .thenThrow(Exception('Stats error'));
        
        final result = await ttsService.onNewGroqResponse();
        
        expect(result['success'], isFalse);
        expect(result['error'], isNotNull);
        expect(result['cleanupDuration'], isA<int>());
      });
    });
    
    group('Service Statistics', () {
      test('should provide accurate service statistics', () async {
        final cacheStats = {
          'fileCount': 3,
          'totalSizeBytes': 512000,
          'totalSizeKB': 500,
        };
        
        when(mockCacheManager.getCacheStats())
            .thenAnswer((_) async => cacheStats);
        
        final stats = await ttsService.getServiceStats();
        
        expect(stats['isPlaying'], isFalse);
        expect(stats['currentlyPlayingMessageId'], isNull);
        expect(stats['baseUrl'], isNotNull);
        expect(stats['requestTimeout'], isA<int>());
        expect(stats['maxRetryAttempts'], isA<int>());
        expect(stats['cacheStats'], equals(cacheStats));
      });
      
      test('should handle statistics errors gracefully', () async {
        when(mockCacheManager.getCacheStats())
            .thenThrow(Exception('Stats error'));
        
        final stats = await ttsService.getServiceStats();
        
        expect(stats['error'], isNotNull);
      });
    });
    
    group('Resource Management', () {
      test('should dispose resources properly', () {
        // Create service and dispose
        final service = TTSAudioService(
          httpClient: mockHttpClient,
          cacheManager: mockCacheManager,
        );
        
        expect(() => service.dispose(), returnsNormally);
        
        // Verify HTTP client was closed
        verify(mockHttpClient.close()).called(1);
      });
      
      test('should handle disposal errors gracefully', () {
        when(mockHttpClient.close()).thenThrow(Exception('Close error'));
        
        expect(() => ttsService.dispose(), returnsNormally);
      });
    });
    
    group('TTSPlaybackResult', () {
      test('should provide correct display messages for different error codes', () {
        final testCases = [
          ('NETWORK_ERROR', 'Network connection failed'),
          ('TIMEOUT', 'Request timed out'),
          ('FILE_NOT_FOUND', 'Audio file not found'),
          ('CACHE_PLAYBACK_ERROR', 'Cached audio playback failed'),
          ('RATE_LIMITED', 'Too many requests'),
          ('INVALID_TEXT', 'Text cannot be converted'),
          ('UNKNOWN_ERROR', 'Audio playback failed'),
        ];
        
        for (final testCase in testCases) {
          final result = TTSPlaybackResult(
            success: false,
            errorCode: testCase.$1,
            fromCache: false,
          );
          
          expect(result.displayMessage, contains(testCase.$2),
              reason: 'Failed for error code: ${testCase.$1}');
        }
      });
      
      test('should provide success message for successful playback', () {
        final result = TTSPlaybackResult(
          success: true,
          fromCache: true,
        );
        
        expect(result.displayMessage, equals('Audio playback successful'));
      });
      
      test('should use custom user-friendly message when provided', () {
        const customMessage = 'Custom error message for user';
        final result = TTSPlaybackResult(
          success: false,
          errorCode: 'SOME_ERROR',
          fromCache: false,
          userFriendlyMessage: customMessage,
        );
        
        expect(result.displayMessage, equals(customMessage));
      });
    });
    
    group('TTSBackendResult', () {
      test('should create backend result with all properties', () {
        final audioData = Uint8List.fromList([1, 2, 3, 4, 5]);
        
        final result = TTSBackendResult(
          success: true,
          audioData: audioData,
          contentType: 'audio/mpeg',
          durationSeconds: 3.5,
          processingTime: 1.2,
          originalText: 'Original text',
          sanitizedText: 'Sanitized text',
        );
        
        expect(result.success, isTrue);
        expect(result.audioData, equals(audioData));
        expect(result.contentType, equals('audio/mpeg'));
        expect(result.durationSeconds, equals(3.5));
        expect(result.processingTime, equals(1.2));
        expect(result.originalText, equals('Original text'));
        expect(result.sanitizedText, equals('Sanitized text'));
      });
      
      test('should create error backend result', () {
        final result = TTSBackendResult(
          success: false,
          errorMessage: 'Backend error',
          errorCode: 'BACKEND_ERROR',
          httpStatusCode: 500,
        );
        
        expect(result.success, isFalse);
        expect(result.errorMessage, equals('Backend error'));
        expect(result.errorCode, equals('BACKEND_ERROR'));
        expect(result.httpStatusCode, equals(500));
      });
    });
  });
}