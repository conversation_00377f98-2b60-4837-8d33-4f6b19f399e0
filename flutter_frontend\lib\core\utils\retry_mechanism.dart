import 'dart:async';
import 'dart:math';
import 'error_handler.dart';

/// Retry mechanism with exponential backoff for failed operations
class RetryMechanism {
  /// Execute a function with retry logic
  static Future<T> execute<T>(
    Future<T> Function() operation, {
    int maxAttempts = 3,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffMultiplier = 2.0,
    Duration maxDelay = const Duration(seconds: 30),
    bool Function(dynamic error)? shouldRetry,
    void Function(int attempt, dynamic error)? onRetry,
  }) async {
    int attempt = 0;
    Duration currentDelay = initialDelay;
    
    while (attempt < maxAttempts) {
      attempt++;
      
      try {
        return await operation();
      } catch (error) {
        ErrorHandler.logError('Retry attempt $attempt', error);
        
        // Check if we should retry this error
        final shouldRetryError = shouldRetry?.call(error) ?? 
                                ErrorHandler.isRecoverableError(error);
        
        if (!shouldRetryError || attempt >= maxAttempts) {
          rethrow;
        }
        
        // Call retry callback
        onRetry?.call(attempt, error);
        
        // Calculate delay with jitter
        final jitter = Random().nextDouble() * 0.1; // 10% jitter
        final delayWithJitter = Duration(
          milliseconds: (currentDelay.inMilliseconds * (1 + jitter)).round(),
        );
        
        // Wait before retrying
        await Future.delayed(delayWithJitter);
        
        // Increase delay for next attempt
        currentDelay = Duration(
          milliseconds: min(
            (currentDelay.inMilliseconds * backoffMultiplier).round(),
            maxDelay.inMilliseconds,
          ),
        );
      }
    }
    
    throw Exception('Max retry attempts exceeded');
  }

  /// Execute with specific retry configuration for authentication
  static Future<T> executeAuth<T>(
    Future<T> Function() operation, {
    void Function(int attempt, dynamic error)? onRetry,
  }) async {
    return execute(
      operation,
      maxAttempts: 3,
      initialDelay: const Duration(seconds: 1),
      backoffMultiplier: 2.0,
      shouldRetry: (error) {
        final errorString = error.toString().toLowerCase();
        // Retry on network errors, timeouts, and server errors
        return errorString.contains('network') ||
               errorString.contains('timeout') ||
               errorString.contains('server') ||
               errorString.contains('connection');
      },
      onRetry: onRetry,
    );
  }

  /// Execute with specific retry configuration for database operations
  static Future<T> executeDatabase<T>(
    Future<T> Function() operation, {
    void Function(int attempt, dynamic error)? onRetry,
  }) async {
    return execute(
      operation,
      maxAttempts: 5,
      initialDelay: const Duration(milliseconds: 500),
      backoffMultiplier: 1.5,
      shouldRetry: (error) {
        final errorString = error.toString().toLowerCase();
        // Retry on connection issues, timeouts, and temporary server errors
        return errorString.contains('connection') ||
               errorString.contains('timeout') ||
               errorString.contains('temporary') ||
               errorString.contains('503') ||
               errorString.contains('502');
      },
      onRetry: onRetry,
    );
  }

  /// Execute with specific retry configuration for API calls
  static Future<T> executeApi<T>(
    Future<T> Function() operation, {
    void Function(int attempt, dynamic error)? onRetry,
  }) async {
    return execute(
      operation,
      maxAttempts: 3,
      initialDelay: const Duration(seconds: 2),
      backoffMultiplier: 2.0,
      maxDelay: const Duration(seconds: 10),
      shouldRetry: (error) {
        final errorString = error.toString().toLowerCase();
        // Retry on network issues and server errors, but not client errors
        return errorString.contains('network') ||
               errorString.contains('timeout') ||
               errorString.contains('500') ||
               errorString.contains('502') ||
               errorString.contains('503') ||
               errorString.contains('504');
      },
      onRetry: onRetry,
    );
  }

  /// Execute with rate limit handling
  static Future<T> executeWithRateLimit<T>(
    Future<T> Function() operation, {
    void Function(int attempt, dynamic error)? onRetry,
  }) async {
    return execute(
      operation,
      maxAttempts: 5,
      initialDelay: const Duration(seconds: 5),
      backoffMultiplier: 2.0,
      maxDelay: const Duration(minutes: 2),
      shouldRetry: (error) {
        final errorString = error.toString().toLowerCase();
        return errorString.contains('rate limit') ||
               errorString.contains('too many requests') ||
               errorString.contains('429');
      },
      onRetry: onRetry,
    );
  }
}

/// Circuit breaker pattern implementation
class CircuitBreaker {
  final int failureThreshold;
  final Duration timeout;
  final Duration resetTimeout;
  
  int _failureCount = 0;
  DateTime? _lastFailureTime;
  CircuitBreakerState _state = CircuitBreakerState.closed;
  
  CircuitBreaker({
    this.failureThreshold = 5,
    this.timeout = const Duration(seconds: 30),
    this.resetTimeout = const Duration(minutes: 1),
  });
  
  /// Execute operation with circuit breaker protection
  Future<T> execute<T>(Future<T> Function() operation) async {
    if (_state == CircuitBreakerState.open) {
      if (_shouldAttemptReset()) {
        _state = CircuitBreakerState.halfOpen;
      } else {
        throw CircuitBreakerOpenException('Circuit breaker is open');
      }
    }
    
    try {
      final result = await operation().timeout(timeout);
      _onSuccess();
      return result;
    } catch (error) {
      _onFailure();
      rethrow;
    }
  }
  
  void _onSuccess() {
    _failureCount = 0;
    _state = CircuitBreakerState.closed;
  }
  
  void _onFailure() {
    _failureCount++;
    _lastFailureTime = DateTime.now();
    
    if (_failureCount >= failureThreshold) {
      _state = CircuitBreakerState.open;
    }
  }
  
  bool _shouldAttemptReset() {
    return _lastFailureTime != null &&
           DateTime.now().difference(_lastFailureTime!) > resetTimeout;
  }
  
  CircuitBreakerState get state => _state;
  int get failureCount => _failureCount;
}

enum CircuitBreakerState { closed, open, halfOpen }

class CircuitBreakerOpenException implements Exception {
  final String message;
  CircuitBreakerOpenException(this.message);
  
  @override
  String toString() => 'CircuitBreakerOpenException: $message';
}

/// Request queue for handling rate limits
class RequestQueue {
  final Duration minInterval;
  final Queue<Completer> _queue = Queue<Completer>();
  Timer? _timer;
  
  RequestQueue({this.minInterval = const Duration(milliseconds: 100)});
  
  /// Add request to queue
  Future<void> enqueue() async {
    final completer = Completer<void>();
    _queue.add(completer);
    
    _processQueue();
    
    return completer.future;
  }
  
  void _processQueue() {
    if (_timer?.isActive == true || _queue.isEmpty) {
      return;
    }
    
    _timer = Timer(minInterval, () {
      if (_queue.isNotEmpty) {
        final completer = _queue.removeFirst();
        completer.complete();
        
        if (_queue.isNotEmpty) {
          _processQueue();
        }
      }
    });
  }
  
  void dispose() {
    _timer?.cancel();
    while (_queue.isNotEmpty) {
      _queue.removeFirst().completeError('Queue disposed');
    }
  }
}