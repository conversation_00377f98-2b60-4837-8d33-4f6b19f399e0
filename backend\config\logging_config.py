"""
Logging configuration for the application.
"""

import logging
import sys
import uuid
from typing import Optional
import json
from datetime import datetime

class CorrelationIdFilter(logging.Filter):
    """
    Filter that adds a correlation ID to log records.
    This helps track related log entries across the application.
    """
    
    def __init__(self, name: str = ""):
        super().__init__(name)
        self.correlation_id = None
    
    def filter(self, record):
        if not hasattr(record, 'correlation_id'):
            record.correlation_id = self.correlation_id or str(uuid.uuid4())
        return True

class JsonFormatter(logging.Formatter):
    """
    Format logs as JSON for better parsing in log management systems.
    """
    
    def format(self, record):
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "correlation_id": getattr(record, 'correlation_id', None)
        }
        
        # Add exception info if available
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in log_data and not key.startswith('_') and not callable(value):
                log_data[key] = value
        
        return json.dumps(log_data)

def configure_logging(log_level: str = "INFO", json_format: bool = False):
    """
    Configure logging for the application.
    
    Args:
        log_level: The log level to use
        json_format: Whether to use JSON formatting
    """
    # Get the numeric log level
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        numeric_level = logging.INFO
    
    # Create handlers
    console_handler = logging.StreamHandler(sys.stdout)
    
    # Create formatters
    if json_format:
        formatter = JsonFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(correlation_id)s] - %(message)s'
        )
    
    # Set formatter on handlers
    console_handler.setFormatter(formatter)
    
    # Create correlation ID filter
    correlation_filter = CorrelationIdFilter()
    console_handler.addFilter(correlation_filter)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Add our handlers
    root_logger.addHandler(console_handler)
    
    # Configure specific loggers
    for logger_name in ["uvicorn", "fastapi", "websockets"]:
        logger = logging.getLogger(logger_name)
        logger.handlers = []
        logger.propagate = True
    
    return correlation_filter

def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the given name.
    
    Args:
        name: The logger name
        
    Returns:
        logging.Logger: The logger
    """
    return logging.getLogger(name)

def set_correlation_id(correlation_id: Optional[str] = None) -> str:
    """
    Set the correlation ID for the current request.
    
    Args:
        correlation_id: The correlation ID to use, or None to generate a new one
        
    Returns:
        str: The correlation ID
    """
    if correlation_id is None:
        correlation_id = str(uuid.uuid4())
    
    # Get the correlation filter from the root logger
    for handler in logging.getLogger().handlers:
        for filter_obj in handler.filters:
            if isinstance(filter_obj, CorrelationIdFilter):
                filter_obj.correlation_id = correlation_id
                return correlation_id
    
    return correlation_id