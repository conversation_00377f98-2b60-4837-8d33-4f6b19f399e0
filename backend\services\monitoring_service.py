"""
Monitoring service for tracking application performance and resource usage.
"""

import logging
import time
import asyncio
import os
import psutil
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from config.logging_config import get_logger

logger = get_logger(__name__)

class MonitoringService:
    """
    Service for monitoring application performance and resource usage.
    Tracks metrics like memory usage, CPU usage, and request latencies.
    """
    
    def __init__(self):
        """Initialize the monitoring service."""
        self.start_time = time.time()
        self.request_counts = {
            "total": 0,
            "success": 0,
            "error": 0
        }
        self.latencies = []
        self.max_latency_records = 1000  # Limit the number of latency records to avoid memory issues
        self.monitoring_task = None
        self.process = psutil.Process(os.getpid())
        self.metrics_history = []
        self.max_history_size = 60  # Keep 1 hour of history (1 sample per minute)
    
    async def start(self):
        """Start the monitoring service."""
        if self.monitoring_task is None:
            self.monitoring_task = asyncio.create_task(self._monitor_resources())
            logger.info("Monitoring service started")
    
    async def stop(self):
        """Stop the monitoring service."""
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
            self.monitoring_task = None
            logger.info("Monitoring service stopped")
    
    def record_request(self, success: bool, latency: float):
        """
        Record a request and its latency.
        
        Args:
            success: Whether the request was successful
            latency: The request latency in seconds
        """
        self.request_counts["total"] += 1
        if success:
            self.request_counts["success"] += 1
        else:
            self.request_counts["error"] += 1
        
        # Add latency to the list, keeping it under the maximum size
        self.latencies.append(latency)
        if len(self.latencies) > self.max_latency_records:
            self.latencies = self.latencies[-self.max_latency_records:]
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get current performance metrics.
        
        Returns:
            Dict[str, Any]: The current metrics
        """
        # Calculate uptime
        uptime = time.time() - self.start_time
        
        # Calculate request rate (requests per second)
        request_rate = self.request_counts["total"] / uptime if uptime > 0 else 0
        
        # Calculate error rate
        error_rate = (self.request_counts["error"] / self.request_counts["total"]) * 100 if self.request_counts["total"] > 0 else 0
        
        # Calculate latency statistics
        avg_latency = sum(self.latencies) / len(self.latencies) if self.latencies else 0
        max_latency = max(self.latencies) if self.latencies else 0
        
        # Get current resource usage
        memory_usage = self.process.memory_info().rss / (1024 * 1024)  # MB
        cpu_percent = self.process.cpu_percent()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "uptime": uptime,
            "requests": {
                "total": self.request_counts["total"],
                "success": self.request_counts["success"],
                "error": self.request_counts["error"],
                "rate": request_rate,
                "error_rate": error_rate
            },
            "latency": {
                "average": avg_latency,
                "max": max_latency
            },
            "resources": {
                "memory_mb": memory_usage,
                "cpu_percent": cpu_percent
            }
        }
    
    def get_metrics_history(self) -> List[Dict[str, Any]]:
        """
        Get historical metrics.
        
        Returns:
            List[Dict[str, Any]]: The metrics history
        """
        return self.metrics_history
    
    async def _monitor_resources(self):
        """Background task to monitor resource usage."""
        try:
            while True:
                # Get current metrics
                metrics = self.get_metrics()
                
                # Add to history
                self.metrics_history.append(metrics)
                
                # Keep history size limited
                if len(self.metrics_history) > self.max_history_size:
                    self.metrics_history = self.metrics_history[-self.max_history_size:]
                
                # Log resource usage if it's high
                memory_mb = metrics["resources"]["memory_mb"]
                cpu_percent = metrics["resources"]["cpu_percent"]
                
                if memory_mb > 500 or cpu_percent > 80:
                    logger.warning(f"High resource usage: Memory={memory_mb:.1f}MB, CPU={cpu_percent:.1f}%")
                
                # Wait for next check
                await asyncio.sleep(60)  # Check every minute
                
        except asyncio.CancelledError:
            # Task was cancelled, exit gracefully
            logger.info("Resource monitoring task cancelled")
        except Exception as e:
            logger.error(f"Error in resource monitoring task: {str(e)}", exc_info=True)

# Global instance
monitoring_service = MonitoringService()