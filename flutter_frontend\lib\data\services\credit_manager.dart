import '../repositories/user_repository.dart';
import '../models/user_profile.dart';

/// Service for managing user credits and refresh cycles
class CreditManager {
  final UserRepository _userRepository = UserRepository();

  /// Consume one credit for a user
  Future<CreditResult> consumeCredit(String email) async {
    try {
      // Get current user profile
      final profile = await _userRepository.getUserProfile(email);
      if (profile == null) {
        return CreditResult.error('User profile not found');
      }

      // Check if user has credits available
      if (!profile.hasCreditsAvailable) {
        return CreditResult.insufficientCredits(
          'No credits available. Next refresh: ${_formatRefreshDate(profile.nextRefreshDate)}',
        );
      }

      // Check if credits need refresh before consumption
      if (profile.needsRefresh) {
        final refreshedProfile = await _refreshCredits(profile);
        if (refreshedProfile != null) {
          // Consume credit from refreshed profile
          final updatedProfile = await _userRepository.consumeCredit(email);
          return CreditResult.success(updatedProfile, wasRefreshed: true);
        }
      }

      // Consume credit normally
      final updatedProfile = await _userRepository.consumeCredit(email);
      return CreditResult.success(updatedProfile);
    } catch (e) {
      return CreditResult.error('Failed to consume credit: $e');
    }
  }

  /// Check if user can consume credits
  Future<CreditCheckResult> canConsumeCredit(String email) async {
    try {
      final profile = await _userRepository.getUserProfile(email);
      if (profile == null) {
        return CreditCheckResult(
          canConsume: false,
          reason: 'User profile not found',
        );
      }

      // Check if credits need refresh
      if (profile.needsRefresh) {
        return CreditCheckResult(
          canConsume: true,
          reason: 'Credits will be refreshed automatically',
          needsRefresh: true,
          currentCredits: profile.maxCredits, // After refresh
          maxCredits: profile.maxCredits,
        );
      }

      if (!profile.hasCreditsAvailable) {
        return CreditCheckResult(
          canConsume: false,
          reason: 'No credits available. Next refresh: ${_formatRefreshDate(profile.nextRefreshDate)}',
          currentCredits: profile.currentCredits,
          maxCredits: profile.maxCredits,
          nextRefreshDate: profile.nextRefreshDate,
        );
      }

      return CreditCheckResult(
        canConsume: true,
        reason: 'Credits available',
        currentCredits: profile.currentCredits,
        maxCredits: profile.maxCredits,
        nextRefreshDate: profile.nextRefreshDate,
      );
    } catch (e) {
      return CreditCheckResult(
        canConsume: false,
        reason: 'Error checking credits: $e',
      );
    }
  }

  /// Refresh credits if needed
  Future<UserProfile?> refreshCreditsIfNeeded(String email) async {
    try {
      final profile = await _userRepository.getUserProfile(email);
      if (profile == null) {
        throw Exception('User profile not found');
      }

      if (profile.needsRefresh) {
        return await _refreshCredits(profile);
      }

      return profile; // No refresh needed
    } catch (e) {
      throw Exception('Failed to refresh credits: $e');
    }
  }

  /// Force refresh credits (for manual refresh)
  Future<UserProfile> forceRefreshCredits(String email) async {
    try {
      final profile = await _userRepository.getUserProfile(email);
      if (profile == null) {
        throw Exception('User profile not found');
      }

      return await _userRepository.refreshCredits(email);
    } catch (e) {
      throw Exception('Failed to force refresh credits: $e');
    }
  }

  /// Get credit status for a user
  Future<CreditStatus> getCreditStatus(String email) async {
    try {
      final profile = await _userRepository.getUserProfile(email);
      if (profile == null) {
        return CreditStatus(
          email: email,
          currentCredits: 0,
          maxCredits: 0,
          plan: 'Unknown',
          nextRefreshDate: DateTime.now(),
          needsRefresh: false,
          daysUntilRefresh: 0,
          creditsUsagePercentage: 0.0,
          remainingCreditsPercentage: 0.0,
          status: 'Profile not found',
        );
      }

      final nextRefresh = profile.nextRefreshDate;
      final daysUntilRefresh = nextRefresh.difference(DateTime.now()).inDays;

      String status;
      if (profile.needsRefresh) {
        status = 'Credits ready for refresh';
      } else if (profile.currentCredits == 0) {
        status = 'No credits available';
      } else if (profile.currentCredits <= profile.maxCredits * 0.2) {
        status = 'Low credits';
      } else {
        status = 'Credits available';
      }

      return CreditStatus(
        email: profile.email,
        currentCredits: profile.currentCredits,
        maxCredits: profile.maxCredits,
        plan: profile.plan,
        nextRefreshDate: nextRefresh,
        needsRefresh: profile.needsRefresh,
        daysUntilRefresh: daysUntilRefresh,
        creditsUsagePercentage: profile.creditsUsagePercentage,
        remainingCreditsPercentage: profile.remainingCreditsPercentage,
        status: status,
      );
    } catch (e) {
      return CreditStatus(
        email: email,
        currentCredits: 0,
        maxCredits: 0,
        plan: 'Unknown',
        nextRefreshDate: DateTime.now(),
        needsRefresh: false,
        daysUntilRefresh: 0,
        creditsUsagePercentage: 0.0,
        remainingCreditsPercentage: 0.0,
        status: 'Error: $e',
      );
    }
  }

  /// Calculate next refresh date for a given plan start date
  DateTime calculateNextRefreshDate(DateTime planStartDate) {
    final now = DateTime.now();
    final planDay = planStartDate.day;
    
    // Start with current month
    var nextRefresh = DateTime(now.year, now.month, planDay);
    
    // If the refresh date for this month has passed, move to next month
    if (nextRefresh.isBefore(now) || nextRefresh.isAtSameMomentAs(now)) {
      // Handle month overflow
      if (now.month == 12) {
        nextRefresh = DateTime(now.year + 1, 1, planDay);
      } else {
        nextRefresh = DateTime(now.year, now.month + 1, planDay);
      }
    }
    
    // Handle edge case where the target day doesn't exist in the target month
    final targetMonth = nextRefresh.month;
    final targetYear = nextRefresh.year;
    final lastDayOfTargetMonth = DateTime(targetYear, targetMonth + 1, 0).day;
    
    if (planDay > lastDayOfTargetMonth) {
      nextRefresh = DateTime(targetYear, targetMonth, lastDayOfTargetMonth);
    }
    
    return nextRefresh;
  }

  /// Check if credits should be refreshed based on plan date
  bool shouldRefreshCredits(DateTime planStartDate) {
    final now = DateTime.now();
    final refreshDate = DateTime(now.year, now.month, planStartDate.day);
    
    // If current date is on or after the refresh date for this month
    // and the plan date is from a previous month
    return now.isAfter(refreshDate) && 
           (planStartDate.month != now.month || planStartDate.year != now.year);
  }

  /// Get credit consumption history (if needed for analytics)
  Future<List<CreditTransaction>> getCreditHistory(String email, {int limit = 50}) async {
    try {
      // This would typically come from a credit_transactions table
      // For now, we'll return empty list as this feature isn't in the current schema
      return [];
    } catch (e) {
      throw Exception('Failed to get credit history: $e');
    }
  }

  /// Internal method to refresh credits
  Future<UserProfile> _refreshCredits(UserProfile profile) async {
    return await _userRepository.refreshCredits(profile.email);
  }

  /// Format refresh date for display
  String _formatRefreshDate(DateTime refreshDate) {
    final now = DateTime.now();
    final difference = refreshDate.difference(now);
    
    if (difference.inDays == 0) {
      return 'today';
    } else if (difference.inDays == 1) {
      return 'tomorrow';
    } else if (difference.inDays < 7) {
      return 'in ${difference.inDays} days';
    } else {
      return '${refreshDate.day}/${refreshDate.month}/${refreshDate.year}';
    }
  }
}

/// Result of a credit consumption operation
class CreditResult {
  final bool success;
  final UserProfile? profile;
  final String? errorMessage;
  final bool wasRefreshed;
  final CreditResultType type;

  const CreditResult._({
    required this.success,
    this.profile,
    this.errorMessage,
    this.wasRefreshed = false,
    required this.type,
  });

  factory CreditResult.success(UserProfile profile, {bool wasRefreshed = false}) {
    return CreditResult._(
      success: true,
      profile: profile,
      wasRefreshed: wasRefreshed,
      type: CreditResultType.success,
    );
  }

  factory CreditResult.insufficientCredits(String message) {
    return CreditResult._(
      success: false,
      errorMessage: message,
      type: CreditResultType.insufficientCredits,
    );
  }

  factory CreditResult.error(String message) {
    return CreditResult._(
      success: false,
      errorMessage: message,
      type: CreditResultType.error,
    );
  }
}

enum CreditResultType { success, insufficientCredits, error }

/// Result of checking if user can consume credits
class CreditCheckResult {
  final bool canConsume;
  final String reason;
  final int currentCredits;
  final int maxCredits;
  final DateTime? nextRefreshDate;
  final bool needsRefresh;

  const CreditCheckResult({
    required this.canConsume,
    required this.reason,
    this.currentCredits = 0,
    this.maxCredits = 0,
    this.nextRefreshDate,
    this.needsRefresh = false,
  });
}

/// Credit status information
class CreditStatus {
  final String email;
  final int currentCredits;
  final int maxCredits;
  final String plan;
  final DateTime nextRefreshDate;
  final bool needsRefresh;
  final int daysUntilRefresh;
  final double creditsUsagePercentage;
  final double remainingCreditsPercentage;
  final String status;

  const CreditStatus({
    required this.email,
    required this.currentCredits,
    required this.maxCredits,
    required this.plan,
    required this.nextRefreshDate,
    required this.needsRefresh,
    required this.daysUntilRefresh,
    required this.creditsUsagePercentage,
    required this.remainingCreditsPercentage,
    required this.status,
  });

  /// Get formatted status message
  String get formattedStatus {
    if (needsRefresh) {
      return 'Credits refresh today!';
    } else if (currentCredits == 0) {
      if (daysUntilRefresh == 0) {
        return 'Credits refresh today';
      } else if (daysUntilRefresh == 1) {
        return 'Credits refresh tomorrow';
      } else {
        return 'Credits refresh in $daysUntilRefresh days';
      }
    } else {
      return '$currentCredits of $maxCredits credits remaining';
    }
  }

  /// Get color based on credit status
  Color get statusColor {
    if (needsRefresh) {
      return const Color(0xFF4CAF50); // Green
    } else if (currentCredits == 0) {
      return const Color(0xFFF44336); // Red
    } else if (remainingCreditsPercentage <= 0.2) {
      return const Color(0xFFFF9800); // Orange
    } else {
      return const Color(0xFF4CAF50); // Green
    }
  }
}

/// Credit transaction record (for future use)
class CreditTransaction {
  final String id;
  final String email;
  final int amount;
  final String type; // 'consumed', 'refreshed', 'bonus'
  final String description;
  final DateTime timestamp;

  const CreditTransaction({
    required this.id,
    required this.email,
    required this.amount,
    required this.type,
    required this.description,
    required this.timestamp,
  });
}