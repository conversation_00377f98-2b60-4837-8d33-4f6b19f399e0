import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/gradients.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/correction_result.dart';
import 'correction_result_widget.dart';

/// Widget for displaying a list of correction results
class CorrectionResultList extends StatelessWidget {
  final List<CorrectionResult> corrections;
  final bool showAgentBadges;
  final bool showProcessingTimes;
  final bool allowExpansion;
  final Function(CorrectionResult)? onCorrectionTap;
  final EdgeInsets? padding;
  final double spacing;
  
  const CorrectionResultList({
    super.key,
    required this.corrections,
    this.showAgentBadges = true,
    this.showProcessingTimes = false,
    this.allowExpansion = false,
    this.onCorrectionTap,
    this.padding,
    this.spacing = 12.0,
  });

  @override
  Widget build(BuildContext context) {
    if (corrections.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      padding: padding ?? const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(),
          
          const SizedBox(height: 16),
          
          // Corrections list
          ...corrections.asMap().entries.map((entry) {
            final index = entry.key;
            final correction = entry.value;
            
            return Column(
              children: [
                _buildCorrectionItem(correction, index),
                if (index < corrections.length - 1)
                  SizedBox(height: spacing),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.psychology,
          color: AppColors.infoBlue,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          'Correction Results (${corrections.length})',
          style: AppTextStyles.headerTitle.copyWith(
            fontSize: 18,
            color: AppColors.lightText,
          ),
        ),
        const Spacer(),
        if (showProcessingTimes)
          _buildTotalProcessingTime(),
      ],
    );
  }

  Widget _buildTotalProcessingTime() {
    final totalTime = corrections.fold<double>(
      0.0,
      (sum, correction) => sum + correction.processingTime,
    );
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.slate700.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.timer,
            size: 12,
            color: AppColors.lightText.withOpacity(0.7),
          ),
          const SizedBox(width: 4),
          Text(
            'Total: ${totalTime.toStringAsFixed(2)}s',
            style: AppTextStyles.captionText.copyWith(
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCorrectionItem(CorrectionResult correction, int index) {
    if (allowExpansion) {
      return ExpandableCorrectionResultWidget(
        correctionResult: correction,
        initiallyExpanded: index == 0, // Expand first item by default
      );
    } else {
      return CorrectionResultWidget(
        correctionResult: correction,
        showAgentBadge: showAgentBadges,
        showProcessingTime: showProcessingTimes,
        onTap: onCorrectionTap != null 
            ? () => onCorrectionTap!(correction)
            : null,
      );
    }
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.psychology_outlined,
            size: 48,
            color: AppColors.lightText.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No corrections available',
            style: AppTextStyles.bodyText1.copyWith(
              color: AppColors.lightText.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start speaking to get AI-powered German corrections',
            textAlign: TextAlign.center,
            style: AppTextStyles.captionText.copyWith(
              color: AppColors.lightText.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget for displaying correction statistics and summary
class CorrectionResultSummary extends StatelessWidget {
  final List<CorrectionResult> corrections;
  final VoidCallback? onViewAll;
  
  const CorrectionResultSummary({
    super.key,
    required this.corrections,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    if (corrections.isEmpty) {
      return const SizedBox.shrink();
    }

    final stats = _calculateStats();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: AppGradients.correctionContainerGradient,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.slate600.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  gradient: AppGradients.agentBadgeGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Correction Summary',
                  style: AppTextStyles.agentBadge,
                ),
              ),
              const Spacer(),
              if (onViewAll != null)
                TextButton(
                  onPressed: onViewAll,
                  child: Text(
                    'View All',
                    style: AppTextStyles.bodyText1.copyWith(
                      color: AppColors.infoBlue,
                      fontSize: 14,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Statistics grid
          Row(
            children: [
              Expanded(child: _buildStatItem('Corrections', stats.totalCorrections, Icons.edit)),
              Expanded(child: _buildStatItem('Suggestions', stats.totalSuggestions, Icons.lightbulb_outline)),
              Expanded(child: _buildStatItem('Explanations', stats.totalExplanations, Icons.info_outline)),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Processing time and accuracy
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Avg Time',
                  '${stats.averageProcessingTime.toStringAsFixed(1)}s',
                  Icons.timer,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Accuracy',
                  '${stats.accuracyPercentage.toStringAsFixed(0)}%',
                  Icons.check_circle_outline,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, dynamic value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        color: AppColors.slate800.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 16,
            color: AppColors.lightText.withOpacity(0.7),
          ),
          const SizedBox(height: 4),
          Text(
            value.toString(),
            style: AppTextStyles.bodyText1.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: AppTextStyles.captionText.copyWith(
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  CorrectionStats _calculateStats() {
    final totalCorrections = corrections.where((c) => c.hasCorrections).length;
    final totalSuggestions = corrections.fold<int>(
      0,
      (sum, correction) => sum + correction.suggestions.length,
    );
    final totalExplanations = corrections.fold<int>(
      0,
      (sum, correction) => sum + correction.explanations.length,
    );
    final totalProcessingTime = corrections.fold<double>(
      0.0,
      (sum, correction) => sum + correction.processingTime,
    );
    final averageProcessingTime = corrections.isNotEmpty 
        ? totalProcessingTime / corrections.length 
        : 0.0;
    
    // Calculate accuracy as percentage of corrections that had suggestions or explanations
    final helpfulCorrections = corrections.where((c) => 
        c.hasSuggestions || c.hasExplanations || c.hasCorrections).length;
    final accuracyPercentage = corrections.isNotEmpty 
        ? (helpfulCorrections / corrections.length) * 100 
        : 0.0;
    
    return CorrectionStats(
      totalCorrections: totalCorrections,
      totalSuggestions: totalSuggestions,
      totalExplanations: totalExplanations,
      averageProcessingTime: averageProcessingTime,
      accuracyPercentage: accuracyPercentage,
    );
  }
}

/// Data class for correction statistics
class CorrectionStats {
  final int totalCorrections;
  final int totalSuggestions;
  final int totalExplanations;
  final double averageProcessingTime;
  final double accuracyPercentage;
  
  const CorrectionStats({
    required this.totalCorrections,
    required this.totalSuggestions,
    required this.totalExplanations,
    required this.averageProcessingTime,
    required this.accuracyPercentage,
  });
}

/// Floating widget for quick correction access
class CorrectionResultFloatingWidget extends StatelessWidget {
  final List<CorrectionResult> recentCorrections;
  final VoidCallback? onTap;
  final bool isVisible;
  
  const CorrectionResultFloatingWidget({
    super.key,
    required this.recentCorrections,
    this.onTap,
    this.isVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible || recentCorrections.isEmpty) {
      return const SizedBox.shrink();
    }

    final latestCorrection = recentCorrections.first;
    
    return Positioned(
      top: 100,
      right: 16,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 200),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: AppGradients.correctionContainerGradient,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.slate600.withOpacity(0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowDark,
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                children: [
                  Icon(
                    Icons.psychology,
                    size: 14,
                    color: AppColors.infoBlue,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      'Latest Correction',
                      style: AppTextStyles.captionText.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 11,
                      ),
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                    color: AppColors.lightText.withOpacity(0.5),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Preview of correction
              if (latestCorrection.hasCorrections) ...[
                Text(
                  latestCorrection.originalText,
                  style: AppTextStyles.correctionOriginal.copyWith(fontSize: 11),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  latestCorrection.correctedText,
                  style: AppTextStyles.correctionCorrected.copyWith(fontSize: 11),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ] else if (latestCorrection.hasSuggestions) ...[
                Text(
                  latestCorrection.suggestions.first,
                  style: AppTextStyles.suggestionText.copyWith(fontSize: 11),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              
              // Count indicator
              if (recentCorrections.length > 1) ...[
                const SizedBox(height: 6),
                Text(
                  '+${recentCorrections.length - 1} more',
                  style: AppTextStyles.captionText.copyWith(
                    fontSize: 10,
                    color: AppColors.lightText.withOpacity(0.7),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}