import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/presentation/widgets/chat/correction_result_widget.dart';
import 'package:deutschkorrekt_flutter/data/models/correction_result.dart';

void main() {
  group('CorrectionResultWidget Tests', () {
    late CorrectionResult testCorrectionResult;
    
    setUp(() {
      testCorrectionResult = CorrectionResult(
        originalText: 'Ich bin **sehr** müde heute.',
        correctedText: 'Ich bin **sehr** müde **heute**.',
        suggestions: [
          'Consider using "heute" at the end for better flow',
          'The word "sehr" adds emphasis to your statement',
        ],
        explanations: [
          'In German, time expressions like "heute" typically come at the end of the sentence',
          'The adverb "sehr" intensifies the adjective "müde"',
        ],
        processingTime: 1.25,
      );
    });

    testWidgets('CorrectionResultWidget displays all components correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CorrectionResultWidget(
              correctionResult: testCorrectionResult,
              showAgentBadge: true,
              showProcessingTime: true,
            ),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Check if agent badge is displayed
      expect(find.text('German Correction Agent'), findsOneWidget);

      // Check if original and corrected text are displayed
      expect(find.text('Original'), findsOneWidget);
      expect(find.text('Corrected'), findsOneWidget);

      // Check if suggestions section is displayed
      expect(find.text('Suggestions'), findsOneWidget);

      // Check for suggestion content using RichText finder
      expect(find.byWidgetPredicate((widget) {
        if (widget is RichText) {
          final text = widget.text.toPlainText();
          return text.contains('Consider using "heute"');
        }
        return false;
      }), findsOneWidget);

      // Check if explanations section is displayed
      expect(find.text('Explanations'), findsOneWidget);

      // Check for explanation content using RichText finder
      expect(find.byWidgetPredicate((widget) {
        if (widget is RichText) {
          final text = widget.text.toPlainText();
          return text.contains('time expressions');
        }
        return false;
      }), findsOneWidget);

      // Check if processing time is displayed
      expect(find.textContaining('Processed in 1.25s'), findsOneWidget);
    });

    testWidgets('CorrectionResultWidget without agent badge', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CorrectionResultWidget(
              correctionResult: testCorrectionResult,
              showAgentBadge: false,
              showProcessingTime: false,
            ),
          ),
        ),
      );

      // Agent badge should not be displayed
      expect(find.text('German Correction Agent'), findsNothing);
      
      // Processing time should not be displayed
      expect(find.textContaining('Processed in'), findsNothing);
      
      // But correction content should still be there
      expect(find.text('Original'), findsOneWidget);
      expect(find.text('Corrected'), findsOneWidget);
    });

    testWidgets('CompactCorrectionResultWidget displays correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactCorrectionResultWidget(
              correctionResult: testCorrectionResult,
            ),
          ),
        ),
      );

      // Check if compact badge is displayed
      expect(find.text('Correction'), findsOneWidget);
      
      // Check if summary is displayed
      expect(find.textContaining('2 suggestions, 2 explanations'), findsOneWidget);
    });

    testWidgets('ExpandableCorrectionResultWidget expands and collapses', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExpandableCorrectionResultWidget(
              correctionResult: testCorrectionResult,
              initiallyExpanded: false,
            ),
          ),
        ),
      );

      // Wait for initial render
      await tester.pumpAndSettle();

      // Initially collapsed - should show header but not detailed content
      expect(find.text('German Correction Agent'), findsOneWidget);

      // Check that the SizeTransition has zero height (collapsed)
      final sizeTransition = tester.widget<SizeTransition>(find.byType(SizeTransition));
      expect(sizeTransition.sizeFactor.value, equals(0.0));

      // Tap on the header area to expand (find by the agent badge text)
      await tester.tap(find.text('German Correction Agent'));
      await tester.pumpAndSettle();

      // Now the SizeTransition should be expanded
      final expandedSizeTransition = tester.widget<SizeTransition>(find.byType(SizeTransition));
      expect(expandedSizeTransition.sizeFactor.value, equals(1.0));

      // And detailed content should be visible
      expect(find.text('Original'), findsOneWidget);
      expect(find.text('Suggestions'), findsOneWidget);

      // Tap to collapse
      await tester.tap(find.text('German Correction Agent'));
      await tester.pumpAndSettle();

      // SizeTransition should be collapsed again
      final collapsedSizeTransition = tester.widget<SizeTransition>(find.byType(SizeTransition));
      expect(collapsedSizeTransition.sizeFactor.value, equals(0.0));
    });

    testWidgets('CorrectionResultWidget handles empty corrections', (WidgetTester tester) async {
      final emptyCorrectionResult = CorrectionResult(
        originalText: '',
        correctedText: '',
        suggestions: [],
        explanations: [],
        processingTime: 0.5,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CorrectionResultWidget(
              correctionResult: emptyCorrectionResult,
            ),
          ),
        ),
      );

      // Agent badge should still be displayed
      expect(find.text('German Correction Agent'), findsOneWidget);
      
      // But correction sections should not be displayed
      expect(find.text('Original'), findsNothing);
      expect(find.text('Suggestions'), findsNothing);
      expect(find.text('Explanations'), findsNothing);
    });

    testWidgets('CorrectionResultWidget handles only suggestions', (WidgetTester tester) async {
      final suggestionsOnlyResult = CorrectionResult(
        originalText: '',
        correctedText: '',
        suggestions: ['This is a suggestion'],
        explanations: [],
        processingTime: 0.8,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CorrectionResultWidget(
              correctionResult: suggestionsOnlyResult,
            ),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Suggestions should be displayed
      expect(find.text('Suggestions'), findsOneWidget);

      // Check for suggestion content using RichText finder
      expect(find.byWidgetPredicate((widget) {
        if (widget is RichText) {
          final text = widget.text.toPlainText();
          return text.contains('This is a suggestion');
        }
        return false;
      }), findsOneWidget);

      // But corrections and explanations should not
      expect(find.text('Original'), findsNothing);
      expect(find.text('Explanations'), findsNothing);
    });

    testWidgets('CorrectionResultWidget handles only explanations', (WidgetTester tester) async {
      final explanationsOnlyResult = CorrectionResult(
        originalText: '',
        correctedText: '',
        suggestions: [],
        explanations: ['This is an explanation'],
        processingTime: 0.3,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CorrectionResultWidget(
              correctionResult: explanationsOnlyResult,
            ),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Explanations should be displayed
      expect(find.text('Explanations'), findsOneWidget);

      // Check for explanation content using RichText finder
      expect(find.byWidgetPredicate((widget) {
        if (widget is RichText) {
          final text = widget.text.toPlainText();
          return text.contains('This is an explanation');
        }
        return false;
      }), findsOneWidget);

      // But corrections and suggestions should not
      expect(find.text('Original'), findsNothing);
      expect(find.text('Suggestions'), findsNothing);
    });

    testWidgets('CorrectionResultWidget tap callback works', (WidgetTester tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CorrectionResultWidget(
              correctionResult: testCorrectionResult,
              onTap: () {
                tapped = true;
              },
            ),
          ),
        ),
      );

      // Tap the widget
      await tester.tap(find.byType(CorrectionResultWidget));
      await tester.pump();
      
      // Verify callback was called
      expect(tapped, isTrue);
    });
  });

  group('CorrectionResult Model Tests', () {
    testWidgets('CorrectionResult hasCorrections property', (WidgetTester tester) async {
      final withCorrections = CorrectionResult(
        originalText: 'Original',
        correctedText: 'Corrected',
        suggestions: [],
        explanations: [],
        processingTime: 1.0,
      );
      
      final withoutCorrections = CorrectionResult(
        originalText: '',
        correctedText: '',
        suggestions: [],
        explanations: [],
        processingTime: 1.0,
      );
      
      expect(withCorrections.hasCorrections, isTrue);
      expect(withoutCorrections.hasCorrections, isFalse);
    });

    testWidgets('CorrectionResult hasSuggestions property', (WidgetTester tester) async {
      final withSuggestions = CorrectionResult(
        originalText: '',
        correctedText: '',
        suggestions: ['Suggestion'],
        explanations: [],
        processingTime: 1.0,
      );
      
      final withoutSuggestions = CorrectionResult(
        originalText: '',
        correctedText: '',
        suggestions: [],
        explanations: [],
        processingTime: 1.0,
      );
      
      expect(withSuggestions.hasSuggestions, isTrue);
      expect(withoutSuggestions.hasSuggestions, isFalse);
    });

    testWidgets('CorrectionResult hasExplanations property', (WidgetTester tester) async {
      final withExplanations = CorrectionResult(
        originalText: '',
        correctedText: '',
        suggestions: [],
        explanations: ['Explanation'],
        processingTime: 1.0,
      );
      
      final withoutExplanations = CorrectionResult(
        originalText: '',
        correctedText: '',
        suggestions: [],
        explanations: [],
        processingTime: 1.0,
      );
      
      expect(withExplanations.hasExplanations, isTrue);
      expect(withoutExplanations.hasExplanations, isFalse);
    });
  });
}