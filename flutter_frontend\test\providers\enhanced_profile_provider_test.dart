import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/presentation/providers/enhanced_profile_provider.dart';
import 'package:deutschkorrekt_flutter/data/models/user_profile.dart';

void main() {
  group('EnhancedProfileProvider', () {
    late EnhancedProfileProvider profileProvider;

    setUp(() {
      profileProvider = EnhancedProfileProvider();
    });

    tearDown(() {
      profileProvider.dispose();
    });

    group('Initial state', () {
      test('should have correct initial state', () {
        expect(profileProvider.userProfile, isNull);
        expect(profileProvider.isLoading, isFalse);
        expect(profileProvider.errorMessage, isNull);
        expect(profileProvider.isInitialized, isFalse);
      });

      test('should have correct convenience getters with null profile', () {
        expect(profileProvider.email, isNull);
        expect(profileProvider.plan, equals('Trial'));
        expect(profileProvider.currentCredits, equals(0));
        expect(profileProvider.maxCredits, equals(20));
        expect(profileProvider.hasCreditsAvailable, isFalse);
        expect(profileProvider.nextRefreshDate, isNull);
        expect(profileProvider.needsRefresh, isFalse);
      });
    });

    group('Profile management', () {
      test('should initialize correctly', () async {
        await profileProvider.initialize();
        expect(profileProvider.isInitialized, isTrue);
      });

      test('should provide correct convenience getters with profile', () {
        final testProfile = UserProfile(
          email: '<EMAIL>',
          plan: 'Premium',
          dateJoined: DateTime(2024, 1, 1),
          datePlan: DateTime(2024, 1, 15),
          maxCredits: 50,
          currentCredits: 30,
        );

        // Simulate setting a profile (in real implementation this would come from database)
        profileProvider.userProfile = testProfile;

        expect(profileProvider.email, equals('<EMAIL>'));
        expect(profileProvider.plan, equals('Premium'));
        expect(profileProvider.currentCredits, equals(30));
        expect(profileProvider.maxCredits, equals(50));
        expect(profileProvider.hasCreditsAvailable, isTrue);
        expect(profileProvider.creditsUsagePercentage, equals(0.4)); // (50-30)/50
        expect(profileProvider.remainingCreditsPercentage, equals(0.6)); // 30/50
      });
    });

    group('Credits management', () {
      test('should check if user can make request', () {
        expect(profileProvider.canMakeRequest(), isFalse);

        // Simulate profile with credits
        final profileWithCredits = UserProfile(
          email: '<EMAIL>',
          plan: 'Trial',
          dateJoined: DateTime.now(),
          datePlan: DateTime.now(),
          maxCredits: 20,
          currentCredits: 10,
        );

        profileProvider.userProfile = profileWithCredits;
        expect(profileProvider.canMakeRequest(), isTrue);

        // Simulate profile without credits
        final profileWithoutCredits = profileWithCredits.copyWith(currentCredits: 0);
        profileProvider.userProfile = profileWithoutCredits;
        expect(profileProvider.canMakeRequest(), isFalse);
      });

      test('should provide correct credits status message', () {
        expect(profileProvider.getCreditsStatusMessage(), equals('Profile not loaded'));

        // Profile with credits
        final profileWithCredits = UserProfile(
          email: '<EMAIL>',
          plan: 'Trial',
          dateJoined: DateTime.now(),
          datePlan: DateTime.now(),
          maxCredits: 20,
          currentCredits: 15,
        );

        profileProvider.userProfile = profileWithCredits;
        expect(profileProvider.getCreditsStatusMessage(), equals('15 of 20 credits remaining'));

        // Profile without credits
        final profileWithoutCredits = profileWithCredits.copyWith(currentCredits: 0);
        profileProvider.userProfile = profileWithoutCredits;
        final statusMessage = profileProvider.getCreditsStatusMessage();
        expect(statusMessage, contains('Credits refresh'));
      });
    });

    group('Utility methods', () {
      test('should get days since joined', () {
        final joinDate = DateTime.now().subtract(const Duration(days: 30));
        final profile = UserProfile(
          email: '<EMAIL>',
          plan: 'Trial',
          dateJoined: joinDate,
          datePlan: DateTime.now(),
          maxCredits: 20,
          currentCredits: 15,
        );

        profileProvider.userProfile = profile;
        expect(profileProvider.getDaysSinceJoined(), equals(30));
      });

      test('should get plan color', () {
        final trialProfile = UserProfile(
          email: '<EMAIL>',
          plan: 'Trial',
          dateJoined: DateTime.now(),
          datePlan: DateTime.now(),
          maxCredits: 20,
          currentCredits: 15,
        );

        profileProvider.userProfile = trialProfile;
        final color = profileProvider.getPlanColor();
        expect(color.value, equals(0xFF9E9E9E)); // Grey for trial

        final premiumProfile = trialProfile.copyWith(plan: 'Premium');
        profileProvider.userProfile = premiumProfile;
        final premiumColor = profileProvider.getPlanColor();
        expect(premiumColor.value, equals(0xFF4CAF50)); // Green for premium
      });

      test('should get formatted refresh date', () {
        expect(profileProvider.getFormattedRefreshDate(), equals('Unknown'));

        final profile = UserProfile(
          email: '<EMAIL>',
          plan: 'Trial',
          dateJoined: DateTime.now(),
          datePlan: DateTime.now(),
          maxCredits: 20,
          currentCredits: 15,
        );

        profileProvider.userProfile = profile;
        final formattedDate = profileProvider.getFormattedRefreshDate();
        expect(formattedDate, isNotEmpty);
      });

      test('should get profile summary', () {
        final summary = profileProvider.getProfileSummary();
        expect(summary['email'], equals('Not loaded'));
        expect(summary['plan'], equals('Unknown'));
        expect(summary['credits'], equals('0/0'));

        final profile = UserProfile(
          email: '<EMAIL>',
          plan: 'Premium',
          dateJoined: DateTime.now().subtract(const Duration(days: 10)),
          datePlan: DateTime.now(),
          maxCredits: 50,
          currentCredits: 30,
        );

        profileProvider.userProfile = profile;
        final profileSummary = profileProvider.getProfileSummary();
        expect(profileSummary['email'], equals('<EMAIL>'));
        expect(profileSummary['plan'], equals('Premium'));
        expect(profileSummary['credits'], equals('30/50'));
        expect(profileSummary['daysJoined'], equals(10));
      });
    });

    group('State management', () {
      test('should clear profile', () {
        final profile = UserProfile(
          email: '<EMAIL>',
          plan: 'Trial',
          dateJoined: DateTime.now(),
          datePlan: DateTime.now(),
          maxCredits: 20,
          currentCredits: 15,
        );

        profileProvider.userProfile = profile;
        expect(profileProvider.userProfile, isNotNull);

        profileProvider.clearProfile();
        expect(profileProvider.userProfile, isNull);
        expect(profileProvider.errorMessage, isNull);
      });
    });
  });
}