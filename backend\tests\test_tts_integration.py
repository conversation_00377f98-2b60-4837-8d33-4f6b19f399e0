"""
Integration tests for TTS functionality with actual Google Cloud TTS API.
These tests require proper Google Cloud credentials and should be run in environments
where the TTS API is accessible and configured.

Tests include:
- End-to-end TTS workflow with real API calls
- Performance testing with actual API latency
- Error handling with real API error responses
- Audio quality and format validation
- Rate limiting and quota management
"""

import pytest
import asyncio
import os
import time
from unittest.mock import patch
from google.cloud import texttospeech
from google.api_core import exceptions as gcp_exceptions

from services.google_tts_client import GoogleTTSClient
from services.tts_service import TTSService


# Skip integration tests if Google Cloud credentials are not available
pytestmark = pytest.mark.skipif(
    not os.environ.get("GOOGLE_APPLICATION_CREDENTIALS") and 
    not os.environ.get("GOOGLE_CLOUD_PROJECT"),
    reason="Google Cloud credentials not available for integration tests"
)


class TestTTSIntegration:
    """Integration test suite for TTS functionality with real Google Cloud API."""

    @pytest.fixture(scope="class")
    def real_google_tts_client(self):
        """Create a real GoogleTTSClient instance for integration testing."""
        try:
            return GoogleTTSClient()
        except Exception as e:
            pytest.skip(f"Cannot initialize Google TTS client: {e}")

    @pytest.fixture(scope="class")
    def real_tts_service(self, real_google_tts_client):
        """Create a real TTSService instance for integration testing."""
        with patch('services.tts_service.google_tts_client', real_google_tts_client):
            return TTSService()

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_generation_simple_german(self, real_google_tts_client):
        """Test real TTS generation with simple German text."""
        text = "Hallo, das ist ein Test."
        
        result = await real_google_tts_client.generate_speech(text)
        
        assert result["audio_data"] is not None
        assert len(result["audio_data"]) > 0
        assert result["content_type"] == "audio/mpeg"
        assert result["duration_seconds"] > 0
        assert result["processing_time"] > 0
        
        # Verify audio data starts with MP3 header
        assert result["audio_data"][:3] == b"ID3" or result["audio_data"][:2] == b"\xff\xfb"

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_generation_complex_german(self, real_google_tts_client):
        """Test real TTS generation with complex German text including special characters."""
        text = "Äpfel, Öl und Übergänge sind schön in München. Die Größe beträgt 19,99€."
        
        result = await real_google_tts_client.generate_speech(text)
        
        assert result["audio_data"] is not None
        assert len(result["audio_data"]) > 0
        assert result["content_type"] == "audio/mpeg"
        assert result["duration_seconds"] > 2.0  # Should be longer for more text

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_generation_long_text(self, real_google_tts_client):
        """Test real TTS generation with longer German text."""
        text = ("Die deutsche Sprache ist eine sehr interessante und komplexe Sprache. "
                "Sie hat viele Regeln und Ausnahmen, die das Lernen herausfordernd machen. "
                "Aber mit Übung und Geduld kann man sie erfolgreich meistern.")
        
        result = await real_google_tts_client.generate_speech(text)
        
        assert result["audio_data"] is not None
        assert len(result["audio_data"]) > 0
        assert result["duration_seconds"] > 5.0  # Should be longer for more text
        
        # Verify reasonable file size (should be larger for longer text)
        assert len(result["audio_data"]) > 10000  # At least 10KB for this length

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_service_end_to_end(self, real_tts_service):
        """Test complete TTS service workflow with real API."""
        text = "Das ist ein vollständiger Test der TTS-Funktionalität."
        
        result = await real_tts_service.generate_speech(text, "integration-test-123")
        
        assert result["success"] is True
        assert result["audio_data"] is not None
        assert len(result["audio_data"]) > 0
        assert result["content_type"] == "audio/mpeg"
        assert result["duration_seconds"] > 0
        assert result["processing_time"] > 0
        assert result["original_text"] == text
        assert result["sanitized_text"] == text

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_service_with_html_sanitization(self, real_tts_service):
        """Test TTS service with HTML content that needs sanitization."""
        text = "Das ist <b>fetter</b> Text und <i>kursiver</i> Text."
        expected_sanitized = "Das ist fetter Text und kursiver Text."
        
        result = await real_tts_service.generate_speech(text, "sanitization-test-123")
        
        assert result["success"] is True
        assert result["audio_data"] is not None
        assert result["original_text"] == text
        assert result["sanitized_text"] == expected_sanitized

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_performance_timing(self, real_google_tts_client):
        """Test TTS performance with real API calls."""
        text = "Performance-Test für die TTS-API."
        
        start_time = time.time()
        result = await real_google_tts_client.generate_speech(text)
        total_time = time.time() - start_time
        
        # Verify timing accuracy
        assert abs(result["processing_time"] - total_time) < 0.1  # Within 100ms
        
        # Performance expectations (may vary based on network)
        assert result["processing_time"] < 10.0  # Should complete within 10 seconds
        assert result["processing_time"] > 0.1   # Should take at least 100ms for real API

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_concurrent_requests(self, real_google_tts_client):
        """Test concurrent TTS requests to verify thread safety."""
        texts = [
            "Erster gleichzeitiger Test.",
            "Zweiter gleichzeitiger Test.",
            "Dritter gleichzeitiger Test."
        ]
        
        # Make concurrent requests
        tasks = [real_google_tts_client.generate_speech(text) for text in texts]
        results = await asyncio.gather(*tasks)
        
        # Verify all requests succeeded
        assert len(results) == 3
        for i, result in enumerate(results):
            assert result["audio_data"] is not None
            assert len(result["audio_data"]) > 0
            assert result["content_type"] == "audio/mpeg"

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_voice_configuration(self, real_google_tts_client):
        """Test that the configured voice is actually being used."""
        text = "Test der Stimmen-Konfiguration."
        
        result = await real_google_tts_client.generate_speech(text)
        
        # Verify client configuration
        client_info = real_google_tts_client.get_client_info()
        assert client_info["voice_name"] == "de-DE-Chirp3-HD-Aoede"
        assert client_info["language_code"] == "de-DE"
        assert client_info["speech_speed"] == 0.9
        assert client_info["audio_format"] == "MP3"
        assert client_info["sample_rate"] == 24000

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_audio_format_validation(self, real_google_tts_client):
        """Test that generated audio has the correct format and properties."""
        text = "Audio-Format-Validierung für MP3."
        
        result = await real_google_tts_client.generate_speech(text)
        
        audio_data = result["audio_data"]
        
        # Basic MP3 format validation
        # MP3 files typically start with ID3 tag or sync frame
        assert len(audio_data) > 100  # Should have reasonable size
        assert (audio_data[:3] == b"ID3" or  # ID3 tag
                audio_data[:2] == b"\xff\xfb" or  # MP3 sync frame
                audio_data[:2] == b"\xff\xfa")    # Alternative MP3 sync frame

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_health_check(self, real_tts_service):
        """Test TTS service health check with real API."""
        result = await real_tts_service.health_check()
        
        assert result["healthy"] is True
        assert result["response_time"] > 0
        assert result["audio_size"] > 0
        assert "operational" in result["message"]

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_error_handling_invalid_text(self, real_google_tts_client):
        """Test error handling with invalid text that would cause API errors."""
        # Test with extremely long text that might exceed API limits
        very_long_text = "Test " * 1000  # Very long text
        
        with pytest.raises((ValueError, Exception)):
            await real_google_tts_client.generate_speech(very_long_text)

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_rate_limiting_simulation(self, real_google_tts_client):
        """Test behavior under rapid successive requests (rate limiting simulation)."""
        text = "Rate-Limiting-Test."
        
        # Make several rapid requests
        start_time = time.time()
        results = []
        
        for i in range(5):
            try:
                result = await real_google_tts_client.generate_speech(f"{text} Nummer {i+1}")
                results.append(result)
            except Exception as e:
                # Rate limiting or quota errors are acceptable in this test
                if "quota" in str(e).lower() or "rate" in str(e).lower():
                    break
                else:
                    raise
        
        total_time = time.time() - start_time
        
        # Should have completed at least some requests
        assert len(results) > 0
        
        # All successful results should be valid
        for result in results:
            assert result["audio_data"] is not None
            assert len(result["audio_data"]) > 0

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_duration_accuracy(self, real_google_tts_client):
        """Test accuracy of duration estimation compared to actual audio length."""
        # Use text with known approximate speaking time
        text = "Dies ist ein Test zur Überprüfung der Dauer-Schätzung der Sprachsynthese."
        
        result = await real_google_tts_client.generate_speech(text)
        
        estimated_duration = result["duration_seconds"]
        
        # Duration should be reasonable for the text length
        # Rough estimate: ~150 words per minute, adjusted for speech speed
        word_count = len(text.split())
        expected_duration = (word_count / 150) * 60 / 0.9  # Adjusted for 0.9 speed
        
        # Allow for reasonable variance in estimation
        assert abs(estimated_duration - expected_duration) < expected_duration * 0.5

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_special_characters_handling(self, real_google_tts_client):
        """Test handling of various special characters and symbols."""
        text = "Sonderzeichen: äöüß, Zahlen: 123, Symbole: €@#%, Datum: 01.01.2024"
        
        result = await real_google_tts_client.generate_speech(text)
        
        assert result["audio_data"] is not None
        assert len(result["audio_data"]) > 0
        assert result["content_type"] == "audio/mpeg"

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_tts_punctuation_handling(self, real_google_tts_client):
        """Test handling of various punctuation marks."""
        text = "Interpunktion: Hallo! Wie geht es? Das ist großartig... Wirklich?"
        
        result = await real_google_tts_client.generate_speech(text)
        
        assert result["audio_data"] is not None
        assert len(result["audio_data"]) > 0
        # Duration should reflect pauses for punctuation
        assert result["duration_seconds"] > 2.0

    @pytest.mark.integration
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_real_tts_stress_test(self, real_google_tts_client):
        """Stress test with multiple varied requests."""
        test_texts = [
            "Kurzer Test.",
            "Mittellanger Test mit mehr Wörtern und Inhalt.",
            "Sehr langer Test mit vielen Wörtern, Satzzeichen und verschiedenen Inhalten, um die Robustheit zu testen.",
            "Test mit Zahlen: 123, 456, 789 und Symbolen: €, @, #, %.",
            "Äpfel, Öl, Übergänge - deutsche Sonderzeichen im Test."
        ]
        
        results = []
        for i, text in enumerate(test_texts):
            try:
                result = await real_google_tts_client.generate_speech(text)
                results.append(result)
                
                # Small delay to avoid overwhelming the API
                await asyncio.sleep(0.5)
                
            except Exception as e:
                # Log the error but continue with other tests
                print(f"Stress test failed for text {i}: {e}")
        
        # Should have succeeded for most texts
        assert len(results) >= len(test_texts) * 0.8  # At least 80% success rate
        
        # All successful results should be valid
        for result in results:
            assert result["audio_data"] is not None
            assert len(result["audio_data"]) > 0
            assert result["content_type"] == "audio/mpeg"