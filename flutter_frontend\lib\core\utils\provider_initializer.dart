import 'package:flutter/foundation.dart';
import '../../presentation/providers/settings_provider.dart';
import '../../presentation/providers/profile_provider.dart';
import '../../presentation/providers/audio_provider.dart';
import '../../presentation/providers/chat_provider.dart';
import '../../data/models/app_error.dart';
import 'provider_error_handler.dart';

/// Utility class for initializing providers in the correct order
class ProviderInitializer {
  static bool _isInitialized = false;
  
  /// Initialize all providers in the correct dependency order
  static Future<void> initializeProviders({
    required SettingsProvider settingsProvider,
    required ProfileProvider profileProvider,
    required AudioProvider audioProvider,
    required ChatProvider chatProvider,
  }) async {
    if (_isInitialized) return;
    
    // Reset error handler state
    ProviderErrorHandler.resetAll();
    
    try {
      // Step 1: Initialize settings provider first (no dependencies)
      if (kDebugMode) {
        print('Initializing SettingsProvider...');
      }
      await _initializeWithErrorHandling(
        'settings',
        () => settingsProvider.initialize(),
      );
      
      // Step 2: Initialize profile provider (no dependencies)
      if (kDebugMode) {
        print('Initializing ProfileProvider...');
      }
      await _initializeWithErrorHandling(
        'profile',
        () => profileProvider.initialize(),
      );
      
      // Step 3: Initialize audio provider with settings configuration
      if (kDebugMode) {
        print('Initializing AudioProvider...');
      }
      await _initializeWithErrorHandling(
        'audio',
        () => audioProvider.initialize(config: settingsProvider.audioConfig),
      );
      
      // Step 4: Initialize chat provider (depends on all others)
      if (kDebugMode) {
        print('Initializing ChatProvider...');
      }
      await _initializeWithErrorHandling(
        'chat',
        () => chatProvider.initialize(),
      );
      
      // Update chat provider dependencies
      chatProvider.updateDependencies(
        settingsProvider,
        profileProvider,
        audioProvider,
      );
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('All providers initialized successfully');
        print('System health: ${ProviderErrorHandler.getSystemHealth()}');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing providers: $e');
        print('Error stats: ${ProviderErrorHandler.getErrorStats()}');
      }
      rethrow;
    }
  }
  
  /// Initialize a provider with error handling and retry logic
  static Future<void> _initializeWithErrorHandling(
    String providerId,
    Future<void> Function() initAction,
  ) async {
    try {
      await initAction();
    } catch (e) {
      final appError = AppError.unknown(
        details: 'Failed to initialize $providerId provider: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      
      final success = await ProviderErrorHandler.handleProviderError(
        providerId: providerId,
        error: appError,
        retryAction: initAction,
      );
      
      if (!success) {
        throw Exception('Failed to initialize $providerId provider after retries: $e');
      }
    }
  }
  
  /// Check if providers are initialized
  static bool get isInitialized => _isInitialized;
  
  /// Reset initialization state (for testing)
  static void reset() {
    _isInitialized = false;
  }
  
  /// Get initialization statistics
  static Map<String, dynamic> getStats({
    required SettingsProvider settingsProvider,
    required ProfileProvider profileProvider,
    required AudioProvider audioProvider,
    required ChatProvider chatProvider,
  }) {
    return {
      'isInitialized': _isInitialized,
      'settingsInitialized': settingsProvider.isInitialized,
      'profileInitialized': profileProvider.isInitialized,
      'audioInitialized': audioProvider.isInitialized,
      'chatInitialized': chatProvider.state.isInitialized,
      'settingsStats': settingsProvider.getStats(),
      'profileStats': profileProvider.getProfileStats(),
      'audioStats': audioProvider.getStats(),
      'chatStats': chatProvider.getStats(),
    };
  }
}