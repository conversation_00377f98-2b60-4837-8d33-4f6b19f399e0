import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/app_error.dart';

/// Widget for displaying WebSocket errors with proper styling and retry options
class WebSocketErrorWidget extends StatelessWidget {
  final AppError error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showRetryButton;
  final bool showDismissButton;
  final EdgeInsets? padding;
  
  const WebSocketErrorWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.onDismiss,
    this.showRetryButton = true,
    this.showDismissButton = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: _getBackgroundColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getBackgroundColor().withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Error header
          Row(
            children: [
              Icon(
                _getErrorIcon(),
                color: _getIconColor(),
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _getErrorTitle(),
                  style: AppTextStyles.messageText.copyWith(
                    color: _getTextColor(),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (showDismissButton && onDismiss != null)
                IconButton(
                  onPressed: onDismiss,
                  icon: Icon(
                    Icons.close,
                    color: _getTextColor().withOpacity(0.7),
                    size: 18,
                  ),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Error message
          Text(
            error.userMessage,
            style: AppTextStyles.captionText.copyWith(
              color: _getTextColor().withOpacity(0.8),
            ),
          ),
          
          // Error details (for debugging)
          if (error.details.isNotEmpty && error.details != error.userMessage) ...[
            const SizedBox(height: 6),
            Text(
              error.details,
              style: AppTextStyles.captionText.copyWith(
                fontSize: 11,
                color: _getTextColor().withOpacity(0.6),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
          
          // Error code and retry info
          if (error.errorCode != null || error.retryAfter != null) ...[
            const SizedBox(height: 8),
            _buildErrorMetadata(),
          ],
          
          // Action buttons
          if (showRetryButton || _shouldShowAdditionalActions()) ...[
            const SizedBox(height: 12),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }
  
  Widget _buildErrorMetadata() {
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: [
        if (error.errorCode != null)
          _buildMetadataChip('Code: ${error.errorCode}'),
        if (error.retryAfter != null)
          _buildMetadataChip('Retry in: ${error.retryAfter!.inSeconds}s'),
        if (error.isRecoverable)
          _buildMetadataChip('Recoverable', color: AppColors.successGreen),
      ],
    );
  }
  
  Widget _buildMetadataChip(String text, {Color? color}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: (color ?? _getBackgroundColor()).withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        text,
        style: AppTextStyles.captionText.copyWith(
          fontSize: 10,
          color: color ?? _getTextColor(),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
  
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Additional action buttons based on error type
        ..._getAdditionalActionButtons(),
        
        // Retry button
        if (showRetryButton && onRetry != null && error.isRecoverable)
          TextButton.icon(
            onPressed: onRetry,
            icon: Icon(
              Icons.refresh,
              size: 16,
              color: _getTextColor(),
            ),
            label: Text(
              'Retry',
              style: AppTextStyles.smallButtonText.copyWith(
                color: _getTextColor(),
              ),
            ),
          ),
      ],
    );
  }
  
  List<Widget> _getAdditionalActionButtons() {
    final buttons = <Widget>[];
    
    switch (error.type) {
      case AppErrorType.microphonePermissionDenied:
        buttons.add(
          TextButton.icon(
            onPressed: () => _openAppSettings(),
            icon: Icon(
              Icons.settings,
              size: 16,
              color: _getTextColor(),
            ),
            label: Text(
              'Settings',
              style: AppTextStyles.smallButtonText.copyWith(
                color: _getTextColor(),
              ),
            ),
          ),
        );
        break;
      case AppErrorType.websocketConnectionFailed:
        buttons.add(
          TextButton.icon(
            onPressed: () => _checkNetworkStatus(),
            icon: Icon(
              Icons.network_check,
              size: 16,
              color: _getTextColor(),
            ),
            label: Text(
              'Check Network',
              style: AppTextStyles.smallButtonText.copyWith(
                color: _getTextColor(),
              ),
            ),
          ),
        );
        break;
      default:
        break;
    }
    
    return buttons;
  }
  
  bool _shouldShowAdditionalActions() {
    return error.type == AppErrorType.microphonePermissionDenied ||
           error.type == AppErrorType.websocketConnectionFailed;
  }
  
  IconData _getErrorIcon() {
    switch (error.type) {
      case AppErrorType.microphonePermissionDenied:
        return Icons.mic_off;
      case AppErrorType.websocketConnectionFailed:
        return Icons.wifi_off;
      case AppErrorType.audioRecordingFailed:
        return Icons.record_voice_over;
      case AppErrorType.transcriptionTimeout:
        return Icons.timer_off;
      case AppErrorType.backendError:
        return Icons.cloud_off;
      case AppErrorType.networkError:
        return Icons.signal_wifi_off;
      case AppErrorType.unknown:
      default:
        return Icons.error_outline;
    }
  }
  
  String _getErrorTitle() {
    switch (error.type) {
      case AppErrorType.microphonePermissionDenied:
        return 'Microphone Access Required';
      case AppErrorType.websocketConnectionFailed:
        return 'Connection Failed';
      case AppErrorType.audioRecordingFailed:
        return 'Recording Error';
      case AppErrorType.transcriptionTimeout:
        return 'Request Timed Out';
      case AppErrorType.backendError:
        return 'Server Error';
      case AppErrorType.networkError:
        return 'Network Error';
      case AppErrorType.unknown:
      default:
        return 'Error Occurred';
    }
  }
  
  Color _getBackgroundColor() {
    switch (error.type) {
      case AppErrorType.microphonePermissionDenied:
        return AppColors.warningYellow;
      case AppErrorType.websocketConnectionFailed:
      case AppErrorType.networkError:
        return AppColors.infoBlue;
      case AppErrorType.transcriptionTimeout:
        return AppColors.warningYellow;
      case AppErrorType.audioRecordingFailed:
      case AppErrorType.backendError:
      case AppErrorType.unknown:
      default:
        return AppColors.errorRed;
    }
  }
  
  Color _getIconColor() {
    return _getBackgroundColor();
  }
  
  Color _getTextColor() {
    return _getBackgroundColor();
  }
  
  void _openAppSettings() {
    // TODO: Implement opening app settings
    print('Opening app settings for microphone permissions');
  }
  
  void _checkNetworkStatus() {
    // TODO: Implement network status check
    print('Checking network status');
  }
}

/// Compact error widget for smaller spaces
class CompactWebSocketErrorWidget extends StatelessWidget {
  final AppError error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  
  const CompactWebSocketErrorWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _getColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: _getColor().withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getIcon(),
            color: _getColor(),
            size: 16,
          ),
          const SizedBox(width: 6),
          Flexible(
            child: Text(
              _getShortMessage(),
              style: AppTextStyles.captionText.copyWith(
                color: _getColor(),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (onRetry != null && error.isRecoverable) ...[
            const SizedBox(width: 6),
            GestureDetector(
              onTap: onRetry,
              child: Icon(
                Icons.refresh,
                color: _getColor(),
                size: 14,
              ),
            ),
          ],
          if (onDismiss != null) ...[
            const SizedBox(width: 6),
            GestureDetector(
              onTap: onDismiss,
              child: Icon(
                Icons.close,
                color: _getColor().withOpacity(0.7),
                size: 14,
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  IconData _getIcon() {
    switch (error.type) {
      case AppErrorType.microphonePermissionDenied:
        return Icons.mic_off;
      case AppErrorType.websocketConnectionFailed:
        return Icons.wifi_off;
      case AppErrorType.audioRecordingFailed:
        return Icons.record_voice_over;
      case AppErrorType.transcriptionTimeout:
        return Icons.timer_off;
      case AppErrorType.backendError:
        return Icons.cloud_off;
      case AppErrorType.networkError:
        return Icons.signal_wifi_off;
      case AppErrorType.unknown:
      default:
        return Icons.error;
    }
  }
  
  String _getShortMessage() {
    switch (error.type) {
      case AppErrorType.microphonePermissionDenied:
        return 'Mic access needed';
      case AppErrorType.websocketConnectionFailed:
        return 'Connection failed';
      case AppErrorType.audioRecordingFailed:
        return 'Recording error';
      case AppErrorType.transcriptionTimeout:
        return 'Timed out';
      case AppErrorType.backendError:
        return 'Server error';
      case AppErrorType.networkError:
        return 'Network error';
      case AppErrorType.unknown:
      default:
        return 'Error occurred';
    }
  }
  
  Color _getColor() {
    switch (error.type) {
      case AppErrorType.microphonePermissionDenied:
        return AppColors.warningYellow;
      case AppErrorType.websocketConnectionFailed:
      case AppErrorType.networkError:
        return AppColors.infoBlue;
      case AppErrorType.transcriptionTimeout:
        return AppColors.warningYellow;
      case AppErrorType.audioRecordingFailed:
      case AppErrorType.backendError:
      case AppErrorType.unknown:
      default:
        return AppColors.errorRed;
    }
  }
}

/// Error toast widget for temporary error notifications
class ErrorToastWidget extends StatefulWidget {
  final AppError error;
  final Duration duration;
  final VoidCallback? onDismiss;
  
  const ErrorToastWidget({
    super.key,
    required this.error,
    this.duration = const Duration(seconds: 4),
    this.onDismiss,
  });

  @override
  State<ErrorToastWidget> createState() => _ErrorToastWidgetState();
}

class _ErrorToastWidgetState extends State<ErrorToastWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));
    
    _controller.forward();
    
    // Auto-dismiss after duration
    Future.delayed(widget.duration, () {
      if (mounted) {
        _dismiss();
      }
    });
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  void _dismiss() {
    _controller.reverse().then((_) {
      widget.onDismiss?.call();
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          margin: const EdgeInsets.all(16),
          child: CompactWebSocketErrorWidget(
            error: widget.error,
            onDismiss: _dismiss,
          ),
        ),
      ),
    );
  }
}