import 'package:flutter/material.dart';
import 'colors.dart';

/// Gradient definitions matching the Expo version exactly
class AppGradients {
  static const LinearGradient mainBackground = LinearGradient(
    colors: [
      AppColors.slate600,
      AppColors.slate700,
      AppColors.slate800,
      AppColors.slate900,
      AppColors.slate950,
    ],
    begin: Alignment(0, 1),
    end: Alignment(1, 0),
  );
  
  static const LinearGradient headerGradient = LinearGradient(
    colors: [AppColors.slate900, AppColors.slate800],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  static const LinearGradient userMessageGradient = LinearGradient(
    colors: [AppColors.slate600, AppColors.slate700],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient microphoneActiveGradient = LinearGradient(
    colors: [AppColors.errorRed, Color(0xFFdc2626)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient agentBadgeGradient = LinearGradient(
    colors: [AppColors.slate700, AppColors.slate800],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient microphoneBottomGradient = LinearGradient(
    colors: [AppColors.slate800, AppColors.slate900],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  static const LinearGradient modalOverlayGradient = LinearGradient(
    colors: [AppColors.slate900, AppColors.slate950],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  static const LinearGradient correctionContainerGradient = LinearGradient(
    colors: [AppColors.slate800, AppColors.slate700],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Private constructor to prevent instantiation
  const AppGradients._();
}