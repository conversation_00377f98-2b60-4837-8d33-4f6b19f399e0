/// Model for user profile data and statistics
class ProfileData {
  final String userName;
  final String userLevel;
  final List<StatItem> stats;
  final List<LearningGoal> goals;
  final List<ActivityItem> recentActivity;
  final String avatarUrl;
  final DateTime joinDate;
  
  const ProfileData({
    required this.userName,
    required this.userLevel,
    required this.stats,
    required this.goals,
    required this.recentActivity,
    this.avatarUrl = '',
    required this.joinDate,
  });
  
  /// Create from JSON response
  factory ProfileData.fromJson(Map<String, dynamic> json) {
    return ProfileData(
      userName: json['user_name'] ?? 'User',
      userLevel: json['user_level'] ?? 'Beginner',
      stats: (json['stats'] as List<dynamic>?)
          ?.map((stat) => StatItem.fromJson(stat))
          .toList() ?? [],
      goals: (json['goals'] as List<dynamic>?)
          ?.map((goal) => LearningGoal.fromJson(goal))
          .toList() ?? [],
      recentActivity: (json['recent_activity'] as List<dynamic>?)
          ?.map((activity) => ActivityItem.fromJson(activity))
          .toList() ?? [],
      avatarUrl: json['avatar_url'] ?? '',
      joinDate: DateTime.tryParse(json['join_date'] ?? '') ?? DateTime.now(),
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'user_name': userName,
      'user_level': userLevel,
      'stats': stats.map((stat) => stat.toJson()).toList(),
      'goals': goals.map((goal) => goal.toJson()).toList(),
      'recent_activity': recentActivity.map((activity) => activity.toJson()).toList(),
      'avatar_url': avatarUrl,
      'join_date': joinDate.toIso8601String(),
    };
  }
  
  /// Get total corrections count
  int get totalCorrections {
    final correctionsStat = stats.firstWhere(
      (stat) => stat.key == 'corrections',
      orElse: () => const StatItem(key: 'corrections', value: '0', label: 'Corrections'),
    );
    return int.tryParse(correctionsStat.value) ?? 0;
  }
  
  /// Get improvement percentage
  double get improvementPercentage {
    final improvementStat = stats.firstWhere(
      (stat) => stat.key == 'improvement',
      orElse: () => const StatItem(key: 'improvement', value: '0.0', label: 'Improvement'),
    );
    return double.tryParse(improvementStat.value) ?? 0.0;
  }
  
  @override
  String toString() {
    return 'ProfileData(userName: $userName, level: $userLevel, stats: ${stats.length})';
  }
}

/// Model for user statistics
class StatItem {
  final String key;
  final String value;
  final String label;
  final String? unit;
  final String? icon;
  
  const StatItem({
    required this.key,
    required this.value,
    required this.label,
    this.unit,
    this.icon,
  });
  
  /// Create from JSON
  factory StatItem.fromJson(Map<String, dynamic> json) {
    return StatItem(
      key: json['key'] ?? '',
      value: json['value']?.toString() ?? '0',
      label: json['label'] ?? '',
      unit: json['unit'],
      icon: json['icon'],
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'value': value,
      'label': label,
      'unit': unit,
      'icon': icon,
    };
  }
  
  @override
  String toString() {
    return 'StatItem(key: $key, value: $value, label: $label)';
  }
}

/// Model for learning goals
class LearningGoal {
  final String id;
  final String title;
  final String description;
  final double progress;
  final double target;
  final bool isCompleted;
  final DateTime deadline;
  
  const LearningGoal({
    required this.id,
    required this.title,
    required this.description,
    required this.progress,
    required this.target,
    required this.isCompleted,
    required this.deadline,
  });
  
  /// Create from JSON
  factory LearningGoal.fromJson(Map<String, dynamic> json) {
    return LearningGoal(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      progress: (json['progress'] ?? 0.0).toDouble(),
      target: (json['target'] ?? 100.0).toDouble(),
      isCompleted: json['is_completed'] ?? false,
      deadline: DateTime.tryParse(json['deadline'] ?? '') ?? DateTime.now().add(const Duration(days: 30)),
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'progress': progress,
      'target': target,
      'is_completed': isCompleted,
      'deadline': deadline.toIso8601String(),
    };
  }
  
  /// Get progress percentage (0.0 to 1.0)
  double get progressPercentage => target > 0 ? (progress / target).clamp(0.0, 1.0) : 0.0;
  
  /// Get days remaining until deadline
  int get daysRemaining => deadline.difference(DateTime.now()).inDays;
  
  @override
  String toString() {
    return 'LearningGoal(id: $id, title: $title, progress: $progress/$target)';
  }
}

/// Model for recent activity items
class ActivityItem {
  final String id;
  final String type;
  final String description;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;
  
  const ActivityItem({
    required this.id,
    required this.type,
    required this.description,
    required this.timestamp,
    this.metadata,
  });
  
  /// Create from JSON
  factory ActivityItem.fromJson(Map<String, dynamic> json) {
    return ActivityItem(
      id: json['id'] ?? '',
      type: json['type'] ?? 'general',
      description: json['description'] ?? '',
      timestamp: DateTime.tryParse(json['timestamp'] ?? '') ?? DateTime.now(),
      metadata: json['metadata'],
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
  
  /// Get formatted time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
  
  @override
  String toString() {
    return 'ActivityItem(id: $id, type: $type, description: $description)';
  }
}