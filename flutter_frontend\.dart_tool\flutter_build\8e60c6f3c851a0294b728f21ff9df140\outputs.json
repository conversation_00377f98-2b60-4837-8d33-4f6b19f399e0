["C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\images\\placeholder.txt", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\placeholder.txt", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\fonts\\Inter-Regular.ttf", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\fonts\\Inter-Medium.ttf", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\fonts\\Inter-SemiBold.ttf", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\fonts\\Inter-Bold.ttf", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound\\assets\\js\\async_processor.js", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound\\assets\\js\\tau_web.js", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound_web\\howler\\howler.js", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound_web\\src\\flutter_sound.js", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound_web\\src\\flutter_sound_player.js", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound_web\\src\\flutter_sound_recorder.js", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound_web\\src\\flutter_sound_stream_processor.js", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "C:\\Users\\<USER>\\Desktop\\deutschkorrekt\\flutter_frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]