# DeutschKorrekt App Icons & Splash Screens Setup

This document provides instructions for setting up app icons and splash screens for the DeutschKorrekt Flutter app.

## App Icon Requirements

### Design Guidelines
- **Theme**: German language learning with modern, clean design
- **Colors**: Use the app's brand colors (blue #3b82f6, green #10b981)
- **Style**: Minimalist, professional, easily recognizable at small sizes
- **Content**: Could include German flag colors, speech bubble, or "D" letter mark

### Android Icons Required

Place the following icon files in their respective directories:

#### `android/app/src/main/res/mipmap-mdpi/` (48x48 dp)
- `ic_launcher.png` - 48x48 pixels

#### `android/app/src/main/res/mipmap-hdpi/` (72x72 dp)
- `ic_launcher.png` - 72x72 pixels

#### `android/app/src/main/res/mipmap-xhdpi/` (96x96 dp)
- `ic_launcher.png` - 96x96 pixels

#### `android/app/src/main/res/mipmap-xxhdpi/` (144x144 dp)
- `ic_launcher.png` - 144x144 pixels

#### `android/app/src/main/res/mipmap-xxxhdpi/` (192x192 dp)
- `ic_launcher.png` - 192x192 pixels

### iOS Icons Required

Place the following icon files in `ios/Runner/Assets.xcassets/AppIcon.appiconset/`:

- `Icon-20.png` - 20x20 pixels
- `<EMAIL>` - 40x40 pixels
- `<EMAIL>` - 60x60 pixels
- `Icon-29.png` - 29x29 pixels
- `<EMAIL>` - 58x58 pixels
- `<EMAIL>` - 87x87 pixels
- `Icon-40.png` - 40x40 pixels
- `<EMAIL>` - 80x80 pixels
- `<EMAIL>` - 120x120 pixels
- `<EMAIL>` - 120x120 pixels
- `<EMAIL>` - 180x180 pixels
- `Icon-76.png` - 76x76 pixels
- `<EMAIL>` - 152x152 pixels
- `<EMAIL>` - 167x167 pixels
- `Icon-1024.png` - 1024x1024 pixels (App Store)

## Splash Screen Setup

### Current Configuration
The splash screens are already configured to show:
- Dark background (#1a1a1a) matching the app theme
- App icon centered on screen
- Smooth transition to Flutter app

### Customization Options

#### Android
Edit `android/app/src/main/res/drawable/launch_background.xml` to:
- Change background color
- Add app name text
- Modify logo positioning

#### iOS
Edit `ios/Runner/Assets.xcassets/LaunchImage.launchimage/` to add custom launch images.

## Icon Generation Tools

### Recommended Tools
1. **App Icon Generator**: https://appicon.co/
2. **Flutter Launcher Icons**: https://pub.dev/packages/flutter_launcher_icons
3. **Icon Kitchen**: https://icon.kitchen/

### Using Flutter Launcher Icons Package

1. Add to `pubspec.yaml`:
```yaml
dev_dependencies:
  flutter_launcher_icons: ^0.13.1

flutter_icons:
  android: true
  ios: true
  image_path: "assets/icon/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icon/app_icon.png"
  windows:
    generate: true
    image_path: "assets/icon/app_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icon/app_icon.png"
```

2. Run: `flutter pub get`
3. Run: `flutter pub run flutter_launcher_icons:main`

## Brand Guidelines

### Color Palette
- **Primary Blue**: #3b82f6 (German flag blue inspiration)
- **Success Green**: #10b981 (correct answers, progress)
- **Error Red**: #ef4444 (corrections, mistakes)
- **Background Dark**: #0f172a (main app background)
- **Text Light**: #f8fafc (primary text color)

### Typography
- **Primary Font**: System default (SF Pro on iOS, Roboto on Android)
- **Accent Font**: Consider German-inspired fonts for branding

### Icon Style
- **Flat design** with subtle shadows
- **Rounded corners** (8px radius for consistency)
- **High contrast** for accessibility
- **Scalable vector** graphics when possible

## Testing

### Android Testing
1. Build APK: `flutter build apk`
2. Install on device: `flutter install`
3. Check icon appears correctly in launcher
4. Verify splash screen displays properly

### iOS Testing
1. Build iOS app: `flutter build ios`
2. Open in Xcode and run on simulator/device
3. Check icon in home screen and App Store
4. Verify launch screen transitions smoothly

## Production Checklist

- [ ] All Android icon sizes generated and placed
- [ ] All iOS icon sizes generated and placed
- [ ] Icons follow brand guidelines
- [ ] Splash screens tested on multiple devices
- [ ] App Store/Play Store screenshots prepared
- [ ] Icon accessibility tested (color contrast)
- [ ] High-resolution assets for marketing materials

## Notes

- The current setup provides the structure and configuration
- Actual icon image files need to be created and placed in the specified directories
- Consider hiring a professional designer for production-quality icons
- Test on various devices and screen densities
- Ensure icons look good in both light and dark system themes
