/// Audio utility functions for processing and conversion
class AudioUtils {
  /// Convert audio data to PCM16 format matching Expo version
  static List<int> convertToPCM16(List<int> audioData) {
    // TODO: Implement PCM16 conversion
    return audioData;
  }
  
  /// Calculate audio chunk size (1024 samples as per Expo version)
  static const int chunkSize = 1024;
  
  /// Audio sample rate (16kHz as per Expo version)
  static const int sampleRate = 16000;
  
  /// Audio channels (mono as per Expo version)
  static const int channels = 1;
}