import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../data/models/audio_config.dart';

/// Provider for managing app settings with persistent storage
class SettingsProvider extends ChangeNotifier {
  static const String _keyNotifications = 'notifications_enabled';
  static const String _keySoundEnabled = 'sound_enabled';
  static const String _keyAutoCorrect = 'auto_correct_enabled';
  static const String _keyVoiceRecording = 'voice_recording_enabled';
  static const String _keyLanguage = 'language';
  static const String _keyTheme = 'theme';
  static const String _keyAudioSampleRate = 'audio_sample_rate';
  static const String _keyAudioChannels = 'audio_channels';
  static const String _keyAudioChunkSize = 'audio_chunk_size';
  static const String _keyShowTranscriptionHints = 'show_transcription_hints';
  static const String _keyAutoScrollEnabled = 'auto_scroll_enabled';
  static const String _keyHapticFeedback = 'haptic_feedback';
  
  SharedPreferences? _prefs;
  bool _isInitialized = false;
  bool _isDisposed = false;
  
  // Settings values
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _autoCorrectEnabled = true;
  bool _voiceRecordingEnabled = true;
  bool _showTranscriptionHints = true;
  bool _autoScrollEnabled = true;
  bool _hapticFeedbackEnabled = true;
  String _language = 'German';
  String _theme = 'dark';
  AudioConfig _audioConfig = AudioConfig.defaultConfig();
  
  // Getters
  bool get notificationsEnabled => _notificationsEnabled;
  bool get soundEnabled => _soundEnabled;
  bool get autoCorrectEnabled => _autoCorrectEnabled;
  bool get voiceRecordingEnabled => _voiceRecordingEnabled;
  bool get showTranscriptionHints => _showTranscriptionHints;
  bool get autoScrollEnabled => _autoScrollEnabled;
  bool get hapticFeedbackEnabled => _hapticFeedbackEnabled;
  String get language => _language;
  String get theme => _theme;
  AudioConfig get audioConfig => _audioConfig;
  bool get isInitialized => _isInitialized;
  
  /// Initialize settings provider and load saved settings
  Future<void> initialize() async {
    if (_isDisposed || _isInitialized) return;
    
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      print('Error initializing settings: $e');
      _isInitialized = true; // Continue with defaults
      notifyListeners();
    }
  }
  
  /// Load settings from persistent storage
  Future<void> _loadSettings() async {
    if (_prefs == null) return;
    
    _notificationsEnabled = _prefs!.getBool(_keyNotifications) ?? true;
    _soundEnabled = _prefs!.getBool(_keySoundEnabled) ?? true;
    _autoCorrectEnabled = _prefs!.getBool(_keyAutoCorrect) ?? true;
    _voiceRecordingEnabled = _prefs!.getBool(_keyVoiceRecording) ?? true;
    _showTranscriptionHints = _prefs!.getBool(_keyShowTranscriptionHints) ?? true;
    _autoScrollEnabled = _prefs!.getBool(_keyAutoScrollEnabled) ?? true;
    _hapticFeedbackEnabled = _prefs!.getBool(_keyHapticFeedback) ?? true;
    _language = _prefs!.getString(_keyLanguage) ?? 'German';
    _theme = _prefs!.getString(_keyTheme) ?? 'dark';
    
    // Load audio configuration
    final sampleRate = _prefs!.getInt(_keyAudioSampleRate) ?? 16000;
    final channels = _prefs!.getInt(_keyAudioChannels) ?? 1;
    final chunkSize = _prefs!.getInt(_keyAudioChunkSize) ?? 1024;
    
    _audioConfig = AudioConfig(
      sampleRate: sampleRate,
      channels: channels,
      chunkSize: chunkSize,
    );
  }
  
  /// Save a boolean setting
  Future<void> _saveBoolSetting(String key, bool value) async {
    if (_prefs != null) {
      await _prefs!.setBool(key, value);
    }
  }
  
  /// Save a string setting
  Future<void> _saveStringSetting(String key, String value) async {
    if (_prefs != null) {
      await _prefs!.setString(key, value);
    }
  }
  
  /// Save an integer setting
  Future<void> _saveIntSetting(String key, int value) async {
    if (_prefs != null) {
      await _prefs!.setInt(key, value);
    }
  }
  
  /// Toggle notifications
  Future<void> toggleNotifications() async {
    if (_isDisposed) return;
    
    _notificationsEnabled = !_notificationsEnabled;
    await _saveBoolSetting(_keyNotifications, _notificationsEnabled);
    notifyListeners();
  }
  
  /// Toggle sound
  Future<void> toggleSound() async {
    if (_isDisposed) return;
    
    _soundEnabled = !_soundEnabled;
    await _saveBoolSetting(_keySoundEnabled, _soundEnabled);
    notifyListeners();
  }
  
  /// Toggle auto-correct
  Future<void> toggleAutoCorrect() async {
    if (_isDisposed) return;
    
    _autoCorrectEnabled = !_autoCorrectEnabled;
    await _saveBoolSetting(_keyAutoCorrect, _autoCorrectEnabled);
    notifyListeners();
  }
  
  /// Toggle voice recording
  Future<void> toggleVoiceRecording() async {
    if (_isDisposed) return;
    
    _voiceRecordingEnabled = !_voiceRecordingEnabled;
    await _saveBoolSetting(_keyVoiceRecording, _voiceRecordingEnabled);
    notifyListeners();
  }
  
  /// Toggle transcription hints
  Future<void> toggleTranscriptionHints() async {
    if (_isDisposed) return;
    
    _showTranscriptionHints = !_showTranscriptionHints;
    await _saveBoolSetting(_keyShowTranscriptionHints, _showTranscriptionHints);
    notifyListeners();
  }
  
  /// Toggle auto-scroll
  Future<void> toggleAutoScroll() async {
    if (_isDisposed) return;
    
    _autoScrollEnabled = !_autoScrollEnabled;
    await _saveBoolSetting(_keyAutoScrollEnabled, _autoScrollEnabled);
    notifyListeners();
  }
  
  /// Toggle haptic feedback
  Future<void> toggleHapticFeedback() async {
    if (_isDisposed) return;
    
    _hapticFeedbackEnabled = !_hapticFeedbackEnabled;
    await _saveBoolSetting(_keyHapticFeedback, _hapticFeedbackEnabled);
    notifyListeners();
  }
  
  /// Set language
  Future<void> setLanguage(String language) async {
    if (_isDisposed || _language == language) return;
    
    _language = language;
    await _saveStringSetting(_keyLanguage, language);
    notifyListeners();
  }
  
  /// Set theme
  Future<void> setTheme(String theme) async {
    if (_isDisposed || _theme == theme) return;
    
    _theme = theme;
    await _saveStringSetting(_keyTheme, theme);
    notifyListeners();
  }
  
  /// Update audio configuration
  Future<void> updateAudioConfig(AudioConfig config) async {
    if (_isDisposed) return;
    
    _audioConfig = config;
    
    await _saveIntSetting(_keyAudioSampleRate, config.sampleRate);
    await _saveIntSetting(_keyAudioChannels, config.channels);
    await _saveIntSetting(_keyAudioChunkSize, config.chunkSize);
    
    notifyListeners();
  }
  
  /// Reset all settings to defaults
  Future<void> resetToDefaults() async {
    if (_isDisposed) return;
    
    _notificationsEnabled = true;
    _soundEnabled = true;
    _autoCorrectEnabled = true;
    _voiceRecordingEnabled = true;
    _showTranscriptionHints = true;
    _autoScrollEnabled = true;
    _hapticFeedbackEnabled = true;
    _language = 'German';
    _theme = 'dark';
    _audioConfig = AudioConfig.defaultConfig();
    
    // Clear all saved settings
    if (_prefs != null) {
      await _prefs!.clear();
      
      // Save defaults
      await _saveBoolSetting(_keyNotifications, _notificationsEnabled);
      await _saveBoolSetting(_keySoundEnabled, _soundEnabled);
      await _saveBoolSetting(_keyAutoCorrect, _autoCorrectEnabled);
      await _saveBoolSetting(_keyVoiceRecording, _voiceRecordingEnabled);
      await _saveBoolSetting(_keyShowTranscriptionHints, _showTranscriptionHints);
      await _saveBoolSetting(_keyAutoScrollEnabled, _autoScrollEnabled);
      await _saveBoolSetting(_keyHapticFeedback, _hapticFeedbackEnabled);
      await _saveStringSetting(_keyLanguage, _language);
      await _saveStringSetting(_keyTheme, _theme);
      await _saveIntSetting(_keyAudioSampleRate, _audioConfig.sampleRate);
      await _saveIntSetting(_keyAudioChannels, _audioConfig.channels);
      await _saveIntSetting(_keyAudioChunkSize, _audioConfig.chunkSize);
    }
    
    notifyListeners();
  }
  
  /// Get available languages
  List<String> getAvailableLanguages() {
    return ['German', 'English', 'Spanish', 'French', 'Italian'];
  }
  
  /// Get available themes
  List<String> getAvailableThemes() {
    return ['dark', 'light', 'system'];
  }
  
  /// Get available audio sample rates
  List<int> getAvailableSampleRates() {
    return [8000, 16000, 22050, 44100, 48000];
  }
  
  /// Get available audio channel configurations
  List<int> getAvailableChannels() {
    return [1, 2]; // Mono, Stereo
  }
  
  /// Get available chunk sizes
  List<int> getAvailableChunkSizes() {
    return [512, 1024, 2048, 4096];
  }
  
  /// Export settings to JSON
  Map<String, dynamic> exportSettings() {
    return {
      'notifications_enabled': _notificationsEnabled,
      'sound_enabled': _soundEnabled,
      'auto_correct_enabled': _autoCorrectEnabled,
      'voice_recording_enabled': _voiceRecordingEnabled,
      'show_transcription_hints': _showTranscriptionHints,
      'auto_scroll_enabled': _autoScrollEnabled,
      'haptic_feedback': _hapticFeedbackEnabled,
      'language': _language,
      'theme': _theme,
      'audio_config': _audioConfig.toJson(),
    };
  }
  
  /// Import settings from JSON
  Future<void> importSettings(Map<String, dynamic> settings) async {
    if (_isDisposed) return;
    
    try {
      _notificationsEnabled = settings['notifications_enabled'] ?? true;
      _soundEnabled = settings['sound_enabled'] ?? true;
      _autoCorrectEnabled = settings['auto_correct_enabled'] ?? true;
      _voiceRecordingEnabled = settings['voice_recording_enabled'] ?? true;
      _showTranscriptionHints = settings['show_transcription_hints'] ?? true;
      _autoScrollEnabled = settings['auto_scroll_enabled'] ?? true;
      _hapticFeedbackEnabled = settings['haptic_feedback'] ?? true;
      _language = settings['language'] ?? 'German';
      _theme = settings['theme'] ?? 'dark';
      
      if (settings['audio_config'] != null) {
        _audioConfig = AudioConfig.fromJson(settings['audio_config']);
      }
      
      // Save imported settings
      await _saveBoolSetting(_keyNotifications, _notificationsEnabled);
      await _saveBoolSetting(_keySoundEnabled, _soundEnabled);
      await _saveBoolSetting(_keyAutoCorrect, _autoCorrectEnabled);
      await _saveBoolSetting(_keyVoiceRecording, _voiceRecordingEnabled);
      await _saveBoolSetting(_keyShowTranscriptionHints, _showTranscriptionHints);
      await _saveBoolSetting(_keyAutoScrollEnabled, _autoScrollEnabled);
      await _saveBoolSetting(_keyHapticFeedback, _hapticFeedbackEnabled);
      await _saveStringSetting(_keyLanguage, _language);
      await _saveStringSetting(_keyTheme, _theme);
      await _saveIntSetting(_keyAudioSampleRate, _audioConfig.sampleRate);
      await _saveIntSetting(_keyAudioChannels, _audioConfig.channels);
      await _saveIntSetting(_keyAudioChunkSize, _audioConfig.chunkSize);
      
      notifyListeners();
      
    } catch (e) {
      print('Error importing settings: $e');
    }
  }
  
  /// Get settings statistics
  Map<String, dynamic> getStats() {
    return {
      'isInitialized': _isInitialized,
      'settingsCount': exportSettings().length,
      'audioConfig': _audioConfig.toJson(),
    };
  }
  
  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}