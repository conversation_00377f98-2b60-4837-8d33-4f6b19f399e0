import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../data/models/audio_config.dart';
import '../../data/models/app_error.dart';
import '../../data/services/audio_service.dart';
import '../../data/services/permissions_service.dart';
import '../../data/services/error_handling_service.dart';
import '../../core/utils/app_lifecycle_manager.dart';

/// Provider for managing audio recording and streaming state
class AudioProvider extends ChangeNotifier {
  final AudioService _audioService = AudioService.instance;
  final PermissionsService _permissionsService = PermissionsService.instance;
  final ErrorHandlingService _errorHandlingService = ErrorHandlingService.instance;
  final AppLifecycleManager _lifecycleManager = AppLifecycleManager.instance;
  
  StreamSubscription? _recordingStateSubscription;
  StreamSubscription? _errorSubscription;
  StreamSubscription? _lifecycleSubscription;
  StreamSubscription? _lifecycleErrorSubscription;
  
  bool _isRecording = false;
  bool _hasPermission = false;
  bool _isConnecting = false;
  bool _isInitialized = false;
  String _status = 'idle';
  AudioConfig _config = AudioConfig.defaultConfig();
  AppError? _lastError;
  double _currentAmplitude = 0.0;
  bool _isDisposed = false;
  
  // Getters
  bool get isRecording => _isRecording;
  bool get hasPermission => _hasPermission;
  bool get isConnecting => _isConnecting;
  bool get isInitialized => _isInitialized;
  String get status => _status;
  AudioConfig get config => _config;
  AppError? get lastError => _lastError;
  double get currentAmplitude => _currentAmplitude;
  bool get hasError => _lastError != null;
  
  /// Initialize the audio provider
  Future<void> initialize({AudioConfig? config}) async {
    if (_isDisposed || _isInitialized) return;
    
    try {
      _config = config ?? AudioConfig.defaultConfig();
      
      await _audioService.initialize(config: _config);
      _setupSubscriptions();
      await _checkPermissions();
      
      _isInitialized = true;
      _updateStatus('initialized');
      
    } catch (e) {
      _handleError(AppError.audioRecordingFailed(
        details: 'Failed to initialize audio: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      ));
    }
  }
  
  /// Set up subscriptions to audio service streams
  void _setupSubscriptions() {
    _recordingStateSubscription = _audioService.recordingStateStream.listen(
      _handleRecordingStateChange,
      onError: _handleError,
    );
    
    _errorSubscription = _audioService.errorStream.listen(
      _handleError,
    );
    
    // Initialize and subscribe to lifecycle manager
    _initializeLifecycleManager();
  }
  
  /// Initialize lifecycle manager and set up subscriptions
  Future<void> _initializeLifecycleManager() async {
    try {
      await _lifecycleManager.initialize();
      
      _lifecycleSubscription = _lifecycleManager.lifecycleStream.listen(
        _handleLifecycleChange,
      );
      
      _lifecycleErrorSubscription = _lifecycleManager.errorStream.listen(
        _handleError,
      );
    } catch (e) {
      print('Failed to initialize lifecycle manager: $e');
    }
  }
  
  /// Handle app lifecycle changes
  void _handleLifecycleChange(AppLifecycleState state) {
    if (_isDisposed) return;
    
    print('AudioProvider handling lifecycle change: $state');
    
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _updateStatus('background');
        break;
      case AppLifecycleState.resumed:
        _updateStatus(_isRecording ? 'recording' : 'idle');
        break;
      case AppLifecycleState.detached:
        _updateStatus('terminated');
        break;
      default:
        break;
    }
    
    notifyListeners();
  }
  
  /// Handle recording state changes from audio service
  void _handleRecordingStateChange(bool recording) {
    if (_isDisposed) return;
    
    _isRecording = recording;
    _updateStatus(recording ? 'recording' : 'idle');
    notifyListeners();
  }
  
  /// Handle errors from audio service
  void _handleError(dynamic error) {
    if (_isDisposed) return;
    
    AppError appError;
    if (error is AppError) {
      appError = error;
    } else {
      appError = AppError.audioRecordingFailed(
        details: error.toString(),
        originalException: error is Exception ? error : Exception(error.toString()),
      );
    }
    
    _lastError = appError;
    _updateStatus('error');
    notifyListeners();
  }
  
  /// Check microphone permissions
  Future<void> _checkPermissions() async {
    try {
      _hasPermission = await _permissionsService.hasMicrophonePermission();
      notifyListeners();
    } catch (e) {
      _handleError(AppError.microphonePermissionDenied(
        details: 'Failed to check permissions: $e',
      ));
    }
  }
  
  /// Request microphone permissions
  Future<bool> requestPermissions() async {
    if (_isDisposed) return false;
    
    try {
      _updateStatus('requesting_permission');
      
      final granted = await _permissionsService.requestMicrophonePermissionWithHandling();
      _hasPermission = granted;
      
      if (!granted) {
        final error = await _permissionsService.handleMicrophonePermissionResult();
        if (error != null) {
          _handleError(error);
        }
      }
      
      _updateStatus(granted ? 'permission_granted' : 'permission_denied');
      notifyListeners();
      
      return granted;
      
    } catch (e) {
      _handleError(AppError.microphonePermissionDenied(
        details: 'Failed to request permissions: $e',
      ));
      return false;
    }
  }
  
  /// Start audio recording
  Future<void> startRecording() async {
    if (_isDisposed || _isRecording) return;
    
    try {
      _isConnecting = true;
      _updateStatus('connecting');
      _clearError();
      notifyListeners();
      
      // Check permissions first
      if (!_hasPermission) {
        final granted = await requestPermissions();
        if (!granted) {
          _isConnecting = false;
          notifyListeners();
          return;
        }
      }
      
      await _audioService.startRecording();
      
      _isConnecting = false;
      notifyListeners();
      
      // Start amplitude monitoring
      _startAmplitudeMonitoring();
      
    } catch (e) {
      _isConnecting = false;
      _handleError(AppError.audioRecordingFailed(
        details: 'Failed to start recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      ));
    }
  }
  
  /// Stop audio recording
  Future<void> stopRecording() async {
    if (_isDisposed || !_isRecording) return;
    
    try {
      await _audioService.stopRecording();
      _stopAmplitudeMonitoring();
      
    } catch (e) {
      _handleError(AppError.audioRecordingFailed(
        details: 'Failed to stop recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      ));
    }
  }
  
  /// Pause audio recording
  Future<void> pauseRecording() async {
    if (_isDisposed || !_isRecording) return;
    
    try {
      await _audioService.pauseRecording();
      _updateStatus('paused');
      
    } catch (e) {
      _handleError(AppError.audioRecordingFailed(
        details: 'Failed to pause recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      ));
    }
  }
  
  /// Resume audio recording
  Future<void> resumeRecording() async {
    if (_isDisposed) return;
    
    try {
      await _audioService.resumeRecording();
      _updateStatus('recording');
      
    } catch (e) {
      _handleError(AppError.audioRecordingFailed(
        details: 'Failed to resume recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      ));
    }
  }
  
  /// Start monitoring audio amplitude
  void _startAmplitudeMonitoring() {
    Timer.periodic(const Duration(milliseconds: 100), (timer) async {
      if (!_isRecording || _isDisposed) {
        timer.cancel();
        return;
      }
      
      try {
        _currentAmplitude = await _audioService.getAmplitude();
        notifyListeners();
      } catch (e) {
        // Ignore amplitude errors
      }
    });
  }
  
  /// Stop monitoring audio amplitude
  void _stopAmplitudeMonitoring() {
    _currentAmplitude = 0.0;
    notifyListeners();
  }
  
  /// Update audio configuration
  void updateConfig(AudioConfig newConfig) {
    if (_isDisposed || _isRecording) return;
    
    _config = newConfig;
    _audioService.updateConfig(newConfig);
    notifyListeners();
  }
  
  /// Check if recording is supported
  Future<bool> isRecordingSupported() async {
    if (_isDisposed) return false;
    
    try {
      return await _audioService.isRecordingSupported();
    } catch (e) {
      return false;
    }
  }
  
  /// Get permission status message
  String getPermissionStatusMessage() {
    if (_lastError?.type == AppErrorType.microphonePermissionDenied) {
      return _lastError!.userMessage;
    }
    
    if (_hasPermission) {
      return 'Microphone access granted';
    } else {
      return 'Microphone access required for voice features';
    }
  }
  
  /// Open app settings for permission management
  Future<void> openAppSettings() async {
    try {
      await _permissionsService.openAppSettings();
    } catch (e) {
      _handleError(AppError.unknown(
        details: 'Failed to open app settings: $e',
      ));
    }
  }
  
  /// Get platform-specific permission rationale
  String getPermissionRationale() {
    return _permissionsService.getPlatformSpecificRationale();
  }
  
  /// Update status and notify listeners
  void _updateStatus(String status) {
    _status = status;
    notifyListeners();
  }
  
  /// Clear error state
  void _clearError() {
    _lastError = null;
  }
  
  /// Clear error state (public method)
  void clearError() {
    if (_isDisposed) return;
    
    _clearError();
    notifyListeners();
  }
  
  /// Reset audio state
  void reset() {
    if (_isDisposed) return;
    
    _isRecording = false;
    _isConnecting = false;
    _currentAmplitude = 0.0;
    _lastError = null;
    _updateStatus('idle');
  }
  
  /// Get audio provider statistics
  Map<String, dynamic> getStats() {
    return {
      'isRecording': _isRecording,
      'hasPermission': _hasPermission,
      'isConnecting': _isConnecting,
      'isInitialized': _isInitialized,
      'status': _status,
      'currentAmplitude': _currentAmplitude,
      'hasError': hasError,
      'config': _config.toJson(),
      'audioServiceStats': _audioService.getStats(),
    };
  }
  
  @override
  void dispose() {
    _isDisposed = true;
    
    _recordingStateSubscription?.cancel();
    _errorSubscription?.cancel();
    
    super.dispose();
  }
}