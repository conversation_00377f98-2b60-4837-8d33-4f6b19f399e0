/// Environment configuration for the DeutschKorrekt app
class Environment {
  static const String _environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
  
  /// Check if running in development mode
  static bool get isDevelopment => _environment == 'development';
  
  /// Check if running in production mode
  static bool get isProduction => _environment == 'production';
  
  /// Get current environment name
  static String get current => _environment;
  
  /// Supabase configuration based on environment
  static String get supabaseUrl {
    switch (_environment) {
      case 'production':
        return 'https://eaemndginhddncydpaix.supabase.co';
      case 'development':
      default:
        return 'https://eaemndginhddncydpaix.supabase.co'; // Same instance for now
    }
  }
  
  /// Supabase anonymous key based on environment
  static String get supabaseAnonKey {
    switch (_environment) {
      case 'production':
        return const String.fromEnvironment('SUPABASE_ANON_KEY_PROD', defaultValue: 'YOUR_PROD_KEY');
      case 'development':
      default:
        return const String.fromEnvironment('SUPABASE_ANON_KEY_DEV', defaultValue: 'YOUR_DEV_KEY');
    }
  }
  
  /// Enable debug logging in development
  static bool get enableDebugLogging => isDevelopment;
}