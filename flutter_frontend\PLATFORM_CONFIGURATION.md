# DeutschKorrekt Platform-Specific Configuration

This document outlines all platform-specific configurations implemented for the DeutschKorrekt Flutter app.

## Android Configuration

### Permissions (`android/app/src/main/AndroidManifest.xml`)

#### Audio Permissions
- `RECORD_AUDIO` - Required for microphone access
- `MODIFY_AUDIO_SETTINGS` - Required for audio session management
- `WAKE_LOCK` - Prevents device sleep during recording

#### Network Permissions
- `INTERNET` - Required for WebSocket communication with backend

#### Hardware Features
- `android.hardware.microphone` (required) - Ensures device has microphone
- `android.hardware.audio.low_latency` (optional) - Optimizes for real-time audio

### Audio Focus Management
- Implemented via `AudioFocusService` using `audio_session` package
- Handles interruptions from calls, other apps, notifications
- Automatically pauses recording when audio focus is lost
- Resumes recording when focus is regained (user-controlled)

### App Icon Configuration
- Configured to use `@mipmap/ic_launcher`
- Supports all Android density buckets (mdpi, hdpi, xhdpi, xxhdpi, xxxhdpi)
- Adaptive icon support for Android 8.0+

### Splash Screen
- Custom launch background with app branding
- Dark theme matching app design
- Smooth transition to Flutter UI
- Configured via `LaunchTheme` in styles.xml

### Build Configuration
- Minimum SDK: 21 (Android 5.0)
- Target SDK: Latest stable
- Compile SDK: Latest stable
- ProGuard/R8 optimization enabled for release builds

## iOS Configuration

### Permissions (`ios/Runner/Info.plist`)

#### Audio Permissions
- `NSMicrophoneUsageDescription` - Explains microphone usage to users
- `UIBackgroundModes` with `audio` - Allows audio processing in background

#### Audio Session Configuration
- Category: `AVAudioSessionCategoryPlayAndRecord`
- Mode: `AVAudioSessionModeSpokenAudio`
- Options: `DefaultToSpeaker`, `AllowBluetooth`, `AllowBluetoothA2DP`

### Audio Focus Management
- Implemented via `AudioFocusService` using `audio_session` package
- Handles interruptions from calls, Siri, other audio apps
- Proper AVAudioSession lifecycle management
- Interruption handling with resume capabilities

### App Icon Configuration
- Complete icon set for all iOS device types and sizes
- Includes App Store icon (1024x1024)
- Supports iPhone, iPad, and Apple Watch (if needed)
- Configured via `Assets.xcassets/AppIcon.appiconset`

### Launch Screen
- Configured via `Assets.xcassets/LaunchImage.launchimage`
- Supports all device orientations and sizes
- Matches app branding and theme

### Build Configuration
- Minimum iOS version: 12.0
- Supports iPhone and iPad
- Bitcode enabled for App Store optimization
- Swift version: Latest stable

## Cross-Platform Features

### Audio Processing
- Real-time PCM16 audio capture at 16kHz
- Isolate-based audio processing for performance
- Automatic gain control and noise reduction
- Compatible with Deepgram API requirements

### WebSocket Communication
- Connection pooling for reliability
- Automatic reconnection with exponential backoff
- Load balancing across multiple connections
- Proper error handling and recovery

### State Management
- Provider pattern for reactive UI updates
- Proper lifecycle management
- Memory leak prevention
- Background/foreground state handling

### Error Handling
- Comprehensive error types and messages
- User-friendly error dialogs
- Automatic retry mechanisms
- Graceful degradation for network issues

## Performance Optimizations

### Audio Processing
- Isolate-based processing prevents UI blocking
- Efficient memory management for audio buffers
- Optimized PCM16 conversion algorithms
- Minimal latency for real-time streaming

### Network Communication
- WebSocket connection pooling
- Efficient binary data transmission
- Automatic connection health monitoring
- Load balancing for optimal performance

### UI Rendering
- Optimized gradient rendering with const constructors
- Efficient list virtualization for long conversations
- Proper widget disposal and memory management
- Smooth animations with hardware acceleration

### Memory Management
- Proper stream disposal and cleanup
- Isolate lifecycle management
- Connection pool resource management
- Automatic garbage collection optimization

## Security Considerations

### Audio Data
- Real-time streaming without local storage
- Secure WebSocket connections (WSS)
- No persistent audio file storage
- Proper cleanup of audio buffers

### Network Communication
- TLS encryption for all communications
- Certificate pinning (recommended for production)
- Proper error message sanitization
- No sensitive data in logs

### Permissions
- Minimal required permissions
- Clear usage descriptions for users
- Runtime permission requests
- Graceful handling of permission denials

## Testing Configuration

### Unit Tests
- Comprehensive service testing
- Mock implementations for external dependencies
- Audio processing algorithm validation
- Error handling verification

### Widget Tests
- UI component testing with proper finders
- State management testing
- User interaction simulation
- Accessibility testing

### Integration Tests
- End-to-end user flow testing
- Real WebSocket communication testing
- Audio recording and processing testing
- Platform-specific feature testing

## Production Deployment

### Android
- Signed APK/AAB generation
- Play Store metadata and screenshots
- ProGuard/R8 optimization
- Multiple architecture support (arm64-v8a, armeabi-v7a, x86_64)

### iOS
- App Store Connect configuration
- TestFlight beta testing
- App Store review guidelines compliance
- Device compatibility testing

### Backend Integration
- Production WebSocket endpoint configuration
- SSL certificate validation
- Rate limiting and abuse prevention
- Monitoring and analytics integration

## Maintenance and Updates

### Dependency Management
- Regular package updates
- Security vulnerability monitoring
- Breaking change migration guides
- Compatibility testing across Flutter versions

### Platform Updates
- Android API level updates
- iOS version compatibility
- New platform feature adoption
- Deprecated API migration

### Performance Monitoring
- Crash reporting integration
- Performance metrics collection
- User experience analytics
- Real-time error monitoring

## Troubleshooting

### Common Issues
- Microphone permission denied
- WebSocket connection failures
- Audio focus conflicts
- Background processing limitations

### Debug Tools
- Flutter Inspector for UI debugging
- Platform-specific debugging tools
- Network traffic analysis
- Audio processing diagnostics

### Support Resources
- Platform-specific documentation links
- Community support channels
- Issue reporting guidelines
- Performance optimization guides
