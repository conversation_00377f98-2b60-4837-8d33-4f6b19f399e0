# DeutschKorrekt Flutter Frontend

A Flutter frontend application that exactly replicates the functionality and UI of the existing Expo/React Native DeutschKorrekt app. This is a German language learning tool that provides real-time speech-to-text transcription with AI-powered grammar correction and language assistance.

## Features

- Real-time speech-to-text transcription
- AI-powered German grammar correction
- WebSocket communication with backend
- German flag microphone button design
- Gradient UI matching Expo version
- Profile and settings screens
- Enterprise-level Flutter architecture

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── app.dart                  # Main app widget
├── core/
│   ├── constants/           # Colors, gradients, text styles
│   ├── utils/              # Audio and WebSocket utilities
│   └── theme/              # App theme configuration
├── data/
│   ├── models/             # Data models (Message, CorrectionResult, etc.)
│   ├── services/           # WebSocket, Audio, Permissions services
│   └── repositories/       # Data repositories
└── presentation/
    ├── providers/          # State management (Provider pattern)
    ├── screens/           # Main screens (Chat, Profile, Settings)
    └── widgets/           # Reusable UI components
```

## Dependencies

- **provider**: State management
- **web_socket_channel**: WebSocket communication
- **record**: Audio recording
- **permission_handler**: Microphone permissions
- **http**: HTTP requests
- **uuid**: Unique ID generation
- **intl**: Internationalization

## Getting Started

1. Ensure Flutter SDK is installed (>=3.10.0)
2. Run `flutter pub get` to install dependencies
3. For Android: Ensure microphone permissions are configured
4. For iOS: Ensure microphone usage description is set
5. Run `flutter run` to start the app

## Backend Integration

The app connects to the DeutschKorrekt backend via WebSocket:
- URL: `wss://deutschkorrekt-backend-************.europe-west3.run.app/stt`
- Supports real-time audio streaming
- Handles partial/final transcripts and AI responses

## Architecture

The app follows Flutter best practices with:
- Provider pattern for state management
- Layered architecture (Presentation, Business Logic, Data)
- Proper separation of concerns
- Enterprise-level error handling
- Comprehensive testing strategy

## Development Status

This project is currently in development as part of the flutter-frontend-replication spec. The implementation follows the exact design and requirements from the original Expo version.