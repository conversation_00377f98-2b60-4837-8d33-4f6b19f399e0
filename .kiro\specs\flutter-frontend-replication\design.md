# Design Document

## Overview

This document outlines the technical design for creating a Flutter frontend application that exactly replicates the DeutschKorrekt Expo/React Native app. The Flutter app will be a German language learning tool providing real-time speech-to-text transcription with AI-powered grammar correction and language assistance.

The design follows Flutter best practices and enterprise-level architecture patterns while maintaining pixel-perfect visual fidelity to the original Expo app. The app will use modern Flutter state management, proper separation of concerns, and production-ready error handling.

## Architecture

### High-Level Architecture

The Flutter app follows a layered architecture pattern:

```
┌─────────────────────────────────────────┐
│              Presentation Layer          │
│  ┌─────────────┐  ┌─────────────────────┐│
│  │   Screens   │  │      Widgets        ││
│  │             │  │                     ││
│  └─────────────┘  └─────────────────────┘│
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              Business Logic Layer       │
│  ┌─────────────┐  ┌─────────────────────┐│
│  │  Providers  │  │     Services        ││
│  │             │  │                     ││
│  └─────────────┘  └─────────────────────┘│
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│                Data Layer               │
│  ┌─────────────┐  ┌─────────────────────┐│
│  │ WebSocket   │  │   Audio Recorder    ││
│  │  Client     │  │                     ││
│  └─────────────┘  └─────────────────────┘│
└─────────────────────────────────────────┘
```

### State Management

The app will use **Provider** pattern for state management, chosen for its simplicity and alignment with Flutter best practices:

- **ChatProvider**: Manages conversation state, messages, and UI state
- **AudioProvider**: Handles audio recording, streaming, and WebSocket communication
- **SettingsProvider**: Manages app settings and user preferences
- **ProfileProvider**: Handles user profile data and learning statistics

### Project Structure

```
lib/
├── main.dart
├── app.dart
├── core/
│   ├── constants/
│   │   ├── colors.dart
│   │   ├── gradients.dart
│   │   └── text_styles.dart
│   ├── utils/
│   │   ├── audio_utils.dart
│   │   └── websocket_utils.dart
│   └── theme/
│       └── app_theme.dart
├── data/
│   ├── models/
│   │   ├── message.dart
│   │   ├── groq_response.dart
│   │   └── correction_result.dart
│   ├── services/
│   │   ├── websocket_service.dart
│   │   ├── audio_service.dart
│   │   └── permissions_service.dart
│   └── repositories/
│       └── chat_repository.dart
├── presentation/
│   ├── providers/
│   │   ├── chat_provider.dart
│   │   ├── audio_provider.dart
│   │   ├── settings_provider.dart
│   │   └── profile_provider.dart
│   ├── screens/
│   │   ├── chat_screen.dart
│   │   ├── profile_screen.dart
│   │   └── settings_screen.dart
│   └── widgets/
│       ├── common/
│       │   ├── gradient_background.dart
│       │   ├── custom_modal.dart
│       │   └── formatted_text.dart
│       ├── chat/
│       │   ├── message_bubble.dart
│       │   ├── microphone_button.dart
│       │   ├── correction_result_widget.dart
│       │   └── chat_header.dart
│       ├── profile/
│       │   ├── profile_card.dart
│       │   ├── stats_widget.dart
│       │   └── activity_list.dart
│       └── settings/
│           ├── setting_item.dart
│           └── setting_switch.dart
└── generated/
    └── assets.dart
```

## Components and Interfaces

### Core Models

#### Message Model
```dart
class Message {
  final String id;
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final bool isAudio;
  final bool isStreaming;
  final bool isProcessing;
  final CorrectionResult? correctionResult;
  final TranslationResult? translationResult;
  
  Message({
    required this.id,
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.isAudio = false,
    this.isStreaming = false,
    this.isProcessing = false,
    this.correctionResult,
    this.translationResult,
  });
}
```

#### WebSocket Message Types
```dart
enum WebSocketMessageType {
  partialTranscript,
  finalTranscript,
  processing,
  groqResponse,
  error,
  timeout,
  info,
  sessionStarted,
}

class WebSocketMessage {
  final WebSocketMessageType messageType;
  final Map<String, dynamic> data;
  
  WebSocketMessage({
    required this.messageType,
    required this.data,
  });
}
```

### Key Services

#### WebSocketService
```dart
class WebSocketService {
  static const String backendUrl = 'wss://deutschkorrekt-backend-645996191396.europe-west3.run.app/stt';
  
  WebSocket? _webSocket;
  StreamController<WebSocketMessage> _messageController;
  
  Future<void> connect();
  Future<void> disconnect();
  void sendAudioData(Uint8List audioData);
  Stream<WebSocketMessage> get messageStream;
  bool get isConnected;
}
```

#### AudioService
```dart
class AudioService {
  static const int sampleRate = 16000;
  static const int channels = 1;
  static const int chunkSize = 1024;
  
  StreamSubscription<Uint8List>? _audioSubscription;
  
  Future<bool> requestPermissions();
  Future<void> startRecording();
  Future<void> stopRecording();
  Stream<Uint8List> get audioStream;
  bool get isRecording;
}
```

### UI Components

#### MicrophoneButton Widget
```dart
class MicrophoneButton extends StatefulWidget {
  final VoidCallback onPressed;
  final bool isStreaming;
  final bool isConnecting;
  final bool isDisabled;
  
  const MicrophoneButton({
    Key? key,
    required this.onPressed,
    required this.isStreaming,
    required this.isConnecting,
    required this.isDisabled,
  }) : super(key: key);
}
```

The button will feature:
- German flag background (black, red, yellow stripes)
- Microphone icon overlay
- Scaling animation when active (transform: scale(1.15))
- Color transitions (normal → red gradient when streaming)
- Disabled state handling

#### MessageBubble Widget
```dart
class MessageBubble extends StatelessWidget {
  final Message message;
  
  const MessageBubble({
    Key? key,
    required this.message,
  }) : super(key: key);
}
```

Features:
- User messages: Right-aligned with slate gradient background
- AI messages: Left-aligned with white background and border
- Streaming indicators and processing states
- Correction result containers with agent badges
- FormattedText rendering for bold text markers

#### FormattedText Widget
```dart
class FormattedText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextStyle? boldStyle;
  
  const FormattedText({
    Key? key,
    required this.text,
    this.style,
    this.boldStyle,
  }) : super(key: key);
}
```

Parses **bold** markers and renders with appropriate styling.

## Data Models

### Message Flow Data Structure

```dart
// Conversation state
class ChatState {
  final List<Message> messages;
  final String? currentMessageId;
  final bool isConnected;
  final bool isStreaming;
  final bool isProcessing;
  final String partialText;
  final int retryCount;
  
  ChatState({
    required this.messages,
    this.currentMessageId,
    required this.isConnected,
    required this.isStreaming,
    required this.isProcessing,
    required this.partialText,
    required this.retryCount,
  });
}
```

### Correction Results

```dart
class CorrectionResult {
  final String originalText;
  final String correctedText;
  final List<String> suggestions;
  final List<String> explanations;
  final double processingTime;
  
  CorrectionResult({
    required this.originalText,
    required this.correctedText,
    required this.suggestions,
    required this.explanations,
    required this.processingTime,
  });
}
```

### Profile Data

```dart
class ProfileData {
  final String userName;
  final String userLevel;
  final List<StatItem> stats;
  final List<LearningGoal> goals;
  final List<ActivityItem> recentActivity;
  
  ProfileData({
    required this.userName,
    required this.userLevel,
    required this.stats,
    required this.goals,
    required this.recentActivity,
  });
}
```

## Error Handling

### Error Types and Handling Strategy

```dart
enum AppError {
  microphonePermissionDenied,
  websocketConnectionFailed,
  audioRecordingFailed,
  transcriptionTimeout,
  backendError,
  networkError,
}

class ErrorHandler {
  static void handleError(AppError error, {String? message}) {
    switch (error) {
      case AppError.microphonePermissionDenied:
        _showPermissionDialog();
        break;
      case AppError.websocketConnectionFailed:
        _showConnectionErrorDialog(message);
        break;
      // ... other error cases
    }
  }
}
```

### Retry Logic

The app implements the same retry logic as the Expo version:
- WebSocket connection retries: 3 attempts with exponential backoff
- Audio streaming retries: Automatic reconnection on failure
- Graceful degradation when backend is unavailable

## Testing Strategy

### Unit Tests
- **Model Tests**: Message, CorrectionResult, ProfileData serialization/deserialization
- **Service Tests**: WebSocketService, AudioService, PermissionsService
- **Provider Tests**: ChatProvider, AudioProvider state management
- **Utility Tests**: Audio processing, WebSocket message parsing

### Widget Tests
- **Component Tests**: MicrophoneButton, MessageBubble, FormattedText
- **Screen Tests**: ChatScreen, ProfileScreen, SettingsScreen
- **Integration Tests**: Full user flow from recording to AI response

### Integration Tests
- **End-to-End Tests**: Complete recording → transcription → AI response flow
- **WebSocket Tests**: Real backend communication testing
- **Audio Tests**: Microphone permission and recording functionality

### Test Structure
```
test/
├── unit/
│   ├── models/
│   ├── services/
│   ├── providers/
│   └── utils/
├── widget/
│   ├── screens/
│   └── widgets/
└── integration/
    ├── audio_flow_test.dart
    ├── websocket_flow_test.dart
    └── full_conversation_test.dart
```

## Visual Design Implementation

### Color Scheme (Exact Expo Replication)

```dart
class AppColors {
  // Gradient colors from Expo version
  static const Color slate600 = Color(0xFF64748b);
  static const Color slate700 = Color(0xFF475569);
  static const Color slate800 = Color(0xFF334155);
  static const Color slate900 = Color(0xFF1e293b);
  static const Color slate950 = Color(0xFF0f172a);
  
  // German flag colors
  static const Color germanBlack = Color(0xFF03080c);
  static const Color germanRed = Color(0xFFdd291a);
  static const Color germanYellow = Color(0xFFfdb922);
  
  // Message colors
  static const Color userMessageGradientStart = Color(0xFF64748b);
  static const Color userMessageGradientEnd = Color(0xFF475569);
  static const Color aiMessageBackground = Color(0xFFffffff);
  static const Color aiMessageBorder = Color(0xFFe2e8f0);
}
```

### Gradients

```dart
class AppGradients {
  static const LinearGradient mainBackground = LinearGradient(
    colors: [
      AppColors.slate600,
      AppColors.slate700,
      AppColors.slate800,
      AppColors.slate900,
      AppColors.slate950,
    ],
    begin: Alignment(0, 1),
    end: Alignment(1, 0),
  );
  
  static const LinearGradient headerGradient = LinearGradient(
    colors: [AppColors.slate900, AppColors.slate800],
  );
  
  static const LinearGradient userMessageGradient = LinearGradient(
    colors: [AppColors.slate600, AppColors.slate700],
  );
}
```

### Typography

```dart
class AppTextStyles {
  static const TextStyle headerTitle = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: Color(0xFFf1f5f9),
    fontFamily: 'Inter',
  );
  
  static const TextStyle messageText = TextStyle(
    fontSize: 17,
    fontWeight: FontWeight.normal,
    lineHeight: 1.4,
    fontFamily: 'Inter',
  );
  
  static const TextStyle userMessageText = TextStyle(
    color: Color(0xFFf8fafc),
  );
  
  static const TextStyle aiMessageText = TextStyle(
    color: Color(0xFF1e293b),
  );
}
```

## Performance Considerations

### Audio Processing Optimization
- Use isolates for audio processing to prevent UI blocking
- Implement efficient PCM16 conversion matching Expo version
- Buffer management for smooth real-time streaming

### Memory Management
- Proper disposal of WebSocket connections and audio streams
- Message list pagination for long conversations
- Image and asset caching optimization

### UI Performance
- Use `const` constructors where possible
- Implement efficient list rendering with `ListView.builder`
- Optimize gradient rendering and animations

## Platform-Specific Considerations

### Android
- Microphone permission handling with proper rationale
- Audio focus management for recording
- Background processing limitations

### iOS
- AVAudioSession configuration for recording
- App lifecycle handling during recording
- iOS-specific permission dialogs

## Security Considerations

### Audio Data Protection
- Secure WebSocket connections (WSS)
- No local audio storage (streaming only)
- Proper cleanup of audio buffers

### Network Security
- Certificate pinning for backend connections
- Input validation for WebSocket messages
- Rate limiting for audio streaming

## Deployment Strategy

### Build Configuration
- Separate configurations for development, staging, and production
- Environment-specific WebSocket URLs
- Proper code obfuscation for release builds

### App Store Preparation
- Microphone usage description for both platforms
- Privacy policy compliance
- App icon and metadata matching DeutschKorrekt branding