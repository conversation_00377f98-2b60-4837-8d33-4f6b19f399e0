import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter/material.dart';
import 'package:deutschkorrekt_flutter/main.dart' as app;
import 'package:deutschkorrekt_flutter/core/config/supabase_config.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Authentication Integration Tests', () {
    setUpAll(() async {
      // Initialize Supabase for testing
      await SupabaseConfig.initialize();
    });

    testWidgets('Complete authentication flow - signup, login, logout', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Should show auth screen initially
      expect(find.text('DeutschKorrekt'), findsOneWidget);
      expect(find.text('Sign In'), findsOneWidget);

      // Test signup flow
      await _testSignupFlow(tester);
      
      // Test login flow
      await _testLoginFlow(tester);
      
      // Test logout flow
      await _testLogoutFlow(tester);
    });

    testWidgets('Email validation during signup', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Switch to signup mode
      await tester.tap(find.text('Sign up'));
      await tester.pumpAndSettle();

      // Enter invalid email
      await tester.enterText(find.byType(TextFormField).first, 'invalid-email');
      await tester.enterText(find.byType(TextFormField).at(1), 'password123');
      await tester.enterText(find.byType(TextFormField).at(2), 'password123');

      // Try to submit
      await tester.tap(find.text('Sign Up'));
      await tester.pumpAndSettle();

      // Should show validation error
      expect(find.text('Please enter a valid email address'), findsOneWidget);
    });

    testWidgets('Password validation during signup', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Switch to signup mode
      await tester.tap(find.text('Sign up'));
      await tester.pumpAndSettle();

      // Enter short password
      await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(1), '123');
      await tester.enterText(find.byType(TextFormField).at(2), '123');

      // Try to submit
      await tester.tap(find.text('Sign Up'));
      await tester.pumpAndSettle();

      // Should show validation error
      expect(find.text('Password must be at least 8 characters long'), findsOneWidget);
    });

    testWidgets('Password confirmation validation', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Switch to signup mode
      await tester.tap(find.text('Sign up'));
      await tester.pumpAndSettle();

      // Enter mismatched passwords
      await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(1), 'password123');
      await tester.enterText(find.byType(TextFormField).at(2), 'different123');

      // Try to submit
      await tester.tap(find.text('Sign Up'));
      await tester.pumpAndSettle();

      // Should show validation error
      expect(find.text('Passwords do not match'), findsOneWidget);
    });

    testWidgets('Forgot password flow', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Tap forgot password
      await tester.tap(find.text('Forgot your password?'));
      await tester.pumpAndSettle();

      // Should show reset password dialog
      expect(find.text('Reset Password'), findsOneWidget);
      expect(find.text('Enter your email address'), findsOneWidget);

      // Enter email and submit
      await tester.enterText(find.byType(TextField), '<EMAIL>');
      await tester.tap(find.text('Send Reset Link'));
      await tester.pumpAndSettle();

      // Should show success message
      expect(find.text('Password reset email sent!'), findsOneWidget);
    });

    testWidgets('Google OAuth button interaction', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Should find Google sign in button
      expect(find.text('Sign in with Google'), findsOneWidget);

      // Tap Google sign in button
      await tester.tap(find.text('Sign in with Google'));
      await tester.pumpAndSettle();

      // Note: In a real test, this would open OAuth flow
      // For integration testing, we would mock the OAuth response
    });
  });
}

Future<void> _testSignupFlow(WidgetTester tester) async {
  // Switch to signup mode
  await tester.tap(find.text('Sign up'));
  await tester.pumpAndSettle();

  expect(find.text('Sign Up'), findsOneWidget);
  expect(find.text('Join us today!'), findsOneWidget);

  // Fill signup form
  final testEmail = 'test_${DateTime.now().millisecondsSinceEpoch}@example.com';
  await tester.enterText(find.byType(TextFormField).first, testEmail);
  await tester.enterText(find.byType(TextFormField).at(1), 'password123');
  await tester.enterText(find.byType(TextFormField).at(2), 'password123');

  // Submit signup
  await tester.tap(find.text('Sign Up'));
  await tester.pumpAndSettle();

  // Should show success message or email verification prompt
  // Note: Actual behavior depends on Supabase configuration
}

Future<void> _testLoginFlow(WidgetTester tester) async {
  // Should be on login screen
  expect(find.text('Sign In'), findsOneWidget);

  // Fill login form with test credentials
  await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
  await tester.enterText(find.byType(TextFormField).at(1), 'password123');

  // Submit login
  await tester.tap(find.text('Sign In'));
  await tester.pumpAndSettle();

  // Should navigate to chat screen on successful login
  // Note: This would require valid test credentials
}

Future<void> _testLogoutFlow(WidgetTester tester) async {
  // Should be on chat screen after login
  // Tap profile button
  await tester.tap(find.byIcon(Icons.person));
  await tester.pumpAndSettle();

  // Should show profile popup
  expect(find.text('Profile'), findsOneWidget);

  // Tap sign out button
  await tester.tap(find.text('Sign Out'));
  await tester.pumpAndSettle();

  // Should show confirmation dialog
  expect(find.text('Are you sure you want to sign out?'), findsOneWidget);

  // Confirm logout
  await tester.tap(find.text('Sign Out').last);
  await tester.pumpAndSettle();

  // Should return to auth screen
  expect(find.text('Sign In'), findsOneWidget);
}