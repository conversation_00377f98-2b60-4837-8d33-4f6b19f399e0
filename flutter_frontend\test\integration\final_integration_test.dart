import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter/material.dart';
import 'package:deutschkorrekt_flutter/main.dart' as app;
import 'package:deutschkorrekt_flutter/core/config/supabase_config.dart';
import 'package:deutschkorrekt_flutter/data/services/session_tracker.dart';
import 'package:deutschkorrekt_flutter/data/services/credit_manager.dart';
import 'package:deutschkorrekt_flutter/core/services/analytics_service.dart';
import 'package:deutschkorrekt_flutter/core/services/monitoring_service.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Final Integration Tests', () {
    late SessionTracker sessionTracker;
    late CreditManager creditManager;
    late AnalyticsService analytics;
    late MonitoringService monitoring;
    late String testEmail;

    setUpAll(() async {
      // Initialize all services
      await SupabaseConfig.initialize();
      
      sessionTracker = SessionTracker();
      creditManager = CreditManager();
      analytics = AnalyticsService();
      monitoring = MonitoringService();
      
      await analytics.initialize();
      await monitoring.initialize();
      
      testEmail = 'final_test_${DateTime.now().millisecondsSinceEpoch}@example.com';
    });

    testWidgets('Complete user journey with all systems integrated', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Test 1: Authentication Flow
      await _testCompleteAuthFlow(tester, testEmail);
      
      // Test 2: Profile Management Integration
      await _testProfileManagementIntegration(tester);
      
      // Test 3: Credit System Integration
      await _testCreditSystemIntegration(tester);
      
      // Test 4: Session Tracking Integration
      await _testSessionTrackingIntegration(tester);
      
      // Test 5: Analytics Integration
      await _testAnalyticsIntegration(tester);
      
      // Test 6: Error Handling Integration
      await _testErrorHandlingIntegration(tester);
      
      // Test 7: Final Cleanup
      await _testFinalCleanup(tester);
    });

    testWidgets('Credit consumption and session tracking integration', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login
      await _performLogin(tester, testEmail);
      
      // Verify initial credit state
      final initialStatus = await creditManager.getCreditStatus(testEmail);
      expect(initialStatus.currentCredits, equals(20)); // Trial plan default
      
      // Simulate session with credit consumption
      final sessionResult = await sessionTracker.logSession(
        email: testEmail,
        message: 'Test integration message',
        response: 'Test integration response',
      );
      
      expect(sessionResult.success, isTrue);
      expect(sessionResult.updatedProfile!.currentCredits, equals(19));
      
      // Verify session was logged
      final sessions = await sessionTracker.getRecentSessions(testEmail, limit: 1);
      expect(sessions.length, equals(1));
      expect(sessions.first.message, equals('Test integration message'));
    });

    testWidgets('Profile popup integration with real data', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login
      await _performLogin(tester, testEmail);
      
      // Generate some session data
      await _generateTestSessions(testEmail);
      
      // Open profile popup
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();

      // Verify profile data is displayed
      expect(find.text('Profile'), findsOneWidget);
      expect(find.text(testEmail), findsOneWidget);
      expect(find.text('Trial'), findsOneWidget);
      expect(find.text('Credits'), findsOneWidget);
      expect(find.text('Statistics'), findsOneWidget);
      
      // Verify credit information
      expect(find.textContaining('of 20 credits'), findsOneWidget);
      
      // Verify statistics are shown
      expect(find.text('Total Sessions'), findsOneWidget);
      
      // Close profile
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();
    });

    testWidgets('Error handling across all systems', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Test authentication error handling
      await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(1), 'wrongpassword');
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();

      // Should show error message
      expect(find.textContaining('Invalid email or password'), findsOneWidget);
      
      // Test form validation
      await tester.enterText(find.byType(TextFormField).first, 'invalid-email');
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();

      expect(find.text('Please enter a valid email address'), findsOneWidget);
    });

    testWidgets('Analytics and monitoring integration', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Track app initialization
      analytics.trackAppLifecycle('app_started');
      
      // Track screen view
      analytics.trackScreenView('auth_screen');
      
      // Perform login to generate events
      await _performLogin(tester, testEmail);
      
      // Track successful login
      analytics.trackAuth('login_success', properties: {
        'method': 'email',
        'user_id': testEmail,
      });
      
      // Check analytics summary
      final analyticsSummary = analytics.getAnalyticsSummary();
      expect(analyticsSummary['is_initialized'], isTrue);
      expect(analyticsSummary['queued_events'], greaterThan(0));
      
      // Run health checks
      final healthReport = await monitoring.runHealthChecks();
      expect(healthReport.status, isNotNull);
      expect(healthReport.checks, isNotEmpty);
    });

    testWidgets('Session management and token refresh', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login
      await _performLogin(tester, testEmail);
      
      // Verify user is authenticated
      expect(find.byIcon(Icons.person), findsOneWidget); // Profile button visible
      
      // Test logout
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();
      
      await tester.tap(find.text('Sign Out'));
      await tester.pumpAndSettle();
      
      await tester.tap(find.text('Sign Out').last); // Confirm
      await tester.pumpAndSettle();
      
      // Should return to auth screen
      expect(find.text('Sign In'), findsOneWidget);
    });
  });
}

Future<void> _testCompleteAuthFlow(WidgetTester tester, String email) async {
  // Test signup
  await tester.tap(find.text('Sign up'));
  await tester.pumpAndSettle();

  await tester.enterText(find.byType(TextFormField).first, email);
  await tester.enterText(find.byType(TextFormField).at(1), 'password123');
  await tester.enterText(find.byType(TextFormField).at(2), 'password123');

  await tester.tap(find.text('Sign Up'));
  await tester.pumpAndSettle();

  // Wait for signup to complete
  await tester.pumpAndSettle(const Duration(seconds: 2));
}

Future<void> _testProfileManagementIntegration(WidgetTester tester) async {
  // Open profile
  await tester.tap(find.byIcon(Icons.person));
  await tester.pumpAndSettle();

  // Verify profile elements
  expect(find.text('Profile'), findsOneWidget);
  expect(find.text('Credits'), findsOneWidget);
  expect(find.text('Statistics'), findsOneWidget);

  // Test refresh functionality
  await tester.tap(find.text('Refresh Profile'));
  await tester.pumpAndSettle();

  // Close profile
  await tester.tap(find.byIcon(Icons.close));
  await tester.pumpAndSettle();
}

Future<void> _testCreditSystemIntegration(WidgetTester tester) async {
  // This would test credit consumption through the UI
  // For now, we'll verify the credit display in profile
  await tester.tap(find.byIcon(Icons.person));
  await tester.pumpAndSettle();

  // Should show credit information
  expect(find.textContaining('credits'), findsOneWidget);

  await tester.tap(find.byIcon(Icons.close));
  await tester.pumpAndSettle();
}

Future<void> _testSessionTrackingIntegration(WidgetTester tester) async {
  // This would test session tracking through actual chat interactions
  // For now, we'll verify the statistics display
  await tester.tap(find.byIcon(Icons.person));
  await tester.pumpAndSettle();

  expect(find.text('Statistics'), findsOneWidget);

  await tester.tap(find.byIcon(Icons.close));
  await tester.pumpAndSettle();
}

Future<void> _testAnalyticsIntegration(WidgetTester tester) async {
  // Analytics integration is tested through service calls
  final analytics = AnalyticsService();
  
  analytics.trackScreenView('chat_screen');
  analytics.trackInteraction('profile_button', 'tap');
  
  final summary = analytics.getAnalyticsSummary();
  expect(summary['queued_events'], greaterThan(0));
}

Future<void> _testErrorHandlingIntegration(WidgetTester tester) async {
  // Test form validation errors
  await tester.enterText(find.byType(TextFormField).first, 'invalid-email');
  await tester.tap(find.text('Sign In'));
  await tester.pumpAndSettle();

  expect(find.text('Please enter a valid email address'), findsOneWidget);
}

Future<void> _testFinalCleanup(WidgetTester tester) async {
  // Ensure we end in a clean state
  if (find.text('Sign In').evaluate().isEmpty) {
    // If not on auth screen, logout
    await tester.tap(find.byIcon(Icons.person));
    await tester.pumpAndSettle();
    
    await tester.tap(find.text('Sign Out'));
    await tester.pumpAndSettle();
    
    await tester.tap(find.text('Sign Out').last);
    await tester.pumpAndSettle();
  }
  
  expect(find.text('Sign In'), findsOneWidget);
}

Future<void> _performLogin(WidgetTester tester, String email) async {
  await tester.enterText(find.byType(TextFormField).first, email);
  await tester.enterText(find.byType(TextFormField).at(1), 'password123');
  await tester.tap(find.text('Sign In'));
  await tester.pumpAndSettle();
}

Future<void> _generateTestSessions(String email) async {
  final sessionTracker = SessionTracker();
  
  // Generate a few test sessions
  for (int i = 0; i < 3; i++) {
    await sessionTracker.logSession(
      email: email,
      message: 'Test message $i',
      response: 'Test response $i',
    );
  }
}