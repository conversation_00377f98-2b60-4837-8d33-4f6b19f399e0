import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/data/models/session_data.dart';

void main() {
  group('SessionData', () {
    late SessionData testSession;
    late DateTime testDate;

    setUp(() {
      testDate = DateTime(2024, 1, 15, 10, 30);
      testSession = SessionData(
        sessionId: 1,
        email: '<EMAIL>',
        message: 'Hello, how are you?',
        response: 'I am fine, thank you!',
        datetime: testDate,
      );
    });

    group('JSON serialization', () {
      test('should serialize to JSON correctly', () {
        final json = testSession.toJson();
        
        expect(json['session_id'], equals(1));
        expect(json['email'], equals('<EMAIL>'));
        expect(json['message'], equals('Hello, how are you?'));
        expect(json['response'], equals('I am fine, thank you!'));
        expect(json['datetime'], equals(testDate.toIso8601String()));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'session_id': 1,
          'email': '<EMAIL>',
          'message': 'Hello, how are you?',
          'response': 'I am fine, thank you!',
          'datetime': '2024-01-15T10:30:00.000',
        };

        final session = SessionData.fromJson(json);
        
        expect(session.sessionId, equals(1));
        expect(session.email, equals('<EMAIL>'));
        expect(session.message, equals('Hello, how are you?'));
        expect(session.response, equals('I am fine, thank you!'));
      });

      test('should create insert JSON without sessionId', () {
        final insertJson = testSession.toInsertJson();
        
        expect(insertJson.containsKey('session_id'), isFalse);
        expect(insertJson['email'], equals('<EMAIL>'));
        expect(insertJson['message'], equals('Hello, how are you?'));
        expect(insertJson['response'], equals('I am fine, thank you!'));
      });
    });

    group('Factory constructors', () {
      test('should create session with current datetime', () {
        final session = SessionData.create(
          email: '<EMAIL>',
          message: 'Test message',
          response: 'Test response',
        );

        expect(session.email, equals('<EMAIL>'));
        expect(session.message, equals('Test message'));
        expect(session.response, equals('Test response'));
        expect(session.sessionId, isNull);
        
        // Should be created with current time (within 1 second)
        final now = DateTime.now();
        expect(session.datetime.difference(now).inSeconds.abs(), lessThan(1));
      });

      test('should create session with specified datetime', () {
        final customDate = DateTime(2024, 2, 1, 15, 45);
        final session = SessionData.create(
          email: '<EMAIL>',
          message: 'Test message',
          response: 'Test response',
          datetime: customDate,
        );

        expect(session.datetime, equals(customDate));
      });
    });

    group('Utility methods', () {
      test('should calculate message and response lengths', () {
        expect(testSession.messageLength, equals(18)); // "Hello, how are you?"
        expect(testSession.responseLength, equals(20)); // "I am fine, thank you!"
      });

      test('should check if session is recent', () {
        final recentSession = SessionData.create(
          email: '<EMAIL>',
          message: 'Recent message',
          response: 'Recent response',
        );
        
        expect(recentSession.isRecent, isTrue);
        
        final oldSession = testSession.copyWith(
          datetime: DateTime.now().subtract(const Duration(hours: 2)),
        );
        
        expect(oldSession.isRecent, isFalse);
      });

      test('should format datetime correctly', () {
        final formattedDatetime = testSession.formattedDatetime;
        expect(formattedDatetime, contains('2024-01-15'));
        expect(formattedDatetime, contains('10:30:00'));
      });
    });

    group('copyWith', () {
      test('should create copy with updated fields', () {
        final updatedSession = testSession.copyWith(
          message: 'Updated message',
          response: 'Updated response',
        );

        expect(updatedSession.sessionId, equals(testSession.sessionId));
        expect(updatedSession.email, equals(testSession.email));
        expect(updatedSession.message, equals('Updated message'));
        expect(updatedSession.response, equals('Updated response'));
        expect(updatedSession.datetime, equals(testSession.datetime));
      });
    });

    group('Equality and toString', () {
      test('should implement equality correctly', () {
        final identicalSession = SessionData(
          sessionId: 1,
          email: '<EMAIL>',
          message: 'Hello, how are you?',
          response: 'I am fine, thank you!',
          datetime: testDate,
        );

        expect(testSession, equals(identicalSession));
        expect(testSession.hashCode, equals(identicalSession.hashCode));
      });

      test('should have meaningful toString', () {
        final stringRepresentation = testSession.toString();
        expect(stringRepresentation, contains('SessionData'));
        expect(stringRepresentation, contains('<EMAIL>'));
        expect(stringRepresentation, contains('1'));
      });
    });

    group('Edge cases', () {
      test('should handle null sessionId', () {
        final sessionWithoutId = SessionData(
          email: '<EMAIL>',
          message: 'Test message',
          response: 'Test response',
          datetime: testDate,
        );

        expect(sessionWithoutId.sessionId, isNull);
        
        final json = sessionWithoutId.toJson();
        expect(json.containsKey('session_id'), isFalse);
      });

      test('should handle empty messages and responses', () {
        final emptySession = SessionData(
          email: '<EMAIL>',
          message: '',
          response: '',
          datetime: testDate,
        );

        expect(emptySession.messageLength, equals(0));
        expect(emptySession.responseLength, equals(0));
      });
    });
  });
}