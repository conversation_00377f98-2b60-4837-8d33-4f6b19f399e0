{"name": "deutschkorrekt-mobile", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"@deepgram/sdk": "^4.9.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.1.0", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-audio-record": "^0.2.2", "react-native-gesture-handler": "^2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-live-audio-stream": "^1.1.1", "react-native-reanimated": "^3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "^15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-webview": "^13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/code-frame": "^7.25.2", "@babel/preset-env": "^7.25.2", "@babel/runtime": "^7.25.2", "@react-native-community/cli": "latest", "@react-native/babel-preset": "^0.79.5", "@react-native/eslint-config": "^0.79.5", "@react-native/metro-config": "^0.79.5", "@react-native/typescript-config": "^0.79.5", "@types/react": "^19.0.10", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^29.7.0", "eslint": "^8.57.0", "jest": "^29.7.0", "metro": "^0.81.0", "metro-resolver": "^0.81.0", "prettier": "^3.3.3", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18"}}