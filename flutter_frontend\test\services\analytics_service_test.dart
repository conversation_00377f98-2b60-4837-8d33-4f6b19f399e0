import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/core/services/analytics_service.dart';

void main() {
  group('AnalyticsService', () {
    late AnalyticsService analyticsService;

    setUp(() {
      analyticsService = AnalyticsService();
    });

    tearDown(() {
      analyticsService.dispose();
    });

    group('Initialization', () {
      test('should initialize with user properties', () async {
        await analyticsService.initialize(
          userId: 'test_user_123',
          userProperties: {
            'plan': 'premium',
            'signup_date': '2024-01-01',
          },
        );

        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['is_initialized'], isTrue);
        expect(summary['user_properties'], greaterThan(0));
      });

      test('should initialize without user properties', () async {
        await analyticsService.initialize();

        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['is_initialized'], isTrue);
      });
    });

    group('Event Tracking', () {
      setUp(() async {
        await analyticsService.initialize(userId: 'test_user');
      });

      test('should track basic events', () {
        analyticsService.trackEvent('test_event');
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(1));
      });

      test('should track events with properties', () {
        analyticsService.trackEvent('test_event', {
          'property1': 'value1',
          'property2': 42,
        });
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(1));
      });

      test('should track authentication events', () {
        analyticsService.trackAuth('login', properties: {
          'method': 'email',
        });
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(1));
      });

      test('should track profile events', () {
        analyticsService.trackProfile('updated', properties: {
          'field': 'email',
        });
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(1));
      });

      test('should track credit events', () {
        analyticsService.trackCredit('consumed', properties: {
          'remaining': 19,
        });
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(1));
      });

      test('should track session events', () {
        analyticsService.trackSession('started', properties: {
          'session_id': 'session_123',
        });
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(1));
      });

      test('should track error events', () {
        analyticsService.trackError('Network error', context: 'login');
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(1));
      });

      test('should track performance metrics', () {
        analyticsService.trackPerformance('response_time', 250.0, unit: 'ms');
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(1));
      });

      test('should track screen views', () {
        analyticsService.trackScreenView('login_screen');
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(1));
      });

      test('should track user interactions', () {
        analyticsService.trackInteraction('login_button', 'tap');
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(1));
      });

      test('should track app lifecycle events', () {
        analyticsService.trackAppLifecycle('resumed');
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(1));
      });
    });

    group('Timer Functionality', () {
      setUp(() async {
        await analyticsService.initialize(userId: 'test_user');
      });

      test('should start and stop timers', () {
        analyticsService.startTimer('test_operation');
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['active_timers'], equals(1));
        
        analyticsService.stopTimer('test_operation');
        
        final updatedSummary = analyticsService.getAnalyticsSummary();
        expect(updatedSummary['active_timers'], equals(0));
        expect(updatedSummary['queued_events'], equals(1)); // Performance event
      });

      test('should handle stopping non-existent timer', () {
        analyticsService.stopTimer('non_existent_timer');
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(0));
      });
    });

    group('User Properties', () {
      setUp(() async {
        await analyticsService.initialize(userId: 'test_user');
      });

      test('should set user properties', () {
        analyticsService.setUserProperties({
          'plan': 'premium',
          'age': 25,
        });
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(1)); // user_properties_updated event
      });

      test('should set user ID', () {
        analyticsService.setUserId('new_user_123');
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(1)); // user_id_set event
      });
    });

    group('Event Queue Management', () {
      setUp(() async {
        await analyticsService.initialize(userId: 'test_user');
      });

      test('should queue multiple events', () {
        analyticsService.trackEvent('event1');
        analyticsService.trackEvent('event2');
        analyticsService.trackEvent('event3');
        
        final summary = analyticsService.getAnalyticsSummary();
        expect(summary['queued_events'], equals(3));
      });

      test('should handle events when not initialized', () {
        final uninitializedService = AnalyticsService();
        uninitializedService.trackEvent('test_event');
        
        final summary = uninitializedService.getAnalyticsSummary();
        expect(summary['is_initialized'], isFalse);
        expect(summary['queued_events'], equals(0));
      });
    });
  });

  group('AnalyticsEvent', () {
    test('should create event with all properties', () {
      final event = AnalyticsEvent(
        name: 'test_event',
        properties: {'key': 'value'},
        timestamp: DateTime.now(),
      );

      expect(event.name, equals('test_event'));
      expect(event.properties['key'], equals('value'));
      expect(event.timestamp, isA<DateTime>());
    });

    test('should serialize to JSON', () {
      final timestamp = DateTime.now();
      final event = AnalyticsEvent(
        name: 'test_event',
        properties: {'key': 'value'},
        timestamp: timestamp,
      );

      final json = event.toJson();
      
      expect(json['name'], equals('test_event'));
      expect(json['properties']['key'], equals('value'));
      expect(json['timestamp'], equals(timestamp.toIso8601String()));
    });

    test('should deserialize from JSON', () {
      final timestamp = DateTime.now();
      final json = {
        'name': 'test_event',
        'properties': {'key': 'value'},
        'timestamp': timestamp.toIso8601String(),
      };

      final event = AnalyticsEvent.fromJson(json);
      
      expect(event.name, equals('test_event'));
      expect(event.properties['key'], equals('value'));
      expect(event.timestamp, equals(timestamp));
    });
  });

  group('AnalyticsMixin', () {
    test('should provide analytics methods', () {
      final testClass = TestClassWithAnalytics();
      
      // Test that methods are available and don't throw
      expect(() => testClass.trackEvent('test'), returnsNormally);
      expect(() => testClass.trackScreenView('test_screen'), returnsNormally);
      expect(() => testClass.trackInteraction('button', 'tap'), returnsNormally);
      expect(() => testClass.trackError('test error'), returnsNormally);
      expect(() => testClass.startTimer('test_timer'), returnsNormally);
      expect(() => testClass.stopTimer('test_timer'), returnsNormally);
    });
  });
}

class TestClassWithAnalytics with AnalyticsMixin {
  // Test class to verify mixin functionality
}