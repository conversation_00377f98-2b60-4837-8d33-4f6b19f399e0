import 'package:flutter/material.dart';
import '../../../data/services/session_tracker.dart';

/// Profile statistics widget showing session and usage statistics
class ProfileStats extends StatelessWidget {
  final SessionStats? sessionStats;
  final bool isLoading;

  const ProfileStats({
    super.key,
    this.sessionStats,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Stats header
          Row(
            children: [
              Icon(
                Icons.analytics,
                size: 20,
                color: Colors.grey[700],
              ),
              const SizedBox(width: 8),
              Text(
                'Statistics',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                  fontFamily: 'Inter',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          if (isLoading)
            _buildLoadingState()
          else if (sessionStats != null)
            _buildStatsContent()
          else
            _buildNoDataState(),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 12),
          Text(
            'Loading statistics...',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
              fontFamily: 'Inter',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoDataState() {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.bar_chart,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 12),
          Text(
            'No statistics available',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontFamily: 'Inter',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsContent() {
    return Column(
      children: [
        // Main stats grid
        _buildStatsGrid(),
        
        const SizedBox(height: 16),
        
        // Recent activity
        _buildRecentActivity(),
      ],
    );
  }

  Widget _buildStatsGrid() {
    final stats = sessionStats!;
    
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 2.5,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      children: [
        _buildStatCard(
          'Total Sessions',
          stats.totalSessions.toString(),
          Icons.chat_bubble_outline,
          Colors.blue,
        ),
        _buildStatCard(
          'Credits Used',
          stats.creditsUsed.toString(),
          Icons.account_balance_wallet,
          Colors.orange,
        ),
        _buildStatCard(
          'This Week',
          stats.sessionsThisWeek.toString(),
          Icons.calendar_week,
          Colors.green,
        ),
        _buildStatCard(
          'This Month',
          stats.sessionsThisMonth.toString(),
          Icons.calendar_month,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 20,
            color: color,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'Inter',
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color.withOpacity(0.8),
              fontFamily: 'Inter',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    final stats = sessionStats!;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Activity',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
              fontFamily: 'Inter',
            ),
          ),
          
          const SizedBox(height: 12),
          
          _buildActivityItem(
            'Today',
            '${stats.sessionsToday} sessions',
            Icons.today,
            Colors.blue,
          ),
          
          const SizedBox(height: 8),
          
          _buildActivityItem(
            'Average Message',
            '${stats.averageMessageLength.toStringAsFixed(0)} characters',
            Icons.message,
            Colors.green,
          ),
          
          const SizedBox(height: 8),
          
          _buildActivityItem(
            'Average Response',
            '${stats.averageResponseLength.toStringAsFixed(0)} characters',
            Icons.reply,
            Colors.orange,
          ),
          
          if (stats.lastSession != null) ...[
            const SizedBox(height: 8),
            _buildActivityItem(
              'Last Session',
              _formatLastSession(stats.lastSession!),
              Icons.access_time,
              Colors.purple,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActivityItem(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: color,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
              fontFamily: 'Inter',
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: color,
            fontFamily: 'Inter',
          ),
        ),
      ],
    );
  }

  String _formatLastSession(DateTime lastSession) {
    final now = DateTime.now();
    final difference = now.difference(lastSession);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${lastSession.day}/${lastSession.month}/${lastSession.year}';
    }
  }
}