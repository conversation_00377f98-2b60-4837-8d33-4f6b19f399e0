import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/services/permissions_service.dart';

/// Dialog for requesting microphone permissions with proper user guidance
class PermissionDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onGranted;
  final VoidCallback? onDenied;
  final bool showSettingsOption;
  
  const PermissionDialog({
    super.key,
    required this.title,
    required this.message,
    this.onGranted,
    this.onDenied,
    this.showSettingsOption = false,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppColors.slate800,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Icon(
            Icons.mic,
            color: AppColors.infoBlue,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: AppTextStyles.dialogTitle.copyWith(
                color: AppColors.lightText,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            message,
            style: AppTextStyles.dialogContent.copyWith(
              color: AppColors.lightText.withOpacity(0.9),
            ),
          ),
          
          if (showSettingsOption) ...[
            const SizedBox(height: 16),
            _buildPermissionInstructions(),
          ],
        ],
      ),
      actions: [
        if (showSettingsOption)
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await PermissionsService.instance.openAppSettings();
            },
            child: Text(
              'Open Settings',
              style: AppTextStyles.dialogAction.copyWith(
                color: AppColors.infoBlue,
              ),
            ),
          ),
        
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            onDenied?.call();
          },
          child: Text(
            'Cancel',
            style: AppTextStyles.dialogAction.copyWith(
              color: AppColors.lightText.withOpacity(0.7),
            ),
          ),
        ),
        
        if (!showSettingsOption)
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onGranted?.call();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.infoBlue,
              foregroundColor: AppColors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Grant Permission',
              style: AppTextStyles.dialogAction.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }
  
  Widget _buildPermissionInstructions() {
    final instructions = PermissionsService.instance.getPermissionInstructions();
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.slate700.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.infoBlue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'To enable microphone access:',
            style: AppTextStyles.captionText.copyWith(
              color: AppColors.lightText,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          ...instructions.asMap().entries.map((entry) {
            final index = entry.key;
            final instruction = entry.value;
            
            return Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: AppColors.infoBlue.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: AppTextStyles.captionText.copyWith(
                          fontSize: 10,
                          color: AppColors.infoBlue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      instruction,
                      style: AppTextStyles.captionText.copyWith(
                        color: AppColors.lightText.withOpacity(0.8),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }
}

/// Show permission dialog with proper context
Future<bool?> showPermissionDialog(
  BuildContext context, {
  String? title,
  String? message,
  bool showSettingsOption = false,
}) async {
  final permissionsService = PermissionsService.instance;
  final guidance = permissionsService.getPermissionGuidance();
  
  return await showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => PermissionDialog(
      title: title ?? guidance['title']!,
      message: message ?? guidance['message']!,
      showSettingsOption: showSettingsOption,
      onGranted: () async {
        final granted = await permissionsService.requestMicrophonePermission();
        Navigator.of(context).pop(granted);
      },
      onDenied: () {
        Navigator.of(context).pop(false);
      },
    ),
  );
}

/// Show permission denied dialog with settings option
Future<void> showPermissionDeniedDialog(BuildContext context) async {
  final permissionsService = PermissionsService.instance;
  
  await showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => PermissionDialog(
      title: 'Microphone Access Required',
      message: 'Microphone permission was denied. To use voice features, please enable microphone access in your device settings.',
      showSettingsOption: true,
      onDenied: () {
        Navigator.of(context).pop();
      },
    ),
  );
}

/// Show permission permanently denied dialog
Future<void> showPermissionPermanentlyDeniedDialog(BuildContext context) async {
  await showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => PermissionDialog(
      title: 'Microphone Access Blocked',
      message: 'Microphone permission has been permanently denied. Please enable it manually in your device settings to use voice features.',
      showSettingsOption: true,
      onDenied: () {
        Navigator.of(context).pop();
      },
    ),
  );
}