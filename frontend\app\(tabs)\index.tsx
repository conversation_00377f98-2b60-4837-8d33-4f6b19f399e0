import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ScrollView,
  Image,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Mic, Square, CircleCheck as CheckCircle, CircleAlert as AlertCircle, Settings, User, X } from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAudioStreaming } from '@/hooks/useAudioStreaming';
import { FormattedText } from '@/components/FormattedText';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  isAudio?: boolean;
  isStreaming?: boolean;
  isProcessing?: boolean;
  correctionResult?: {
    original_text: string;
    corrected_text: string;
    suggestions: string[];
    explanations: string[];
    processing_time: number;
  };
  translationResult?: {
    original_text: string;
    translations: string[];
    processing_time: number;
  };
}

export default function ChatScreen() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hallo! Ich bin DeutschKorrekt, dein persönlicher Sprachtrainer. Ich helfe dir, dein Deutsch zu verbessern!',
      isUser: false,
      timestamp: new Date(),
    }
  ]);
  const flatListRef = useRef<FlatList>(null);
  const [currentMessageId, setCurrentMessageId] = useState<string | null>(null);
  const currentMessageIdRef = useRef<string | null>(null); // Use ref for immediate access
  const [showSettings, setShowSettings] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [isPreWarmed, setIsPreWarmed] = useState(false);

  const {
    isConnected,
    isStreaming,
    isProcessing,
    isConnecting,
    partialText,
    finalText,
    retryCount,
    connect,
    disconnect,
    startStreaming: startStreamingHook,
  } = useAudioStreaming({
    backendUrl: 'wss://deutschkorrekt-backend-645996191396.europe-west3.run.app',
    onPartialTranscript: (text) => {
      console.log('🎯 Partial transcript:', text);
      // Use ref for immediate access to current message ID
      const messageId = currentMessageIdRef.current;
      if (messageId) {
        setMessages(prev => prev.map(msg =>
          msg.id === messageId
            ? { ...msg, text }  // Replace with latest partial transcript
            : msg
        ));
      }
    },
    onFinalTranscript: (text) => {
      console.log('🎯 Final transcript:', text);
      // Use ref for immediate access to current message ID
      const messageId = currentMessageIdRef.current;
      if (messageId) {
        setMessages(prev => prev.map(msg =>
          msg.id === messageId
            ? { ...msg, text, isStreaming: false, isProcessing: false }
            : msg
        ));
        // Clear currentMessageId when we get final transcript (session ended)
        setCurrentMessageId(null);
        currentMessageIdRef.current = null;
        console.log('🔄 Cleared currentMessageId after final transcript');
      }
    },
    onCorrection: (result) => {
      // Update the current message with correction result
      if (currentMessageId) {
        setMessages(prev => prev.map(msg =>
          msg.id === currentMessageId
            ? { ...msg, isProcessing: false, correctionResult: result }
            : msg
        ));
      }
    },
    onTranslation: (result) => {
      // Update the current message with translation result
      if (currentMessageId) {
        setMessages(prev => prev.map(msg =>
          msg.id === currentMessageId
            ? { ...msg, isProcessing: false, translationResult: result }
            : msg
        ));
      }
    },
    onError: (error) => {
      console.error('STT Error:', error);
      if (currentMessageId) {
        setMessages(prev => prev.map(msg =>
          msg.id === currentMessageId
            ? { ...msg, text: `Error: ${error}`, isStreaming: false, isProcessing: false }
            : msg
        ));
      }
    },
    onGroqResponse: (result) => {
      console.log('🤖 Groq response received:', result);
      
      // Create a new AI message with the Groq response
      const aiMessage: Message = {
        id: `groq_${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        text: result.response_text,
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => {
        const newMessages = [...prev, aiMessage];
        
        // The user's message should be the second-to-last message (AI response is last)
        // Find the most recent user message
        let userMessageIndex = -1;
        for (let i = newMessages.length - 2; i >= 0; i--) {
          if (newMessages[i].isUser) {
            userMessageIndex = i;
            break;
          }
        }
        
        console.log('📍 User message index:', userMessageIndex, 'Total messages:', newMessages.length);
        
        // Scroll to show the user's message at the top after a brief delay
        if (userMessageIndex >= 0) {
          setTimeout(() => {
            console.log('📜 Scrolling to user message at index:', userMessageIndex);
            try {
              flatListRef.current?.scrollToIndex({ 
                index: userMessageIndex, 
                animated: true,
                viewPosition: 0 // 0 = top of screen, 1 = bottom of screen
              });
            } catch (error) {
              console.log('📜 scrollToIndex failed, using scrollToOffset');
              // Fallback: estimate offset and scroll there
              const estimatedOffset = userMessageIndex * 150; // Rough estimate of message height
              flatListRef.current?.scrollToOffset({ 
                offset: estimatedOffset, 
                animated: true 
              });
            }
          }, 300);
        }
        
        return newMessages;
      });

      // Clear the current message ID immediately to reset button state
      setCurrentMessageId(null);
      currentMessageIdRef.current = null;
      console.log('🔄 Cleared currentMessageId after Groq response');

      // Ensure clean state after Groq response
      disconnect();
    },
    onTimeout: () => {
      console.log('Session timed out');
      if (currentMessageId) {
        setMessages(prev => prev.map(msg =>
          msg.id === currentMessageId
            ? { ...msg, text: msg.text || 'Session timed out', isStreaming: false, isProcessing: false }
            : msg
        ));
        setCurrentMessageId(null);
      }
    },
    onInfo: (message) => {
      console.log('STT Info:', message);
    },
  });

  // Pre-warm connection on app load
  useEffect(() => {
    const preWarmConnection = async () => {
      if (!isPreWarmed) {
        console.log('🔥 Pre-warming connection...');
        try {
          await connect();
          setTimeout(() => {
            disconnect();
            setIsPreWarmed(true);
            console.log('✅ Connection pre-warmed');
          }, 1000);
        } catch (error) {
          console.log('⚠️ Pre-warm failed, will connect on demand');
          setIsPreWarmed(true);
        }
      }
    };
    
    preWarmConnection();
  }, [connect, disconnect, isPreWarmed]);

  // Removed duplicate partial text effect - handled in onPartialTranscript callback

  const renderMessage = ({ item }: { item: Message }) => {
    return (
      <View style={[styles.messageContainer, item.isUser ? styles.userMessage : styles.aiMessage]}>
        <View style={item.isUser ? styles.userBubbleContainer : styles.aiBubbleContainer}>
          {item.isUser ? (
            <LinearGradient
              colors={['#64748b', '#475569']}
              style={styles.messageBubble}
            >
              <Text style={[styles.messageText, styles.userText]}>
                {item.text}
              </Text>

              {/* Show streaming indicator */}
              {item.isStreaming && (
                <Text style={styles.streamingIndicator}>● Live transcription...</Text>
              )}

              {/* Show processing indicator */}
              {item.isProcessing && (
                <Text style={styles.processingIndicator}>⚡ Processing with AI agents...</Text>
              )}

              {/* Show correction results */}
              {item.correctionResult && (
                <View style={styles.resultContainer}>
                  <LinearGradient
                    colors={['#64748b', '#475569']}
                    style={styles.agentBadge}
                  >
                    <Text style={styles.agentBadgeText}>German Correction Agent</Text>
                  </LinearGradient>

                  <View style={styles.germanResultContainer}>
                    {item.correctionResult.corrected_text !== item.correctionResult.original_text && (
                      <View style={styles.correctionContainer}>
                        <Text style={styles.correctionTitle}>Correction:</Text>
                        <View style={styles.correctionItem}>
                          <Text style={styles.originalText}>Original: {item.correctionResult.original_text}</Text>
                          <Text style={styles.correctedText}>Corrected: {item.correctionResult.corrected_text}</Text>
                        </View>
                      </View>
                    )}

                    {item.correctionResult.suggestions.length > 0 && (
                      <View style={styles.suggestionsContainer}>
                        <Text style={styles.suggestionsTitle}>Suggestions:</Text>
                        {item.correctionResult.suggestions.map((suggestion, index) => (
                          <View key={index} style={styles.suggestionItem}>
                            <Text style={styles.suggestionText}>• {suggestion}</Text>
                          </View>
                        ))}
                      </View>
                    )}

                    {item.correctionResult.explanations.length > 0 && (
                      <View style={styles.explanationsContainer}>
                        <Text style={styles.explanationsTitle}>Explanations:</Text>
                        {item.correctionResult.explanations.map((explanation, index) => (
                          <View key={index} style={styles.explanationItem}>
                            <Text style={styles.explanationText}>{explanation}</Text>
                          </View>
                        ))}
                      </View>
                    )}
                  </View>
                </View>
              )}

              {/* Show translation results */}
              {item.translationResult && (
                <View style={styles.resultContainer}>
                  <LinearGradient
                    colors={['#64748b', '#475569']}
                    style={styles.agentBadge}
                  >
                    <Text style={styles.agentBadgeText}>English Translation Agent</Text>
                  </LinearGradient>

                  <View style={styles.englishResultContainer}>
                    <Text style={styles.originalEnglishText}>English: {item.translationResult.original_text}</Text>

                    <View style={styles.translationsContainer}>
                      <Text style={styles.translationsTitle}>German Translations:</Text>
                      {item.translationResult.translations.map((translation, index) => (
                        <View key={index} style={styles.translationItem}>
                          <Text style={styles.translationText}>{translation}</Text>
                        </View>
                      ))}
                    </View>
                  </View>
                </View>
              )}
            </LinearGradient>
          ) : (
            <View style={[styles.messageBubble, styles.aiBubble]}>
              <FormattedText
                text={item.text}
                style={[styles.messageText, styles.aiText]}
                boldStyle={styles.boldText}
              />
            </View>
          )}
        </View>
      </View>
    );
  };

  /**
   * Handle button press - only start streaming (auto-stop via Deepgram)
   */
  const handleButtonPress = async () => {
    // Only allow starting if not already active and properly connected
    if (!isStreaming && !currentMessageId && !isConnecting) {
      console.log('🎯 Button pressed - starting new session');
      await startStreaming();
    } else {
      console.log('🚫 Button press ignored - already active or connecting');
    }
  };

  /**
   * Start audio streaming session
   */
  const startStreaming = async () => {
    try {
      console.log('🚀 Starting streaming session...');
      console.log('🔍 Current state - isStreaming:', isStreaming, 'currentMessageId:', currentMessageId);

      // Create message for this session
      const messageId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      setCurrentMessageId(messageId);
      currentMessageIdRef.current = messageId; // Set ref immediately

      const newMessage: Message = {
        id: messageId,
        text: 'Connecting...',
        isUser: true,
        timestamp: new Date(),
        isAudio: true,
        isStreaming: true,
      };

      setMessages(prev => [...prev, newMessage]);

      // Start streaming (connection handled internally)
      await startStreamingHook();

      // Update message to show listening state
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, text: 'Listening...' }
          : msg
      ));

      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);

    } catch (error) {
      console.error('Failed to start streaming:', error);

      // Update message to show error
      if (currentMessageId) {
        setMessages(prev => prev.map(msg =>
          msg.id === currentMessageId
            ? { ...msg, text: 'Failed to start streaming', isStreaming: false }
            : msg
        ));
      }

      Alert.alert('Error', 'Failed to start audio streaming. Please check microphone permissions.');
    }
  };



  const SettingsModal = () => (
    <Modal
      visible={showSettings}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowSettings(false)}
    >
      <View style={styles.modalOverlay}>
        <LinearGradient
          colors={['#1e293b', '#334155']}
          style={styles.modalContent}
        >
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Einstellungen</Text>
            <TouchableOpacity onPress={() => setShowSettings(false)}>
              <X size={24} color="#f8fafc" />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalBody}>
            <Text style={styles.modalText}>Settings content will go here...</Text>
          </ScrollView>
        </LinearGradient>
      </View>
    </Modal>
  );

  const ProfileModal = () => (
    <Modal
      visible={showProfile}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowProfile(false)}
    >
      <View style={styles.modalOverlay}>
        <LinearGradient
          colors={['#1e293b', '#334155']}
          style={styles.modalContent}
        >
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Profil</Text>
            <TouchableOpacity onPress={() => setShowProfile(false)}>
              <X size={24} color="#f8fafc" />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalBody}>
            <Text style={styles.modalText}>Profile content will go here...</Text>
          </ScrollView>
        </LinearGradient>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#64748b', '#475569', '#334155', '#1e293b', '#0f172a']}
        start={{ x: 0, y: 1 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradient}
      >
        {/* Header */}
        <LinearGradient
          colors={['#1e293b', '#334155']}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerLeft}>
              <Image
                source={require('../../assets/images/Germany-Flag.jpg')}
                style={styles.flagImage}
                resizeMode="cover"
              />
              <Text style={styles.headerTitle}>DeutschKorrekt</Text>
            </View>
            <View style={styles.headerRight}>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => setShowProfile(true)}
              >
                <User size={24} color="#f8fafc" />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => setShowSettings(true)}
              >
                <Settings size={24} color="#f8fafc" />
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>

        {/* Messages */}
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContainer}
          showsVerticalScrollIndicator={false}
        />

        {/* Mic Section at Bottom */}
        <LinearGradient
          colors={['#1e293b', '#334155']}
          style={styles.micSection}
        >
          {/* Audio Streaming Button */}
          <TouchableOpacity
            style={[
              styles.micButton,
              (isStreaming || currentMessageId) && styles.micButtonStreaming
            ]}
            onPress={handleButtonPress}
            activeOpacity={0.8}
            disabled={isConnecting || isStreaming || !!currentMessageId}
          >
{(isStreaming || currentMessageId) ? (
              <LinearGradient
                colors={['#ef4444', '#dc2626']}
                style={styles.micButtonGradient}
              >
                <Mic size={24} color="#fff" />
              </LinearGradient>
            ) : isConnecting ? (
              <LinearGradient
                colors={['#6b7280', '#4b5563']}
                style={styles.micButtonGradient}
              >
                <Mic size={24} color="#fff" />
              </LinearGradient>
            ) : (
              <View style={styles.micButtonGradient}>
                <View style={styles.germanFlagContainer}>
                  <View style={[styles.flagBar, { backgroundColor: '#03080c' }]} />
                  <View style={[styles.flagBar, { backgroundColor: '#dd291a' }]} />
                  <View style={[styles.flagBar, { backgroundColor: '#fdb922' }]} />
                </View>
                <View style={styles.micIconContainer}>
                  <Mic size={24} color="#fff" />
                </View>
              </View>
            )}
          </TouchableOpacity>
        </LinearGradient>

        <SettingsModal />
        <ProfileModal />
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  gradient: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 2,
    borderBottomColor: '#475569',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 12,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    marginLeft: 16,
    padding: 8,
  },
  flagImage: {
    width: 32,
    height: 20,
    marginRight: 12,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#64748b',
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#f1f5f9',
  },
  messagesList: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  messagesContainer: {
    padding: 20,
    paddingBottom: 20,
  },
  messageContainer: {
    marginVertical: 6,
  },
  userMessage: {
    alignItems: 'flex-end',
  },
  aiMessage: {
    alignItems: 'flex-start',
  },
  userBubbleContainer: {
    maxWidth: '92%',
  },
  aiBubbleContainer: {
    maxWidth: '92%',
  },
  messageBubble: {
    padding: 16,
    borderRadius: 18,
    marginVertical: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  aiBubble: {
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 4,
    borderWidth: 2,
    borderColor: '#e2e8f0',
    shadowColor: '#64748b',
  },
  messageText: {
    fontSize: 17,
    fontFamily: 'Inter-Regular',
    lineHeight: 24,
    marginBottom: 6,
  },
  userText: {
    color: '#f8fafc',
  },
  aiText: {
    color: '#1e293b',
  },
  boldText: {
    fontFamily: 'Inter-Bold',
    color: '#0f172a',
  },
  streamingIndicator: {
    color: '#ef4444',
    fontSize: 14,
    marginLeft: 4,
  },
  processingIndicator: {
    color: '#f59e0b',
    fontSize: 14,
    marginLeft: 4,
  },
  resultContainer: {
    marginTop: 12,
    maxHeight: 300,
  },
  agentBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#64748b',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  agentBadgeText: {
    color: '#f8fafc',
    fontSize: 13,
    fontFamily: 'Inter-SemiBold',
  },
  germanResultContainer: {
    marginBottom: 12,
  },
  englishResultContainer: {
    marginBottom: 12,
  },
  correctionContainer: {
    marginBottom: 16,
  },
  correctionTitle: {
    fontSize: 15,
    fontFamily: 'Inter-SemiBold',
    color: '#f8fafc',
    marginBottom: 8,
  },
  correctionItem: {
    backgroundColor: 'rgba(254, 241, 242, 0.9)',
    padding: 12,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  originalText: {
    fontSize: 15,
    fontFamily: 'Inter-Regular',
    color: '#dc2626',
    marginBottom: 4,
  },
  correctedText: {
    fontSize: 15,
    fontFamily: 'Inter-SemiBold',
    color: '#059669',
  },
  originalEnglishText: {
    fontSize: 15,
    fontFamily: 'Inter-Regular',
    color: '#f8fafc',
    marginBottom: 10,
    fontStyle: 'italic',
  },
  suggestionsContainer: {
    marginBottom: 16,
  },
  suggestionsTitle: {
    fontSize: 15,
    fontFamily: 'Inter-SemiBold',
    color: '#f8fafc',
    marginBottom: 8,
  },
  suggestionItem: {
    backgroundColor: 'rgba(239, 246, 255, 0.9)',
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#0ea5e9',
    borderWidth: 1,
    borderColor: '#bae6fd',
  },
  suggestionText: {
    fontSize: 15,
    fontFamily: 'Inter-Regular',
    color: '#0c4a6e',
  },
  explanationsContainer: {
    marginBottom: 16,
  },
  explanationsTitle: {
    fontSize: 15,
    fontFamily: 'Inter-SemiBold',
    color: '#f8fafc',
    marginBottom: 8,
  },
  explanationItem: {
    backgroundColor: 'rgba(254, 252, 232, 0.9)',
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#f59e0b',
    borderWidth: 1,
    borderColor: '#fde68a',
  },
  explanationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#92400e',
    lineHeight: 20,
  },
  translationsContainer: {
    marginBottom: 16,
  },
  translationsTitle: {
    fontSize: 15,
    fontFamily: 'Inter-SemiBold',
    color: '#f8fafc',
    marginBottom: 8,
  },
  translationItem: {
    backgroundColor: 'rgba(240, 253, 244, 0.9)',
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#22c55e',
    borderWidth: 1,
    borderColor: '#bbf7d0',
  },
  translationText: {
    fontSize: 15,
    fontFamily: 'Inter-SemiBold',
    color: '#15803d',
    marginBottom: 6,
  },
  micSection: {
    paddingHorizontal: 20,
    paddingTop: 24,
    paddingBottom: 28,
    borderTopWidth: 2,
    borderTopColor: '#475569',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 12,
  },
  voicePrompt: {
    marginBottom: 16,
    paddingHorizontal: 24,
  },
  voicePromptText: {
    fontSize: 15,
    fontFamily: 'Inter-Medium',
    color: '#cbd5e1',
    textAlign: 'center',
    lineHeight: 22,
  },
  micButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
  },
  micButtonStreaming: {
    transform: [{ scale: 1.15 }],
  },
  micButtonGradient: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.2)',
    overflow: 'hidden',
  },
  germanFlagContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    flexDirection: 'column',
  },
  flagBar: {
    flex: 1,
    width: '100%',
  },
  micIconContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 20,
    padding: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#475569',
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#f8fafc',
  },
  modalBody: {
    padding: 20,
  },
  modalText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#cbd5e1',
    lineHeight: 24,
  },
});