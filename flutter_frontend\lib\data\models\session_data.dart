/// Session data model for tracking user interactions
class SessionData {
  final int? sessionId;
  final String email;
  final String message;
  final String response;
  final DateTime datetime;
  final DateTime? createdAt;

  const SessionData({
    this.sessionId,
    required this.email,
    required this.message,
    required this.response,
    required this.datetime,
    this.createdAt,
  });

  /// Create SessionData from JSON
  factory SessionData.fromJson(Map<String, dynamic> json) {
    return SessionData(
      sessionId: json['session_id'] as int?,
      email: json['email'] as String,
      message: json['message'] as String,
      response: json['response'] as String,
      datetime: DateTime.parse(json['datetime'] as String),
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
    );
  }

  /// Convert SessionData to JSON
  Map<String, dynamic> toJson() {
    return {
      if (sessionId != null) 'session_id': sessionId,
      'email': email,
      'message': message,
      'response': response,
      'datetime': datetime.toIso8601String(),
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
    };
  }

  /// Create SessionData for insertion (without sessionId)
  Map<String, dynamic> toInsertJson() {
    return {
      'email': email,
      'message': message,
      'response': response,
      'datetime': datetime.toIso8601String(),
    };
  }

  /// Create a copy of SessionData with updated fields
  SessionData copyWith({
    int? sessionId,
    String? email,
    String? message,
    String? response,
    DateTime? datetime,
    DateTime? createdAt,
  }) {
    return SessionData(
      sessionId: sessionId ?? this.sessionId,
      email: email ?? this.email,
      message: message ?? this.message,
      response: response ?? this.response,
      datetime: datetime ?? this.datetime,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Create a new session data entry
  factory SessionData.create({
    required String email,
    required String message,
    required String response,
    DateTime? datetime,
  }) {
    return SessionData(
      email: email,
      message: message,
      response: response,
      datetime: datetime ?? DateTime.now(),
    );
  }

  /// Get message length
  int get messageLength => message.length;

  /// Get response length
  int get responseLength => response.length;

  /// Check if session is recent (within last hour)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(datetime);
    return difference.inHours < 1;
  }

  /// Get formatted datetime string
  String get formattedDatetime {
    return datetime.toLocal().toString().split('.')[0]; // Remove microseconds
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SessionData &&
        other.sessionId == sessionId &&
        other.email == email &&
        other.message == message &&
        other.response == response &&
        other.datetime == datetime;
  }

  @override
  int get hashCode {
    return Object.hash(
      sessionId,
      email,
      message,
      response,
      datetime,
    );
  }

  @override
  String toString() {
    return 'SessionData(id: $sessionId, email: $email, datetime: $formattedDatetime)';
  }
}