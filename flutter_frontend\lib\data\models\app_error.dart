/// Enumeration of application error types
enum AppErrorType {
  microphonePermissionDenied,
  websocketConnectionFailed,
  websocketMessageParsingFailed,
  audioRecordingFailed,
  transcriptionTimeout,
  backendError,
  serverError,
  networkError,
  unknown,
  unknownError,
}

/// Model for application errors with context and recovery information
class AppError {
  final AppErrorType type;
  final String message;
  final String? details;
  final DateTime timestamp;
  final bool isRecoverable;
  final String? recoveryAction;
  final Exception? originalException;
  
  const AppError({
    required this.type,
    required this.message,
    this.details,
    required this.timestamp,
    this.isRecoverable = true,
    this.recoveryAction,
    this.originalException,
  });
  
  /// Create a microphone permission error
  factory AppError.microphonePermissionDenied({String? details}) {
    return AppError(
      type: AppErrorType.microphonePermissionDenied,
      message: 'Microphone permission is required to use voice features',
      details: details,
      timestamp: DateTime.now(),
      isRecoverable: true,
      recoveryAction: 'Grant microphone permission in settings',
    );
  }
  
  /// Create a WebSocket connection error
  factory AppError.websocketConnectionFailed({String? details, Exception? originalException}) {
    return AppError(
      type: AppErrorType.websocketConnectionFailed,
      message: 'Failed to connect to the server',
      details: details,
      timestamp: DateTime.now(),
      isRecoverable: true,
      recoveryAction: 'Check your internet connection and try again',
      originalException: originalException,
    );
  }
  
  /// Create an audio recording error
  factory AppError.audioRecordingFailed({String? details, Exception? originalException}) {
    return AppError(
      type: AppErrorType.audioRecordingFailed,
      message: 'Failed to record audio',
      details: details,
      timestamp: DateTime.now(),
      isRecoverable: true,
      recoveryAction: 'Check microphone permissions and try again',
      originalException: originalException,
    );
  }
  
  /// Create a transcription timeout error
  factory AppError.transcriptionTimeout({String? details}) {
    return AppError(
      type: AppErrorType.transcriptionTimeout,
      message: 'Transcription request timed out',
      details: details,
      timestamp: DateTime.now(),
      isRecoverable: true,
      recoveryAction: 'Try speaking again',
    );
  }
  
  /// Create a backend error
  factory AppError.backendError({String? details, Exception? originalException}) {
    return AppError(
      type: AppErrorType.backendError,
      message: 'Server error occurred',
      details: details,
      timestamp: DateTime.now(),
      isRecoverable: true,
      recoveryAction: 'Please try again later',
      originalException: originalException,
    );
  }
  
  /// Create a network error
  factory AppError.networkError({String? details, Exception? originalException}) {
    return AppError(
      type: AppErrorType.networkError,
      message: 'Network connection error',
      details: details,
      timestamp: DateTime.now(),
      isRecoverable: true,
      recoveryAction: 'Check your internet connection',
      originalException: originalException,
    );
  }
  
  /// Create an unknown error
  factory AppError.unknown({String? details, Exception? originalException}) {
    return AppError(
      type: AppErrorType.unknownError,
      message: 'An unexpected error occurred',
      details: details,
      timestamp: DateTime.now(),
      isRecoverable: true,
      recoveryAction: 'Please try again',
      originalException: originalException,
    );
  }



  /// Create a WebSocket message parsing error
  factory AppError.websocketMessageParsingFailed({String? details, Exception? originalException}) {
    return AppError(
      type: AppErrorType.websocketMessageParsingFailed,
      message: 'Failed to parse server message',
      details: details,
      timestamp: DateTime.now(),
      isRecoverable: true,
      recoveryAction: 'Try again',
      originalException: originalException,
    );
  }

  /// Create a server error
  factory AppError.serverError({String? details, Exception? originalException}) {
    return AppError(
      type: AppErrorType.serverError,
      message: 'Server error occurred',
      details: details,
      timestamp: DateTime.now(),
      isRecoverable: true,
      recoveryAction: 'Try again later',
      originalException: originalException,
    );
  }
  
  /// Get user-friendly error message
  String get userMessage {
    switch (type) {
      case AppErrorType.microphonePermissionDenied:
        return 'Please allow microphone access to use voice features';
      case AppErrorType.websocketConnectionFailed:
        return 'Unable to connect to server. Please check your internet connection.';
      case AppErrorType.websocketMessageParsingFailed:
        return 'Failed to parse server message. Please try again.';
      case AppErrorType.audioRecordingFailed:
        return 'Unable to record audio. Please check your microphone.';
      case AppErrorType.transcriptionTimeout:
        return 'Request timed out. Please try again.';
      case AppErrorType.backendError:
        return 'Server error. Please try again later.';
      case AppErrorType.serverError:
        return 'Server error occurred. Please try again later.';
      case AppErrorType.networkError:
        return 'Network error. Please check your connection.';
      case AppErrorType.unknown:
        return 'Something went wrong. Please try again.';
      case AppErrorType.unknownError:
        return 'Something went wrong. Please try again.';
    }
  }
  
  @override
  String toString() {
    return 'AppError(type: $type, message: $message, details: $details)';
  }
}