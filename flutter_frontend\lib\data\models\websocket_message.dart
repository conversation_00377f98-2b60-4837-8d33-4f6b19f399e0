import '../../core/utils/websocket_utils.dart';

/// WebSocket message model for backend communication
class WebSocketMessage {
  final WebSocketMessageType messageType;
  final Map<String, dynamic> data;
  
  const WebSocketMessage({
    required this.messageType,
    required this.data,
  });
  
  /// Create from JSON string
  factory WebSocketMessage.fromJson(Map<String, dynamic> json) {
    final type = WebSocketUtils.parseMessageType(json['type'] ?? 'info');
    return WebSocketMessage(
      messageType: type,
      data: json,
    );
  }
  
  /// Get text content from message
  String get text => data['text'] ?? '';
  
  /// Get transcript content
  String get transcript => data['transcript'] ?? '';
  
  /// Get error message
  String get error => data['error'] ?? '';
  
  /// Check if message is partial transcript
  bool get isPartialTranscript => messageType == WebSocketMessageType.partialTranscript;
  
  /// Check if message is final transcript
  bool get isFinalTranscript => messageType == WebSocketMessageType.finalTranscript;
  
  /// Check if message is processing
  bool get isProcessing => messageType == WebSocketMessageType.processing;
  
  /// Check if message is Groq response
  bool get isGroqResponse => messageType == WebSocketMessageType.groqResponse;
  
  /// Check if message is error
  bool get isError => messageType == WebSocketMessageType.error;
  
  @override
  String toString() {
    return 'WebSocketMessage(type: $messageType, data: $data)';
  }
}