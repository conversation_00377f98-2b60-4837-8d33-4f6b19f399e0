import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../utils/error_handler.dart';
import 'analytics_service.dart';

/// Monitoring service for app performance and health metrics
class MonitoringService {
  static final MonitoringService _instance = MonitoringService._internal();
  factory MonitoringService() => _instance;
  MonitoringService._internal();

  final AnalyticsService _analytics = AnalyticsService();
  final Map<String, PerformanceMetric> _metrics = {};
  final List<HealthCheck> _healthChecks = [];
  Timer? _monitoringTimer;
  bool _isInitialized = false;

  /// Initialize monitoring service
  Future<void> initialize() async {
    try {
      // Set up default health checks
      _setupDefaultHealthChecks();
      
      // Start periodic monitoring
      _startPeriodicMonitoring();
      
      _isInitialized = true;
      
      _analytics.trackEvent('monitoring_initialized');
    } catch (e) {
      ErrorHandler.logError('Monitoring initialization', e);
    }
  }

  /// Record a performance metric
  void recordMetric(String name, double value, {
    String? unit,
    Map<String, dynamic>? tags,
  }) {
    try {
      final metric = PerformanceMetric(
        name: name,
        value: value,
        unit: unit ?? 'count',
        tags: tags ?? {},
        timestamp: DateTime.now(),
      );

      _metrics[name] = metric;
      
      // Track in analytics
      _analytics.trackPerformance(name, value, unit: unit, properties: tags);
      
      // Check for performance issues
      _checkPerformanceThresholds(metric);
    } catch (e) {
      ErrorHandler.logError('Record metric', e);
    }
  }

  /// Record response time
  void recordResponseTime(String operation, Duration duration, {
    bool success = true,
    Map<String, dynamic>? tags,
  }) {
    recordMetric(
      '${operation}_response_time',
      duration.inMilliseconds.toDouble(),
      unit: 'milliseconds',
      tags: {
        'operation': operation,
        'success': success,
        ...?tags,
      },
    );
  }

  /// Record error rate
  void recordError(String operation, String error, {
    Map<String, dynamic>? tags,
  }) {
    recordMetric(
      '${operation}_error_rate',
      1.0,
      unit: 'count',
      tags: {
        'operation': operation,
        'error': error,
        ...?tags,
      },
    );
    
    _analytics.trackError(error, context: operation, properties: tags);
  }

  /// Record memory usage
  Future<void> recordMemoryUsage() async {
    try {
      // This would require platform-specific implementation
      // For now, we'll use a placeholder
      final memoryUsage = await _getMemoryUsage();
      
      recordMetric(
        'memory_usage',
        memoryUsage,
        unit: 'bytes',
      );
    } catch (e) {
      ErrorHandler.logError('Record memory usage', e);
    }
  }

  /// Record network metrics
  void recordNetworkMetric(String endpoint, {
    required Duration responseTime,
    required int statusCode,
    required int bytesReceived,
    required int bytesSent,
  }) {
    final success = statusCode >= 200 && statusCode < 300;
    
    recordMetric(
      'network_response_time',
      responseTime.inMilliseconds.toDouble(),
      unit: 'milliseconds',
      tags: {
        'endpoint': endpoint,
        'status_code': statusCode,
        'success': success,
      },
    );
    
    recordMetric(
      'network_bytes_received',
      bytesReceived.toDouble(),
      unit: 'bytes',
      tags: {'endpoint': endpoint},
    );
    
    recordMetric(
      'network_bytes_sent',
      bytesSent.toDouble(),
      unit: 'bytes',
      tags: {'endpoint': endpoint},
    );
  }

  /// Add a health check
  void addHealthCheck(HealthCheck healthCheck) {
    _healthChecks.add(healthCheck);
  }

  /// Run all health checks
  Future<HealthReport> runHealthChecks() async {
    final results = <HealthCheckResult>[];
    
    for (final check in _healthChecks) {
      try {
        final result = await check.run();
        results.add(result);
      } catch (e) {
        results.add(HealthCheckResult(
          name: check.name,
          status: HealthStatus.unhealthy,
          message: 'Health check failed: $e',
          duration: Duration.zero,
        ));
      }
    }
    
    final overallStatus = results.any((r) => r.status == HealthStatus.unhealthy)
        ? HealthStatus.unhealthy
        : results.any((r) => r.status == HealthStatus.degraded)
            ? HealthStatus.degraded
            : HealthStatus.healthy;
    
    final report = HealthReport(
      status: overallStatus,
      checks: results,
      timestamp: DateTime.now(),
    );
    
    // Track health status
    _analytics.trackEvent('health_check_completed', {
      'status': overallStatus.name,
      'checks_count': results.length,
      'unhealthy_count': results.where((r) => r.status == HealthStatus.unhealthy).length,
    });
    
    return report;
  }

  /// Get current metrics
  Map<String, PerformanceMetric> getMetrics() {
    return Map.unmodifiable(_metrics);
  }

  /// Get metrics summary
  Map<String, dynamic> getMetricsSummary() {
    final summary = <String, dynamic>{};
    
    for (final metric in _metrics.values) {
      summary[metric.name] = {
        'value': metric.value,
        'unit': metric.unit,
        'timestamp': metric.timestamp.toIso8601String(),
        'tags': metric.tags,
      };
    }
    
    return summary;
  }

  /// Clear old metrics
  void clearOldMetrics({Duration maxAge = const Duration(hours: 1)}) {
    final cutoff = DateTime.now().subtract(maxAge);
    _metrics.removeWhere((key, metric) => metric.timestamp.isBefore(cutoff));
  }

  /// Set up default health checks
  void _setupDefaultHealthChecks() {
    // Database connectivity check
    addHealthCheck(HealthCheck(
      name: 'database_connectivity',
      check: () async {
        try {
          // This would check Supabase connectivity
          await Future.delayed(const Duration(milliseconds: 100));
          return HealthCheckResult(
            name: 'database_connectivity',
            status: HealthStatus.healthy,
            message: 'Database connection is healthy',
            duration: const Duration(milliseconds: 100),
          );
        } catch (e) {
          return HealthCheckResult(
            name: 'database_connectivity',
            status: HealthStatus.unhealthy,
            message: 'Database connection failed: $e',
            duration: const Duration(milliseconds: 100),
          );
        }
      },
    ));

    // Memory usage check
    addHealthCheck(HealthCheck(
      name: 'memory_usage',
      check: () async {
        try {
          final memoryUsage = await _getMemoryUsage();
          final status = memoryUsage > 500 * 1024 * 1024 // 500MB
              ? HealthStatus.degraded
              : HealthStatus.healthy;
          
          return HealthCheckResult(
            name: 'memory_usage',
            status: status,
            message: 'Memory usage: ${(memoryUsage / 1024 / 1024).toStringAsFixed(1)}MB',
            duration: const Duration(milliseconds: 50),
          );
        } catch (e) {
          return HealthCheckResult(
            name: 'memory_usage',
            status: HealthStatus.unhealthy,
            message: 'Failed to check memory usage: $e',
            duration: const Duration(milliseconds: 50),
          );
        }
      },
    ));
  }

  /// Start periodic monitoring
  void _startPeriodicMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      try {
        // Record system metrics
        await recordMemoryUsage();
        
        // Run health checks
        await runHealthChecks();
        
        // Clear old metrics
        clearOldMetrics();
      } catch (e) {
        ErrorHandler.logError('Periodic monitoring', e);
      }
    });
  }

  /// Check performance thresholds
  void _checkPerformanceThresholds(PerformanceMetric metric) {
    // Define performance thresholds
    const thresholds = {
      'response_time': 5000.0, // 5 seconds
      'memory_usage': 500 * 1024 * 1024.0, // 500MB
      'error_rate': 0.1, // 10%
    };
    
    for (final entry in thresholds.entries) {
      if (metric.name.contains(entry.key) && metric.value > entry.value) {
        _analytics.trackEvent('performance_threshold_exceeded', {
          'metric': metric.name,
          'value': metric.value,
          'threshold': entry.value,
          'unit': metric.unit,
        });
      }
    }
  }

  /// Get memory usage (platform-specific implementation needed)
  Future<double> _getMemoryUsage() async {
    try {
      // This is a placeholder - real implementation would use platform channels
      return 100 * 1024 * 1024.0; // 100MB placeholder
    } catch (e) {
      return 0.0;
    }
  }

  /// Dispose monitoring service
  void dispose() {
    _monitoringTimer?.cancel();
    _metrics.clear();
    _healthChecks.clear();
    _isInitialized = false;
  }
}

/// Performance metric model
class PerformanceMetric {
  final String name;
  final double value;
  final String unit;
  final Map<String, dynamic> tags;
  final DateTime timestamp;

  const PerformanceMetric({
    required this.name,
    required this.value,
    required this.unit,
    required this.tags,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'value': value,
      'unit': unit,
      'tags': tags,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// Health check model
class HealthCheck {
  final String name;
  final Future<HealthCheckResult> Function() check;

  const HealthCheck({
    required this.name,
    required this.check,
  });

  Future<HealthCheckResult> run() async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await check();
      stopwatch.stop();
      return result.copyWith(duration: stopwatch.elapsed);
    } catch (e) {
      stopwatch.stop();
      return HealthCheckResult(
        name: name,
        status: HealthStatus.unhealthy,
        message: 'Health check failed: $e',
        duration: stopwatch.elapsed,
      );
    }
  }
}

/// Health check result model
class HealthCheckResult {
  final String name;
  final HealthStatus status;
  final String message;
  final Duration duration;

  const HealthCheckResult({
    required this.name,
    required this.status,
    required this.message,
    required this.duration,
  });

  HealthCheckResult copyWith({
    String? name,
    HealthStatus? status,
    String? message,
    Duration? duration,
  }) {
    return HealthCheckResult(
      name: name ?? this.name,
      status: status ?? this.status,
      message: message ?? this.message,
      duration: duration ?? this.duration,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'status': status.name,
      'message': message,
      'duration_ms': duration.inMilliseconds,
    };
  }
}

/// Health report model
class HealthReport {
  final HealthStatus status;
  final List<HealthCheckResult> checks;
  final DateTime timestamp;

  const HealthReport({
    required this.status,
    required this.checks,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'status': status.name,
      'checks': checks.map((c) => c.toJson()).toList(),
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// Health status enum
enum HealthStatus {
  healthy,
  degraded,
  unhealthy,
}