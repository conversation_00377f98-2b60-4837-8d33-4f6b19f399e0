#!/usr/bin/env python3
"""
Simple test to verify TTS test structure works.
"""

import sys
import os

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required modules can be imported."""
    try:
        # Test basic imports
        import pytest
        print("✅ pytest imported successfully")
        
        import google.cloud.texttospeech
        print("✅ google.cloud.texttospeech imported successfully")
        
        import fastapi
        print("✅ fastapi imported successfully")
        
        # Test our test modules can be imported
        from tests.conftest import mock_settings, sample_tts_texts
        print("✅ test fixtures imported successfully")
        
        from tests.test_google_tts_client import TestGoogleTTSClient
        print("✅ GoogleTTSClient tests imported successfully")
        
        from tests.test_tts_service import TestTTSService
        print("✅ TTSService tests imported successfully")
        
        from tests.test_tts_api import TestTTSAPI
        print("✅ TTS API tests imported successfully")
        
        print("\n🎉 All imports successful! Test structure is working.")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality of test fixtures."""
    try:
        # Test that we can import the test classes
        from tests.test_google_tts_client import TestGoogleTTSClient
        from tests.test_tts_service import TestTTSService
        from tests.test_tts_api import TestTTSAPI
        
        print("✅ Test classes imported successfully")
        
        # Test that we can create mock objects
        from unittest.mock import Mock
        mock_client = Mock()
        assert mock_client is not None
        print("✅ Mock objects working")
        
        print("\n🎉 Basic functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Running simple TTS test verification...\n")
    
    success = True
    
    # Test imports
    if not test_imports():
        success = False
    
    print()
    
    # Test basic functionality
    if not test_basic_functionality():
        success = False
    
    if success:
        print("\n✅ All simple tests passed! The TTS test suite is ready to run.")
        print("\nTo run the full test suite:")
        print("  python run_tests.py --type unit -v")
        print("  python run_tests.py --type api -v")
        print("  python run_tests.py --type all -v")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())