"""
Main FastAPI application for Deutschkorrekt backend.
Provides speech-to-text and language processing capabilities.
"""

import logging
import time
from fastapi import FastAP<PERSON>, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from config.settings import settings
from config.logging_config import configure_logging, get_logger, set_correlation_id

# Configure logging
correlation_filter = configure_logging(settings.log_level)
logger = get_logger(__name__)

# Create FastAPI application
app = FastAPI(
    title="Deutschkorrekt Backend",
    description="Speech-to-text and German language correction API",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add middleware for request logging and correlation ID tracking
@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    """
    Middleware for logging requests and setting correlation IDs.
    """
    # Generate a correlation ID for this request
    correlation_id = request.headers.get("X-Correlation-ID") or set_correlation_id()
    
    # Log the request
    logger.info(
        f"Request started: {request.method} {request.url.path}",
        extra={
            "correlation_id": correlation_id,
            "method": request.method,
            "path": request.url.path,
            "query_params": str(request.query_params),
            "client_host": request.client.host if request.client else None,
        }
    )
    
    # Track request timing
    start_time = time.time()
    
    try:
        # Process the request
        response = await call_next(request)
        
        # Calculate request duration
        duration = time.time() - start_time
        
        # Record the request in monitoring service
        from services.monitoring_service import monitoring_service
        monitoring_service.record_request(
            success=(response.status_code < 400),
            latency=duration
        )
        
        # Log the response
        logger.info(
            f"Request completed: {request.method} {request.url.path} - Status: {response.status_code}",
            extra={
                "correlation_id": correlation_id,
                "status_code": response.status_code,
                "duration": duration,
            }
        )
        
        # Add correlation ID to response headers
        response.headers["X-Correlation-ID"] = correlation_id
        
        return response
        
    except Exception as e:
        # Calculate request duration
        duration = time.time() - start_time
        
        # Record the failed request in monitoring service
        from services.monitoring_service import monitoring_service
        monitoring_service.record_request(
            success=False,
            latency=duration
        )
        
        # Log the error
        logger.error(
            f"Request failed: {request.method} {request.url.path} - Error: {str(e)}",
            extra={
                "correlation_id": correlation_id,
                "duration": duration,
            },
            exc_info=True
        )
        
        # Re-raise the exception to be handled by exception handlers
        raise

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint for Google Cloud Run."""
    return {"status": "healthy", "service": settings.app_name}

# Metrics endpoint
@app.get("/metrics")
async def metrics():
    """Metrics endpoint for monitoring."""
    from services.monitoring_service import monitoring_service
    return monitoring_service.get_metrics()

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions."""
    logger.error(f"HTTP error: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail},
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error"},
    )

# Import and include routes
from websocket.stt_endpoint import router as stt_router
from health import router as health_router

app.include_router(stt_router)
app.include_router(health_router)

# TTS API endpoints
from fastapi import Response
from models.data_models import TTSRequest, TTSResponse, TTSErrorResponse, TTSHealthResponse
from services.tts_service import tts_service

@app.post("/api/tts", response_model=TTSResponse)
async def generate_speech(request: TTSRequest, http_request: Request) -> Response:
    """
    Generate speech audio from text using Google Cloud Text-to-Speech.
    
    This endpoint provides enterprise-grade TTS functionality with:
    - Text validation and sanitization
    - German voice (de-DE-Chirp3-HD-Aoede) optimized for mobile
    - MP3 audio output at 24kHz
    - Comprehensive error handling and logging
    - Rate limiting and monitoring
    
    Args:
        request (TTSRequest): TTS request with text and configuration
        http_request (Request): FastAPI request object for correlation ID
        
    Returns:
        Response: Audio data with appropriate headers or error response
    """
    # Get correlation ID from request headers
    correlation_id = http_request.headers.get("X-Correlation-ID", "unknown")
    
    try:
        logger.info(f"🎤 TTS request received: message_id={request.message_id}, text_length={len(request.text)}")
        
        # Generate speech using TTS service
        result = await tts_service.generate_speech(
            text=request.text,
            request_id=f"{correlation_id}:{request.message_id}"
        )
        
        if result["success"]:
            # Return audio data as binary response with appropriate headers
            return Response(
                content=result["audio_data"],
                media_type=result["content_type"],
                headers={
                    "X-Duration-Seconds": str(result["duration_seconds"]),
                    "X-Processing-Time": str(result["processing_time"]),
                    "X-Original-Text-Length": str(len(request.text)),
                    "X-Sanitized-Text-Length": str(len(result["sanitized_text"])),
                    "Content-Disposition": f"attachment; filename=\"tts_{request.message_id}.mp3\"",
                    "Cache-Control": "private, max-age=3600"  # Cache for 1 hour
                }
            )
        else:
            # Return error response
            error_response = TTSErrorResponse(
                error_message=result["error_message"],
                error_code=result["error_code"],
                processing_time=result["processing_time"],
                retry_after=60 if result["error_code"] == "QUOTA_EXCEEDED" else None
            )
            
            # Map error codes to HTTP status codes
            status_code_map = {
                "INVALID_INPUT": 400,
                "QUOTA_EXCEEDED": 429,
                "PERMISSION_DENIED": 403,
                "SERVICE_UNAVAILABLE": 503,
                "INTERNAL_ERROR": 500
            }
            
            status_code = status_code_map.get(result["error_code"], 500)
            
            return JSONResponse(
                status_code=status_code,
                content=error_response.dict()
            )
            
    except Exception as e:
        logger.error(f"❌ Unexpected error in TTS endpoint: {str(e)}", exc_info=True)
        
        error_response = TTSErrorResponse(
            error_message=f"Internal server error: {str(e)}",
            error_code="INTERNAL_ERROR",
            processing_time=0.0
        )
        
        return JSONResponse(
            status_code=500,
            content=error_response.dict()
        )

@app.get("/api/tts/health", response_model=TTSHealthResponse)
async def tts_health_check():
    """
    Health check endpoint for TTS service.
    
    Performs a test TTS generation to verify service functionality.
    
    Returns:
        TTSHealthResponse: Health status with performance metrics
    """
    try:
        logger.info("🏥 TTS health check requested")
        
        # Perform health check using TTS service
        health_result = await tts_service.health_check()
        
        if health_result["healthy"]:
            return TTSHealthResponse(
                healthy=True,
                response_time=health_result["response_time"],
                message=health_result["message"],
                audio_size=health_result.get("audio_size")
            )
        else:
            return TTSHealthResponse(
                healthy=False,
                response_time=health_result["response_time"],
                error=health_result.get("error"),
                error_code=health_result.get("error_code")
            )
            
    except Exception as e:
        logger.error(f"❌ Error in TTS health check endpoint: {str(e)}", exc_info=True)
        
        return TTSHealthResponse(
            healthy=False,
            response_time=0.0,
            error=f"Health check failed: {str(e)}"
        )

@app.get("/api/tts/info")
async def tts_service_info():
    """
    Get information about the TTS service configuration.
    
    Returns:
        Dict: Service configuration and status information
    """
    try:
        return tts_service.get_service_info()
    except Exception as e:
        logger.error(f"❌ Error getting TTS service info: {str(e)}", exc_info=True)
        return {
            "error": f"Failed to get service info: {str(e)}"
        }

# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Initialize services on startup."""
    try:
        logger.info(f"Application starting: {settings.app_name}")

        # Import services
        from services.monitoring_service import monitoring_service
        from services.session_service import session_service

        # Start the monitoring service
        await monitoring_service.start()
        logger.info("Monitoring service started")

        # Start the session service cleanup task
        await session_service.start()
        logger.info("Session service started")

        logger.info(f"Application started successfully: {settings.app_name}")

    except Exception as e:
        logger.error(f"Error during startup: {str(e)}", exc_info=True)
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown."""
    try:
        logger.info("Application shutting down...")

        from services.monitoring_service import monitoring_service
        from services.session_service import session_service

        # Stop the monitoring service
        await monitoring_service.stop()
        logger.info("Monitoring service stopped")

        # Stop the session service cleanup task
        await session_service.stop()
        logger.info("Session service stopped")

        logger.info(f"Application shutdown complete: {settings.app_name}")

    except Exception as e:
        logger.error(f"Error during shutdown: {str(e)}", exc_info=True)

if __name__ == "__main__":
    # For local development
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)