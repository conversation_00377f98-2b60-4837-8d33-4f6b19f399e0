# Requirements Document

## Introduction

Deutschkorrekt is a German grammar correction application that processes speech audio to provide real-time German language corrections and suggestions. The backend system provides speech-to-text streaming capabilities via Deepgram and intelligent language processing through Google ADK's multi-agent orchestration framework. The system automatically detects whether input is in German or English, providing appropriate corrections or translations to help users improve their German conversation skills.

## Requirements

### Requirement 1

**User Story:** As a mobile app user, I want to stream audio to the backend and receive real-time speech-to-text transcription, so that I can see my spoken words converted to text immediately.

#### Acceptance Criteria

1. WHEN the frontend sends audio data to the /stt endpoint THEN the system SHALL stream the transcribed text back in real-time using AssemblyAI
2. WHEN audio streaming begins THEN the system SHALL enforce a maximum recording duration of 20 seconds
3. WHEN the 20-second limit is reached OR the user stops recording THEN the system SHALL complete the transcription and store the final text temporarily
4. IF the AssemblyAI API key is missing or invalid THEN the system SHALL return an appropriate error response
5. WHEN streaming is active THEN the system SHALL maintain the WebSocket connection for real-time text delivery

### Requirement 2

**User Story:** As a system administrator, I want the backend to be containerized and deployable to Google Cloud Run, so that it can scale automatically and be managed in the cloud.

#### Acceptance Criteria

1. WHEN the application is built THEN the system SHALL include a Dockerfile optimized for Google Cloud Run
2. WHEN deployed to Google Cloud Run THEN the system SHALL retrieve the DEEPGRAM_API_KEY from Google Secret Manager
3. WHEN the container starts THEN the system SHALL be accessible via HTTP/HTTPS endpoints
4. WHEN scaling occurs THEN the system SHALL handle multiple concurrent audio streams without interference

### Requirement 3

**User Story:** As a language learner, I want the system to automatically detect whether my speech is in German or English, so that I receive appropriate language processing without manual selection.

#### Acceptance Criteria

1. WHEN the complete transcribed text is received THEN the gateway_agent SHALL analyze the text to determine if it is German or English
2. IF the text is determined to be German THEN the system SHALL route it to the german_agent
3. IF the text is determined to be English THEN the system SHALL route it to the english_agent
4. WHEN language detection is uncertain THEN the system SHALL default to German processing
5. WHEN routing decisions are made THEN the system SHALL use Google ADK's multi-agent orchestration framework

### Requirement 4

**User Story:** As a German language learner, I want to receive corrections and suggestions for my German speech, so that I can improve my grammar and use more idiomatic expressions.

#### Acceptance Criteria

1. WHEN the german_agent receives German text THEN it SHALL analyze the text for grammatical errors and non-idiomatic expressions
2. WHEN corrections are needed THEN the system SHALL provide the corrected German text with explanations in English
3. WHEN suggestions are made THEN the system SHALL offer alternative phrasings that sound more natural in German conversation
4. WHEN processing is complete THEN the system SHALL return the original text, corrected text, and explanations to the frontend
5. WHEN using the LLM THEN the system SHALL use the "gemini-2.5-flash-lite-preview-06-17" model

### Requirement 5

**User Story:** As an English speaker learning German, I want to speak in English and receive German translations with multiple natural alternatives, so that I can learn how to express the same idea in different ways in German.

#### Acceptance Criteria

1. WHEN the english_agent receives English text THEN it SHALL translate the text into German
2. WHEN providing translations THEN the system SHALL offer a maximum of 3 alternative German translations
3. WHEN creating alternatives THEN each translation SHALL represent natural and idiomatic German conversation
4. WHEN translations are complete THEN the system SHALL return all translation options to the frontend
5. WHEN using the LLM THEN the system SHALL use the "gemini-2.5-flash-lite-preview-06-17" model

### Requirement 6

**User Story:** As a developer, I want the system to handle errors gracefully and provide meaningful feedback, so that issues can be diagnosed and resolved quickly.

#### Acceptance Criteria

1. WHEN API calls to AssemblyAI fail THEN the system SHALL return appropriate error messages to the frontend
2. WHEN Google ADK agent processing fails THEN the system SHALL log the error and return a fallback response
3. WHEN the 20-second timeout is reached THEN the system SHALL gracefully close the audio stream and process available text
4. WHEN invalid audio data is received THEN the system SHALL return a clear error message
5. WHEN system resources are exhausted THEN the system SHALL return HTTP 503 Service Unavailable

### Requirement 7

**User Story:** As a system operator, I want comprehensive logging and monitoring capabilities, so that I can track system performance and troubleshoot issues effectively.

#### Acceptance Criteria

1. WHEN audio streams are initiated THEN the system SHALL log the session start with timestamp and duration
2. WHEN agent processing occurs THEN the system SHALL log which agent was selected and processing time
3. WHEN errors occur THEN the system SHALL log detailed error information including stack traces
4. WHEN API calls are made THEN the system SHALL log request/response metadata without sensitive data
5. WHEN the application starts THEN the system SHALL log successful initialization of all components