import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Comprehensive error handler for the DeutschKorrekt app
class ErrorHandler {
  /// Handle Supabase authentication errors
  static String handleAuthError(dynamic error) {
    if (error is AuthException) {
      switch (error.message.toLowerCase()) {
        case 'invalid login credentials':
          return 'Invalid email or password. Please check your credentials and try again.';
        case 'email not confirmed':
          return 'Please check your email and click the confirmation link before signing in.';
        case 'user already registered':
          return 'An account with this email already exists. Please sign in instead.';
        case 'password should be at least 6 characters':
          return 'Password must be at least 8 characters long.';
        case 'invalid email':
          return 'Please enter a valid email address.';
        case 'signup disabled':
          return 'New account registration is currently disabled. Please try again later.';
        case 'email rate limit exceeded':
          return 'Too many email attempts. Please wait a few minutes before trying again.';
        default:
          return 'Authentication failed: ${error.message}';
      }
    }
    
    return _handleGenericError(error);
  }

  /// Handle Supabase database errors
  static String handleDatabaseError(dynamic error) {
    if (error is PostgrestException) {
      switch (error.code) {
        case '23505': // Unique constraint violation
          return 'This record already exists. Please try with different information.';
        case '23503': // Foreign key constraint violation
          return 'Referenced data not found. Please refresh and try again.';
        case '42501': // Insufficient privilege
          return 'You don\'t have permission to perform this action.';
        case '08006': // Connection failure
          return 'Database connection failed. Please check your internet connection.';
        case '57014': // Query timeout
          return 'Operation timed out. Please try again.';
        default:
          return 'Database error: ${error.message}';
      }
    }
    
    return _handleGenericError(error);
  }

  /// Handle network errors
  static String handleNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('socketexception') || 
        errorString.contains('network') ||
        errorString.contains('connection')) {
      return 'Network connection failed. Please check your internet connection and try again.';
    }
    
    if (errorString.contains('timeout')) {
      return 'Request timed out. Please check your connection and try again.';
    }
    
    if (errorString.contains('certificate') || errorString.contains('ssl')) {
      return 'Secure connection failed. Please try again.';
    }
    
    return _handleGenericError(error);
  }

  /// Handle credit-related errors
  static String handleCreditError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('insufficient') || errorString.contains('no credits')) {
      return 'You don\'t have enough credits. Credits refresh monthly on your plan date.';
    }
    
    if (errorString.contains('quota') || errorString.contains('limit')) {
      return 'Usage limit reached. Please wait for your credits to refresh or upgrade your plan.';
    }
    
    return _handleGenericError(error);
  }

  /// Handle session tracking errors
  static String handleSessionError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('session') && errorString.contains('log')) {
      return 'Failed to save session data. Your request was processed but not recorded.';
    }
    
    return _handleGenericError(error);
  }

  /// Handle generic errors
  static String _handleGenericError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('format')) {
      return 'Invalid data format. Please check your input and try again.';
    }
    
    if (errorString.contains('permission') || errorString.contains('unauthorized')) {
      return 'You don\'t have permission to perform this action. Please sign in again.';
    }
    
    if (errorString.contains('server') || errorString.contains('500')) {
      return 'Server error occurred. Please try again in a few moments.';
    }
    
    if (errorString.contains('rate limit')) {
      return 'Too many requests. Please wait a moment before trying again.';
    }
    
    // Return a generic user-friendly message
    return 'An unexpected error occurred. Please try again.';
  }

  /// Show error snackbar
  static void showErrorSnackBar(BuildContext context, String message, {Duration? duration}) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  color: Colors.white,
                  fontFamily: 'Inter',
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        duration: duration ?? const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Show success snackbar
  static void showSuccessSnackBar(BuildContext context, String message, {Duration? duration}) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  color: Colors.white,
                  fontFamily: 'Inter',
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: duration ?? const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Show warning snackbar
  static void showWarningSnackBar(BuildContext context, String message, {Duration? duration}) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.warning_outlined, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  color: Colors.white,
                  fontFamily: 'Inter',
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: duration ?? const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Show info snackbar
  static void showInfoSnackBar(BuildContext context, String message, {Duration? duration}) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info_outline, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  color: Colors.white,
                  fontFamily: 'Inter',
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: duration ?? const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Show error dialog
  static Future<void> showErrorDialog(
    BuildContext context, {
    required String title,
    required String message,
    String? actionText,
    VoidCallback? onAction,
  }) async {
    if (!context.mounted) return;
    
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.red),
              const SizedBox(width: 12),
              Text(title),
            ],
          ),
          content: Text(message),
          actions: [
            if (actionText != null && onAction != null)
              TextButton(
                onPressed: onAction,
                child: Text(actionText),
              ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Show retry dialog
  static Future<bool> showRetryDialog(
    BuildContext context, {
    required String title,
    required String message,
  }) async {
    if (!context.mounted) return false;
    
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.refresh, color: Colors.orange),
              const SizedBox(width: 12),
              Text(title),
            ],
          ),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Retry'),
            ),
          ],
        );
      },
    );
    
    return result ?? false;
  }

  /// Log error for debugging
  static void logError(String operation, dynamic error, [StackTrace? stackTrace]) {
    print('❌ Error in $operation: $error');
    if (stackTrace != null) {
      print('Stack trace: $stackTrace');
    }
  }

  /// Check if error is recoverable
  static bool isRecoverableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // Network errors are usually recoverable
    if (errorString.contains('network') || 
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      return true;
    }
    
    // Rate limit errors are recoverable after waiting
    if (errorString.contains('rate limit')) {
      return true;
    }
    
    // Server errors might be temporary
    if (errorString.contains('server') || errorString.contains('500')) {
      return true;
    }
    
    return false;
  }

  /// Get retry delay based on error type
  static Duration getRetryDelay(dynamic error, int attemptCount) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('rate limit')) {
      // Exponential backoff for rate limits
      return Duration(seconds: (attemptCount * attemptCount) * 5);
    }
    
    if (errorString.contains('network') || errorString.contains('timeout')) {
      // Linear backoff for network issues
      return Duration(seconds: attemptCount * 2);
    }
    
    // Default exponential backoff
    return Duration(seconds: attemptCount * attemptCount);
  }
}