import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';

/// Switch setting item with consistent styling
class SettingSwitch extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final bool value;
  final ValueChanged<bool> onChanged;
  final bool enabled;
  final Color? iconColor;
  final Color? activeColor;
  final SwitchStyle style;
  final bool showBorder;
  
  const SettingSwitch({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    required this.value,
    required this.onChanged,
    this.enabled = true,
    this.iconColor,
    this.activeColor,
    this.style = SwitchStyle.material,
    this.showBorder = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: showBorder
            ? const Border(
                bottom: BorderSide(
                  color: AppColors.slate600,
                  width: 0.5,
                ),
              )
            : null,
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: enabled 
                ? (iconColor ?? AppColors.lightText.withOpacity(0.7))
                : AppColors.lightText.withOpacity(0.3),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.messageText.copyWith(
                    fontWeight: FontWeight.w500,
                    color: enabled 
                        ? AppColors.lightText 
                        : AppColors.lightText.withOpacity(0.5),
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    subtitle!,
                    style: AppTextStyles.captionText.copyWith(
                      color: enabled 
                          ? AppColors.lightText.withOpacity(0.7)
                          : AppColors.lightText.withOpacity(0.3),
                    ),
                  ),
                ],
              ],
            ),
          ),
          _buildSwitch(),
        ],
      ),
    );
  }
  
  Widget _buildSwitch() {
    switch (style) {
      case SwitchStyle.material:
        return Switch(
          value: value,
          onChanged: enabled ? onChanged : null,
          activeColor: activeColor ?? AppColors.infoBlue,
          inactiveThumbColor: AppColors.lightText.withOpacity(0.5),
          inactiveTrackColor: AppColors.slate600.withOpacity(0.5),
        );
      case SwitchStyle.cupertino:
        return CupertinoSwitch(
          value: value,
          onChanged: enabled ? onChanged : null,
          activeColor: activeColor ?? AppColors.infoBlue,
        );
      case SwitchStyle.custom:
        return _buildCustomSwitch();
    }
  }
  
  Widget _buildCustomSwitch() {
    return GestureDetector(
      onTap: enabled ? () => onChanged(!value) : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 50,
        height: 28,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14),
          color: value 
              ? (activeColor ?? AppColors.infoBlue)
              : AppColors.slate600.withOpacity(0.5),
          border: Border.all(
            color: value 
                ? (activeColor ?? AppColors.infoBlue)
                : AppColors.slate500,
            width: 1,
          ),
        ),
        child: AnimatedAlign(
          duration: const Duration(milliseconds: 200),
          alignment: value ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            width: 24,
            height: 24,
            margin: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: value
                ? Icon(
                    Icons.check,
                    size: 14,
                    color: activeColor ?? AppColors.infoBlue,
                  )
                : null,
          ),
        ),
      ),
    );
  }
}

/// Animated switch with smooth transitions
class AnimatedSettingSwitch extends StatefulWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final bool value;
  final ValueChanged<bool> onChanged;
  final bool enabled;
  final Color? iconColor;
  final Color? activeColor;
  final Duration animationDuration;
  
  const AnimatedSettingSwitch({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    required this.value,
    required this.onChanged,
    this.enabled = true,
    this.iconColor,
    this.activeColor,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  State<AnimatedSettingSwitch> createState() => _AnimatedSettingSwitchState();
}

class _AnimatedSettingSwitchState extends State<AnimatedSettingSwitch>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;
  
  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _colorAnimation = ColorTween(
      begin: AppColors.lightText.withOpacity(0.7),
      end: widget.activeColor ?? AppColors.infoBlue,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    if (widget.value) {
      _controller.value = 1.0;
    }
  }
  
  @override
  void didUpdateWidget(AnimatedSettingSwitch oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.value != oldWidget.value) {
      if (widget.value) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: AppColors.slate600,
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            children: [
              Transform.scale(
                scale: _scaleAnimation.value,
                child: Icon(
                  widget.icon,
                  color: widget.enabled 
                      ? (_colorAnimation.value ?? AppColors.lightText.withOpacity(0.7))
                      : AppColors.lightText.withOpacity(0.3),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title,
                      style: AppTextStyles.messageText.copyWith(
                        fontWeight: FontWeight.w500,
                        color: widget.enabled 
                            ? AppColors.lightText 
                            : AppColors.lightText.withOpacity(0.5),
                      ),
                    ),
                    if (widget.subtitle != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        widget.subtitle!,
                        style: AppTextStyles.captionText.copyWith(
                          color: widget.enabled 
                              ? AppColors.lightText.withOpacity(0.7)
                              : AppColors.lightText.withOpacity(0.3),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Switch(
                value: widget.value,
                onChanged: widget.enabled ? widget.onChanged : null,
                activeColor: widget.activeColor ?? AppColors.infoBlue,
                inactiveThumbColor: AppColors.lightText.withOpacity(0.5),
                inactiveTrackColor: AppColors.slate600.withOpacity(0.5),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Switch with confirmation dialog
class ConfirmationSettingSwitch extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final bool value;
  final ValueChanged<bool> onChanged;
  final String confirmationTitle;
  final String confirmationMessage;
  final String confirmButtonText;
  final String cancelButtonText;
  final bool enabled;
  final Color? iconColor;
  final Color? activeColor;
  final bool requireConfirmationOnlyForTrue;
  
  const ConfirmationSettingSwitch({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    required this.value,
    required this.onChanged,
    required this.confirmationTitle,
    required this.confirmationMessage,
    this.confirmButtonText = 'Confirm',
    this.cancelButtonText = 'Cancel',
    this.enabled = true,
    this.iconColor,
    this.activeColor,
    this.requireConfirmationOnlyForTrue = true,
  });

  @override
  Widget build(BuildContext context) {
    return SettingSwitch(
      title: title,
      subtitle: subtitle,
      icon: icon,
      value: value,
      onChanged: (newValue) => _handleChange(context, newValue),
      enabled: enabled,
      iconColor: iconColor,
      activeColor: activeColor,
    );
  }
  
  void _handleChange(BuildContext context, bool newValue) {
    // If we only require confirmation for enabling and we're disabling, proceed directly
    if (requireConfirmationOnlyForTrue && !newValue) {
      onChanged(newValue);
      return;
    }
    
    // If we're enabling or we require confirmation for both, show dialog
    if (newValue || !requireConfirmationOnlyForTrue) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: AppColors.slate800,
          title: Text(
            confirmationTitle,
            style: AppTextStyles.modalTitle,
          ),
          content: Text(
            confirmationMessage,
            style: AppTextStyles.messageText,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                cancelButtonText,
                style: AppTextStyles.smallButtonText.copyWith(
                  color: AppColors.lightText,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                onChanged(newValue);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: activeColor ?? AppColors.infoBlue,
              ),
              child: Text(
                confirmButtonText,
                style: AppTextStyles.smallButtonText,
              ),
            ),
          ],
        ),
      );
    } else {
      onChanged(newValue);
    }
  }
}

/// Switch style enumeration
enum SwitchStyle {
  material,
  cupertino,
  custom,
}