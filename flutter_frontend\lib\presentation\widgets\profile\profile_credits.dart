import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/enhanced_profile_provider.dart';

/// Profile credits widget showing credit information and refresh details
class ProfileCredits extends StatelessWidget {
  const ProfileCredits({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<EnhancedProfileProvider>(
      builder: (context, profileProvider, child) {
        final profile = profileProvider.userProfile;
        
        if (profile == null) {
          return _buildLoadingState();
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Credits header
              Row(
                children: [
                  Icon(
                    Icons.account_balance_wallet,
                    size: 20,
                    color: Colors.grey[700],
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Credits',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                      fontFamily: 'Inter',
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Credits progress bar
              _buildCreditsProgress(profile.currentCredits, profile.maxCredits),
              
              const SizedBox(height: 12),
              
              // Credits text
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${profile.currentCredits} of ${profile.maxCredits} credits',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'Inter',
                    ),
                  ),
                  Text(
                    '${(profile.remainingCreditsPercentage * 100).toStringAsFixed(0)}%',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: _getCreditsColor(profile.remainingCreditsPercentage),
                      fontFamily: 'Inter',
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Refresh information
              _buildRefreshInfo(profileProvider),
              
              // Refresh button if needed
              if (profile.needsRefresh) ...[
                const SizedBox(height: 12),
                _buildRefreshButton(context, profileProvider),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 12),
          Text(
            'Loading credits...',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
              fontFamily: 'Inter',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreditsProgress(int current, int max) {
    final percentage = max > 0 ? current / max : 0.0;
    
    return Column(
      children: [
        LinearProgressIndicator(
          value: percentage,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            _getCreditsColor(percentage),
          ),
          minHeight: 8,
        ),
      ],
    );
  }

  Widget _buildRefreshInfo(EnhancedProfileProvider profileProvider) {
    final refreshDate = profileProvider.getFormattedRefreshDate();
    final needsRefresh = profileProvider.needsRefresh;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: needsRefresh ? Colors.green[50] : Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: needsRefresh ? Colors.green[200]! : Colors.blue[200]!,
        ),
      ),
      child: Row(
        children: [
          Icon(
            needsRefresh ? Icons.refresh : Icons.schedule,
            size: 16,
            color: needsRefresh ? Colors.green[700] : Colors.blue[700],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              needsRefresh 
                  ? 'Credits ready to refresh!'
                  : 'Next refresh: $refreshDate',
              style: TextStyle(
                fontSize: 12,
                color: needsRefresh ? Colors.green[700] : Colors.blue[700],
                fontFamily: 'Inter',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRefreshButton(BuildContext context, EnhancedProfileProvider profileProvider) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: profileProvider.isLoading 
            ? null 
            : () => _handleRefresh(context, profileProvider),
        icon: profileProvider.isLoading
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.refresh, size: 16),
        label: Text(
          profileProvider.isLoading ? 'Refreshing...' : 'Refresh Credits',
          style: const TextStyle(
            fontSize: 14,
            fontFamily: 'Inter',
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Future<void> _handleRefresh(BuildContext context, EnhancedProfileProvider profileProvider) async {
    try {
      await profileProvider.refreshCreditsIfNeeded();
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Credits refreshed successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to refresh credits: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Color _getCreditsColor(double percentage) {
    if (percentage >= 0.7) {
      return Colors.green;
    } else if (percentage >= 0.3) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}