# Core Utilities

This directory contains core utility classes that provide essential functionality across the application.

## SentenceExtractor

The `SentenceExtractor` class provides professional-grade text processing for Groq response handling and TTS functionality.

### Features

- **Robust Brace Parsing**: Handles nested curly braces correctly
- **Sentence Extraction**: Extracts the first complete sentence from brace content
- **Display Formatting**: Provides clean text for user display while preserving original for processing
- **TTS Validation**: Validates extracted sentences for text-to-speech compatibility
- **German Language Support**: Full support for German special characters (äöüßÄÖÜ)
- **Production Quality**: Comprehensive error handling and edge case management

### Usage

#### Extract Sentence for TTS
```dart
String response = "Here is the answer: {Hello world! This is a test.}";
String? sentence = SentenceExtractor.extractFirstSentenceFromBraces(response);
// Returns: "Hello world!"
```

#### Format Text for Display
```dart
String original = "Answer: {Clean this content!} More text.";
String display = SentenceExtractor.formatForDisplay(original);
// Returns: "Answer: Clean this content! More text."
```

#### Validate for TTS
```dart
String? sentence = SentenceExtractor.extractFirstSentenceFromBraces(text);
if (SentenceExtractor.isValidForTTS(sentence)) {
  // Safe to use for TTS
  await ttsService.playTTS(sentence, messageId);
}
```

### Architecture

The `SentenceExtractor` follows enterprise software principles:

1. **Single Responsibility**: Each method has a clear, focused purpose
2. **Immutability**: All methods are static and pure (no side effects)
3. **Consistency**: Same logic used for both TTS extraction and display formatting
4. **Testability**: Comprehensive test coverage with edge cases
5. **Documentation**: Full API documentation with examples
6. **Error Handling**: Graceful handling of all edge cases

### Implementation Details

#### Brace Parsing Algorithm
- Uses character-by-character parsing to handle nested braces correctly
- Maintains brace count to find matching closing brace
- Handles malformed input gracefully (returns null for invalid cases)

#### Sentence Detection
- Uses regex pattern matching for sentence endings (., !, ?)
- Falls back to full content if no sentence endings found
- Trims whitespace and validates content

#### Display Formatting
- Replaces curly brace sections with extracted clean content
- Preserves original text structure and context
- Maintains consistency with TTS processing logic

### Testing

The utility is covered by comprehensive unit tests in `test/utils/sentence_extractor_test.dart`:

- ✅ 27 test cases covering all functionality
- ✅ Edge cases: empty text, nested braces, malformed input
- ✅ German language support with special characters
- ✅ TTS validation with length and content checks
- ✅ Display formatting with structure preservation

### Performance

- **O(n)** time complexity for brace parsing
- **O(1)** space complexity (no additional data structures)
- **Minimal regex usage** for optimal performance
- **No external dependencies** for maximum reliability