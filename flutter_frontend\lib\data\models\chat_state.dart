import 'message.dart';
import 'app_error.dart';

/// Enumeration of chat connection states
enum ConnectionState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error,
}

/// Enumeration of recording states
enum RecordingState {
  idle,
  connecting,
  listening,
  processing,
  error,
}

/// Model representing the overall chat state
class ChatState {
  final List<Message> messages;
  final String? currentMessageId;
  final ConnectionState connectionState;
  final RecordingState recordingState;
  final String partialText;
  final int retryCount;
  final AppError? lastError;
  final DateTime lastActivity;
  final bool isInitialized;
  
  const ChatState({
    required this.messages,
    this.currentMessageId,
    required this.connectionState,
    required this.recordingState,
    required this.partialText,
    required this.retryCount,
    this.lastError,
    required this.lastActivity,
    required this.isInitialized,
  });
  
  /// Create initial chat state
  factory ChatState.initial() {
    return ChatState(
      messages: const [],
      currentMessageId: null,
      connectionState: ConnectionState.disconnected,
      recordingState: RecordingState.idle,
      partialText: '',
      retryCount: 0,
      lastError: null,
      lastActivity: DateTime.now(),
      isInitialized: false,
    );
  }
  
  /// Create a copy with updated properties
  ChatState copyWith({
    List<Message>? messages,
    String? currentMessageId,
    ConnectionState? connectionState,
    RecordingState? recordingState,
    String? partialText,
    int? retryCount,
    AppError? lastError,
    DateTime? lastActivity,
    bool? isInitialized,
  }) {
    return ChatState(
      messages: messages ?? this.messages,
      currentMessageId: currentMessageId ?? this.currentMessageId,
      connectionState: connectionState ?? this.connectionState,
      recordingState: recordingState ?? this.recordingState,
      partialText: partialText ?? this.partialText,
      retryCount: retryCount ?? this.retryCount,
      lastError: lastError ?? this.lastError,
      lastActivity: lastActivity ?? this.lastActivity,
      isInitialized: isInitialized ?? this.isInitialized,
    );
  }
  
  /// Check if currently connected
  bool get isConnected => connectionState == ConnectionState.connected;
  
  /// Check if currently connecting
  bool get isConnecting => connectionState == ConnectionState.connecting;
  
  /// Check if currently recording
  bool get isRecording => recordingState == RecordingState.listening;
  
  /// Check if currently processing
  bool get isProcessing => recordingState == RecordingState.processing;
  
  /// Check if in error state
  bool get hasError => lastError != null;
  
  /// Check if can retry connection
  bool get canRetry => retryCount < 3 && connectionState == ConnectionState.error;
  
  /// Get the current message being edited
  Message? get currentMessage {
    if (currentMessageId == null) return null;
    try {
      return messages.firstWhere((m) => m.id == currentMessageId);
    } catch (e) {
      return null;
    }
  }
  
  /// Get user messages count
  int get userMessagesCount => messages.where((m) => m.isUser).length;
  
  /// Get AI messages count
  int get aiMessagesCount => messages.where((m) => !m.isUser).length;
  
  /// Get last user message
  Message? get lastUserMessage {
    try {
      return messages.lastWhere((m) => m.isUser);
    } catch (e) {
      return null;
    }
  }
  
  /// Get last AI message
  Message? get lastAiMessage {
    try {
      return messages.lastWhere((m) => !m.isUser);
    } catch (e) {
      return null;
    }
  }
  
  /// Check if session is active (has recent activity)
  bool get isSessionActive {
    final now = DateTime.now();
    final timeSinceLastActivity = now.difference(lastActivity);
    return timeSinceLastActivity.inMinutes < 30; // 30 minutes timeout
  }
  
  @override
  String toString() {
    return 'ChatState(messages: ${messages.length}, connection: $connectionState, recording: $recordingState, hasError: $hasError)';
  }
}