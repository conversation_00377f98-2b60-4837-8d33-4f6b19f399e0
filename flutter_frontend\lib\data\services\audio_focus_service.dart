import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:audio_session/audio_session.dart';
import '../models/app_error.dart';

/// Service for managing real audio focus and session management
/// Uses audio_session package for proper platform-specific audio handling
class AudioFocusService {
  static final AudioFocusService _instance = AudioFocusService._();
  static AudioFocusService get instance => _instance;
  
  AudioSession? _audioSession;
  StreamController<bool>? _audioFocusController;
  StreamController<AppError>? _errorController;
  StreamSubscription<AudioInterruptionEvent>? _interruptionSubscription;
  StreamSubscription<void>? _becomingNoisySubscription;
  
  bool _hasAudioFocus = false;
  bool _isInitialized = false;
  bool _isDisposed = false;
  
  // Callbacks for audio focus events
  VoidCallback? _onAudioFocusGained;
  VoidCallback? _onAudioFocusLost;
  VoidCallback? _onAudioInterrupted;
  VoidCallback? _onAudioResumed;
  
  AudioFocusService._();
  
  /// Stream of audio focus changes
  Stream<bool> get audioFocusStream => _audioFocusController?.stream ?? const Stream.empty();
  
  /// Stream of audio focus related errors
  Stream<AppError> get errorStream => _errorController?.stream ?? const Stream.empty();
  
  /// Whether the app currently has audio focus
  bool get hasAudioFocus => _hasAudioFocus;
  
  /// Whether the service is initialized
  bool get isInitialized => _isInitialized;
  
  /// Initialize the audio focus service
  Future<void> initialize() async {
    if (_isDisposed) {
      throw StateError('AudioFocusService has been disposed');
    }
    
    if (_isInitialized) return;
    
    try {
      _audioFocusController = StreamController<bool>.broadcast();
      _errorController = StreamController<AppError>.broadcast();
      
      // Initialize audio session
      _audioSession = await AudioSession.instance;
      
      // Configure audio session for recording
      await _configureAudioSession();
      
      // Set up interruption handling
      _setupInterruptionHandling();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('AudioFocusService initialized successfully');
      }
      
    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Failed to initialize audio focus service: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
      rethrow;
    }
  }
  
  /// Configure audio session for recording
  Future<void> _configureAudioSession() async {
    if (_audioSession == null) return;
    
    try {
      // Configure for recording with playback capability
      await _audioSession!.configure(AudioSessionConfiguration(
        avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
        avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.defaultToSpeaker |
                                       AVAudioSessionCategoryOptions.allowBluetooth,
        avAudioSessionMode: AVAudioSessionMode.spokenAudio,
        avAudioSessionRouteSharingPolicy: AVAudioSessionRouteSharingPolicy.defaultPolicy,
        avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
        androidAudioAttributes: const AndroidAudioAttributes(
          contentType: AndroidAudioContentType.speech,
          flags: AndroidAudioFlags.audibilityEnforced,
          usage: AndroidAudioUsage.voiceCommunication,
        ),
        androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
        androidWillPauseWhenDucked: true,
      ));
      
      if (kDebugMode) {
        print('Audio session configured for recording');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('Error configuring audio session: $e');
      }
      rethrow;
    }
  }
  
  /// Set up interruption handling for audio focus events
  void _setupInterruptionHandling() {
    if (_audioSession == null) return;
    
    // Handle interruptions (calls, other apps taking audio focus)
    _interruptionSubscription = _audioSession!.interruptionEventStream.listen(
      (event) {
        if (kDebugMode) {
          print('Audio interruption event: ${event.type}');
        }
        
        switch (event.type) {
          case AudioInterruptionType.begin:
            _handleAudioInterruption();
            break;
          case AudioInterruptionType.end:
            _handleAudioInterruptionEnd();
            break;
          case AudioInterruptionType.pause:
            _handleAudioInterruption();
            break;
          case AudioInterruptionType.duck:
            // Handle ducking - reduce volume but continue playing
            _handleAudioDucking();
            break;
        }
      },
      onError: (error) {
        final appError = AppError.audioRecordingFailed(
          details: 'Audio interruption handling error: $error',
          originalException: error is Exception ? error : Exception(error.toString()),
        );
        _errorController?.add(appError);
      },
    );
    
    // Handle becoming noisy events (headphones unplugged, etc.)
    _becomingNoisySubscription = _audioSession!.becomingNoisyEventStream.listen(
      (event) {
        if (kDebugMode) {
          print('Audio becoming noisy event');
        }
        _handleAudioBecomingNoisy();
      },
      onError: (error) {
        final appError = AppError.audioRecordingFailed(
          details: 'Audio becoming noisy handling error: $error',
          originalException: error is Exception ? error : Exception(error.toString()),
        );
        _errorController?.add(appError);
      },
    );
  }
  
  /// Handle audio interruption (focus lost)
  void _handleAudioInterruption() {
    if (_hasAudioFocus) {
      _setAudioFocus(false);
      _onAudioInterrupted?.call();
      _onAudioFocusLost?.call();
    }
  }
  
  /// Handle audio interruption end (focus potentially regained)
  void _handleAudioInterruptionEnd() {
    // Don't automatically regain focus - let the app decide
    _onAudioResumed?.call();
  }

  /// Handle audio ducking (reduce volume but continue)
  void _handleAudioDucking() {
    if (kDebugMode) {
      print('Audio ducking requested');
    }

    // For recording apps, we might want to pause instead of duck
    _handleAudioInterruption();
  }

  /// Handle audio becoming noisy (headphones unplugged, etc.)
  void _handleAudioBecomingNoisy() {
    if (_hasAudioFocus) {
      _setAudioFocus(false);
      _onAudioFocusLost?.call();
    }
  }
  
  /// Request audio focus for recording
  Future<bool> requestAudioFocus() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    if (_audioSession == null) {
      final error = AppError.audioRecordingFailed(
        details: 'Audio session not initialized',
      );
      _errorController?.add(error);
      return false;
    }
    
    try {
      // Activate audio session
      await _audioSession!.setActive(true);
      
      _setAudioFocus(true);
      
      if (kDebugMode) {
        print('Audio focus requested and granted');
      }
      
      return true;
      
    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Failed to request audio focus: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
      return false;
    }
  }
  
  /// Release audio focus
  Future<void> releaseAudioFocus() async {
    if (_audioSession == null) return;
    
    try {
      // Deactivate audio session
      await _audioSession!.setActive(false);
      
      _setAudioFocus(false);
      
      if (kDebugMode) {
        print('Audio focus released');
      }
      
    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Failed to release audio focus: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorController?.add(error);
    }
  }
  
  /// Set audio focus state and notify listeners
  void _setAudioFocus(bool hasFocus) {
    if (_hasAudioFocus == hasFocus) return;
    
    _hasAudioFocus = hasFocus;
    _audioFocusController?.add(hasFocus);
    
    if (hasFocus) {
      _onAudioFocusGained?.call();
    } else {
      _onAudioFocusLost?.call();
    }
  }
  
  /// Set callback for when audio focus is gained
  void setOnAudioFocusGained(VoidCallback callback) {
    _onAudioFocusGained = callback;
  }
  
  /// Set callback for when audio focus is lost
  void setOnAudioFocusLost(VoidCallback callback) {
    _onAudioFocusLost = callback;
  }
  
  /// Set callback for when audio is interrupted
  void setOnAudioInterrupted(VoidCallback callback) {
    _onAudioInterrupted = callback;
  }
  
  /// Set callback for when audio interruption ends
  void setOnAudioResumed(VoidCallback callback) {
    _onAudioResumed = callback;
  }
  
  /// Get audio session information for debugging
  Map<String, dynamic> getAudioSessionInfo() {
    if (_audioSession == null) {
      return {'status': 'not_initialized'};
    }
    
    return {
      'has_audio_focus': _hasAudioFocus,
      'is_initialized': _isInitialized,
      'platform': Platform.isAndroid ? 'android' : Platform.isIOS ? 'ios' : 'other',
    };
  }
  
  /// Dispose of the service and clean up resources
  Future<void> dispose() async {
    if (_isDisposed) return;
    
    _isDisposed = true;
    
    // Release audio focus if we have it
    if (_hasAudioFocus) {
      await releaseAudioFocus();
    }
    
    // Cancel subscriptions
    await _interruptionSubscription?.cancel();
    await _becomingNoisySubscription?.cancel();
    
    // Close streams
    await _audioFocusController?.close();
    await _errorController?.close();
    
    // Clear callbacks
    _onAudioFocusGained = null;
    _onAudioFocusLost = null;
    _onAudioInterrupted = null;
    _onAudioResumed = null;
    
    _isInitialized = false;
    
    if (kDebugMode) {
      print('AudioFocusService disposed');
    }
  }
}
