# Design Document

## Overview

The Deutschkorrekt backend is a FastAPI-based microservice that provides real-time speech-to-text processing and intelligent German language correction. The system integrates Deepgram for streaming speech recognition with Google ADK's multi-agent orchestration framework to deliver contextual language processing. The architecture supports automatic language detection and provides specialized processing paths for German corrections and English-to-German translations.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Mobile Frontend] -->|WebSocket Audio Stream| B[FastAPI Backend]
    B -->|Real-time Audio| C[Deepgram SDK]
    C -->|Partial Transcripts| B
    C -->|Final Transcripts| B
    B -->|Real-time Text| A
    B -->|Complete Text| D[Google ADK Multi-Agent System]
    D --> E[Gateway Agent]
    E -->|German Text| F[German Agent]
    E -->|English Text| G[English Agent]
    F -->|Corrections & Suggestions| B
    G -->|Translations| B
    B -->|Final Results| A
    H[Google Secret Manager] -->|API Keys| B
    I[Docker Container] --> J[Google Cloud Run]
```

### Component Architecture

The system follows a layered architecture pattern:

1. **API Layer**: FastAPI with WebSocket support for real-time communication
2. **Service Layer**: Business logic for audio processing and agent orchestration
3. **Integration Layer**: Deepgram and Google ADK integrations
4. **Infrastructure Layer**: Docker containerization and Google Cloud Run deployment

## Components and Interfaces

### 1. FastAPI Application (`main.py`)

**Responsibilities:**
- WebSocket endpoint management for `/stt`
- Request/response handling and validation
- Error handling and logging
- Health check endpoints

**Key Endpoints:**
- `GET /health` - Health check endpoint
- `WebSocket /stt` - Streaming speech-to-text with language processing

### 2. Speech-to-Text Service (`services/stt_service.py`)

**Responsibilities:**
- AssemblyAI SDK integration for real-time streaming transcription
- Real-time audio streaming and transcription using official SDK
- 20-second timeout enforcement
- Temporary text storage for agent processing
- SDK event handling (on_data, on_error, on_open, on_close)

**Key Methods:**
```python
class STTService:
    async def start_streaming_session(self, websocket: WebSocket) -> str
    async def process_audio_chunk(self, audio_data: bytes, session_id: str)
    async def finalize_transcription(self, session_id: str) -> str
    def get_complete_text(self, session_id: str) -> str
    async def _on_data(self, session_id: str, transcript: aai.RealtimeTranscript)
    async def _on_error(self, session_id: str, error: aai.RealtimeError)
    async def _on_open(self, session_id: str, session: aai.RealtimeSession)
    async def _on_close(self, session_id: str)
```

### 3. Multi-Agent Orchestration Service (`services/agent_service.py`)

**Responsibilities:**
- Google ADK agent initialization and management
- Language detection and routing logic
- Agent execution and result processing
- Session state management

**Key Methods:**
```python
class AgentService:
    async def process_text(self, text: str) -> dict
    async def initialize_agents(self)
    async def route_to_agent(self, text: str, detected_language: str) -> dict
```

### 4. Agent Definitions (`agents/`)

#### Gateway Agent (`agents/gateway_agent.py`)
**Purpose:** Language detection and routing
**Model:** gemini-2.5-flash-lite-preview-06-17
**Tools:** Language detection function

#### German Agent (`agents/german_agent.py`)
**Purpose:** German grammar correction and suggestions
**Model:** gemini-2.5-flash-lite-preview-06-17
**Prompt Strategy:**
```
You are a German language expert specializing in grammar correction and idiomatic expression improvement. 
When given German text, analyze it for:
1. Grammatical errors (syntax, conjugation, declension)
2. Non-idiomatic expressions that could sound more natural
3. Vocabulary improvements for conversational German

Provide corrections with explanations in English, but keep the corrected German text in German.
Format your response as JSON with 'original_text', 'corrected_text', 'suggestions', and 'explanations' fields.
```

#### English Agent (`agents/english_agent.py`)
**Purpose:** English to German translation with alternatives
**Model:** gemini-2.5-flash-lite-preview-06-17
**Prompt Strategy:**
```
You are a German translation expert. Translate the given English text into German, providing up to 3 natural, 
idiomatic alternatives that would be appropriate in conversational German.

Consider:
1. Formal vs informal register
2. Regional variations when appropriate
3. Context-appropriate vocabulary choices

Format your response as JSON with 'original_text' and 'translations' array containing up to 3 alternatives.
```

### 5. Configuration Management (`config/settings.py`)

**Responsibilities:**
- Environment variable management
- Google Secret Manager integration
- Application configuration

**Key Settings:**
```python
class Settings:
    deepgram_api_key: str
    google_project_id: str
    max_recording_duration: int = 20
    gemini_model: str = "gemini-2.5-flash-lite-preview-06-17"
```

### 6. WebSocket Manager (`websocket/manager.py`)

**Responsibilities:**
- WebSocket connection lifecycle management
- Message broadcasting and routing
- Connection state tracking
- Error handling for dropped connections

## Data Models

### 1. Audio Session Model
```python
@dataclass
class AudioSession:
    session_id: str
    websocket: WebSocket
    start_time: datetime
    complete_text: str = ""
    is_active: bool = True
    assemblyai_session_id: Optional[str] = None
```

### 2. Language Processing Result
```python
@dataclass
class ProcessingResult:
    original_text: str
    detected_language: str
    processed_content: dict
    processing_time: float
    agent_used: str
```

### 3. German Correction Response
```python
@dataclass
class GermanCorrectionResponse:
    original_text: str
    corrected_text: str
    suggestions: List[str]
    explanations: List[str]
```

### 4. Translation Response
```python
@dataclass
class TranslationResponse:
    original_text: str
    translations: List[str]
```

## Error Handling

### 1. AssemblyAI Integration Errors
- **Connection Failures**: Retry logic with exponential backoff
- **Authentication Errors**: Clear error messages to frontend
- **Rate Limiting**: Queue management and user notification

### 2. Google ADK Agent Errors
- **Model Unavailability**: Fallback to alternative processing
- **Timeout Errors**: Graceful degradation with partial results
- **Invalid Responses**: Error logging and user-friendly messages

### 3. WebSocket Connection Errors
- **Connection Drops**: Automatic cleanup of resources
- **Invalid Audio Data**: Validation and error responses
- **Timeout Handling**: 20-second limit enforcement

### 4. Infrastructure Errors
- **Memory Exhaustion**: Resource monitoring and cleanup
- **Secret Manager Access**: Fallback configuration options
- **Container Startup**: Health check implementation

## Testing Strategy

### 1. Unit Tests
- **Agent Logic**: Test language detection and processing logic
- **Service Methods**: Mock external API calls
- **Data Models**: Validation and serialization tests
- **Configuration**: Environment variable handling

### 2. Integration Tests
- **AssemblyAI Integration**: Test with mock audio streams
- **Google ADK Agents**: Test agent responses with sample texts
- **WebSocket Communication**: Test connection lifecycle
- **Secret Manager**: Test configuration retrieval

### 3. End-to-End Tests
- **Complete Audio Processing Flow**: Mock frontend to backend
- **Error Scenarios**: Test timeout and failure conditions
- **Performance Tests**: Load testing with multiple concurrent streams
- **Container Tests**: Docker build and deployment validation

### 4. Load Testing
- **Concurrent WebSocket Connections**: Test scalability limits
- **Agent Processing Throughput**: Measure response times
- **Memory Usage**: Monitor resource consumption patterns
- **AssemblyAI Rate Limits**: Test quota management

## Security Considerations

### 1. API Key Management
- Google Secret Manager for production secrets
- Environment variable validation
- No hardcoded credentials in source code

### 2. WebSocket Security
- Connection rate limiting
- Input validation for audio data
- Session timeout enforcement

### 3. Data Privacy
- No persistent storage of audio or transcriptions
- Temporary text cleanup after processing
- Minimal logging of sensitive data

## Deployment Configuration

### 1. Docker Configuration
- Multi-stage build for optimization
- Non-root user execution
- Health check implementation
- Environment variable injection

### 2. Google Cloud Run Configuration
- CPU and memory allocation
- Concurrency settings for WebSocket handling
- Environment variable configuration
- Service account permissions for Secret Manager

### 3. Monitoring and Logging
- Structured logging with correlation IDs
- Performance metrics collection
- Error rate monitoring
- Resource utilization tracking