"""
Data models for the Deutschkorrekt application.
"""

from datetime import datetime
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from fastapi import WebSocket
from pydantic import BaseModel, Field

@dataclass
class AudioSession:
    """Represents an active audio streaming session."""
    
    session_id: str
    websocket: WebSocket
    start_time: datetime = field(default_factory=datetime.now)
    complete_text: str = ""
    is_active: bool = True
    assemblyai_session_id: Optional[str] = None
    
    # Track the duration to enforce the time limit
    @property
    def duration(self) -> float:
        """Calculate the current session duration in seconds."""
        return (datetime.now() - self.start_time).total_seconds()

@dataclass
class ProcessingResult:
    """Result of language processing by an agent."""
    
    original_text: str
    detected_language: str
    processed_content: Dict[str, Any]
    processing_time: float
    agent_used: str

@dataclass
class GermanCorrectionResponse:
    """Response from the German correction agent."""
    
    original_text: str
    corrected_text: str
    suggestions: List[str]
    explanations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "original_text": self.original_text,
            "corrected_text": self.corrected_text,
            "suggestions": self.suggestions,
            "explanations": self.explanations
        }

@dataclass
class TranslationResponse:
    """Response from the English-to-German translation agent."""
    
    original_text: str
    translations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "original_text": self.original_text,
            "translations": self.translations
        }

@dataclass
class WebSocketMessage:
    """Message sent over WebSocket to the client."""
    
    message_type: str  # "transcript", "final_transcript", "correction", "translation", "error"
    data: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "message_type": self.message_type,
            "data": self.data
        }

# TTS API Models
class TTSRequest(BaseModel):
    """Request model for Text-to-Speech API."""
    
    text: str = Field(..., min_length=1, max_length=500, description="Text to convert to speech")
    message_id: str = Field(..., min_length=1, description="Unique identifier for the message")
    voice_config: str = Field(default="de-DE-Chirp3-HD-Aoede", description="Voice configuration to use")

class TTSResponse(BaseModel):
    """Response model for successful Text-to-Speech API calls."""
    
    success: bool = Field(True, description="Whether the TTS generation was successful")
    audio_data: bytes = Field(..., description="Generated audio data")
    content_type: str = Field(default="audio/mpeg", description="MIME type of the audio data")
    duration_seconds: float = Field(..., description="Estimated duration of the audio in seconds")
    processing_time: float = Field(..., description="Time taken to generate the audio")
    original_text: str = Field(..., description="Original input text")
    sanitized_text: str = Field(..., description="Text after sanitization")

class TTSErrorResponse(BaseModel):
    """Response model for failed Text-to-Speech API calls."""
    
    success: bool = Field(False, description="Whether the TTS generation was successful")
    error_message: str = Field(..., description="Description of the error")
    error_code: str = Field(..., description="Error code for client handling")
    processing_time: float = Field(..., description="Time taken before the error occurred")
    retry_after: Optional[int] = Field(None, description="Seconds to wait before retrying (for rate limiting)")

class TTSHealthResponse(BaseModel):
    """Response model for TTS service health check."""
    
    healthy: bool = Field(..., description="Whether the TTS service is healthy")
    response_time: float = Field(..., description="Time taken for the health check")
    message: Optional[str] = Field(None, description="Health status message")
    audio_size: Optional[int] = Field(None, description="Size of test audio generated")
    error: Optional[str] = Field(None, description="Error message if unhealthy")
    error_code: Optional[str] = Field(None, description="Error code if unhealthy")