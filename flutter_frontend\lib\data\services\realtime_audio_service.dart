import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_audio_capture/flutter_audio_capture.dart';
import '../models/app_error.dart';
import '../models/audio_config.dart';
import 'permissions_service.dart';
import 'app_lifecycle_service.dart';
import 'audio_focus_service.dart';
import 'audio_isolate_processor.dart';

/// Real-time audio service using flutter_audio_capture for actual microphone streaming
class RealtimeAudioService {
  static final RealtimeAudioService _instance = RealtimeAudioService._();
  static RealtimeAudioService get instance => _instance;
  
  final FlutterAudioCapture _audioCapture = FlutterAudioCapture();
  final PermissionsService _permissionsService = PermissionsService.instance;
  final AppLifecycleService _lifecycleService = AppLifecycleService.instance;
  final AudioFocusService _audioFocusService = AudioFocusService.instance;
  final AudioIsolateProcessor _audioProcessor = AudioIsolateProcessor.instance;
  
  StreamController<Uint8List>? _audioStreamController;
  StreamController<AppError>? _errorStreamController;
  StreamController<bool>? _recordingStateController;
  
  StreamSubscription<bool>? _audioFocusSubscription;
  StreamSubscription<AppLifecycleState>? _lifecycleSubscription;
  
  AudioConfig _config = AudioConfig.defaultConfig();
  bool _isRecording = false;
  bool _isInitialized = false;
  bool _isDisposed = false;
  bool _wasRecordingBeforePause = false;
  
  RealtimeAudioService._();
  
  /// Stream of audio data chunks (PCM16 format)
  Stream<Uint8List> get audioStream => _audioStreamController?.stream ?? const Stream.empty();
  
  /// Stream of audio service errors
  Stream<AppError> get errorStream => _errorStreamController?.stream ?? const Stream.empty();
  
  /// Stream of recording state changes
  Stream<bool> get recordingStateStream => _recordingStateController?.stream ?? const Stream.empty();
  
  /// Check if currently recording
  bool get isRecording => _isRecording;
  
  /// Get current audio configuration
  AudioConfig get config => _config;
  
  /// Check if service is initialized
  bool get isInitialized => _isInitialized;
  
  /// Initialize the real-time audio service
  Future<void> initialize({AudioConfig? config}) async {
    if (_isDisposed) {
      throw StateError('RealtimeAudioService has been disposed');
    }
    
    if (_isInitialized) return;
    
    _config = config ?? AudioConfig.defaultConfig();
    
    _audioStreamController = StreamController<Uint8List>.broadcast();
    _errorStreamController = StreamController<AppError>.broadcast();
    _recordingStateController = StreamController<bool>.broadcast();
    
    // Initialize audio isolate processor
    await _audioProcessor.initialize();

    // Initialize lifecycle service
    await _lifecycleService.initialize();

    // Set up lifecycle callbacks
    _setupLifecycleCallbacks();

    // Set up audio processing pipeline
    _setupAudioProcessingPipeline();
    
    _isInitialized = true;
    if (kDebugMode) {
      print('RealtimeAudioService initialized with config: $_config');
    }
  }
  
  /// Set up lifecycle callbacks for proper audio handling
  void _setupLifecycleCallbacks() {
    _lifecycleService.setOnAppPaused(() {
      if (_isRecording) {
        _wasRecordingBeforePause = true;
        pauseRecording();
      }
    });
    
    _lifecycleService.setOnAppResumed(() {
      if (_wasRecordingBeforePause) {
        _wasRecordingBeforePause = false;
        resumeRecording();
      }
    });
    
    _audioFocusSubscription = _lifecycleService.audioFocusStream.listen((hasFocus) {
      if (!hasFocus && _isRecording) {
        pauseRecording();
      }
    });
    
    _lifecycleSubscription = _lifecycleService.lifecycleStream.listen((state) {
      if (state == AppLifecycleState.detached && _isRecording) {
        stopRecording();
      }
    });
  }
  
  /// Request microphone permissions
  Future<bool> requestPermissions() async {
    try {
      return await _permissionsService.requestMicrophonePermissionWithHandling();
    } catch (e) {
      final error = AppError.microphonePermissionDenied(
        details: 'Failed to request microphone permissions: $e',
      );
      _errorStreamController?.add(error);
      return false;
    }
  }
  
  /// Check if has microphone permissions
  Future<bool> hasPermissions() async {
    return await _permissionsService.hasMicrophonePermission();
  }
  
  /// Start real-time audio recording with streaming
  Future<void> startRecording() async {
    if (_isDisposed) {
      throw StateError('RealtimeAudioService has been disposed');
    }
    
    if (!_isInitialized) {
      await initialize();
    }
    
    if (_isRecording) {
      if (kDebugMode) {
        print('Already recording, ignoring start request');
      }
      return;
    }
    
    try {
      // Check permissions
      if (!await hasPermissions()) {
        final hasPermission = await requestPermissions();
        if (!hasPermission) {
          final error = AppError.microphonePermissionDenied();
          _errorStreamController?.add(error);
          return;
        }
      }

      // Request audio focus
      final hasAudioFocus = await _audioFocusService.requestAudioFocus();
      if (!hasAudioFocus) {
        final error = AppError.audioRecordingFailed(
          details: 'Failed to acquire audio focus for recording',
        );
        _errorStreamController?.add(error);
        return;
      }
      
      // Start real-time audio capture
      await _audioCapture.start(
        _onAudioData,
        _onAudioError,
        sampleRate: _config.sampleRate, // 16000 Hz for Deepgram compatibility
        bufferSize: _config.chunkSize,  // Buffer size for iOS
      );
      
      _isRecording = true;
      _recordingStateController?.add(true);
      
      if (kDebugMode) {
        print('Real-time audio recording started with sample rate: ${_config.sampleRate}Hz');
      }
      
    } catch (e) {
      _isRecording = false;
      _recordingStateController?.add(false);
      
      final error = AppError.audioRecordingFailed(
        details: 'Failed to start real-time audio recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorStreamController?.add(error);
    }
  }
  
  /// Set up audio processing pipeline with isolate
  void _setupAudioProcessingPipeline() {
    // Forward processed audio from isolate to our stream
    _audioProcessor.processedAudioStream.listen(
      (processedAudioData) {
        _audioStreamController?.add(processedAudioData);
      },
      onError: (error) {
        final appError = AppError.audioRecordingFailed(
          details: 'Audio processing error: $error',
          originalException: Exception(error.toString()),
        );
        _errorStreamController?.add(appError);
      },
    );

    // Forward processing errors
    _audioProcessor.errorStream.listen(
      (error) {
        final appError = AppError.audioRecordingFailed(
          details: 'Audio isolate error: $error',
          originalException: Exception(error),
        );
        _errorStreamController?.add(appError);
      },
    );
  }

  /// Handle incoming audio data from flutter_audio_capture
  void _onAudioData(dynamic audioData) {
    if (!_isRecording || _isDisposed) return;

    try {
      // Convert Float64List to PCM16 format (Uint8List)
      final Float64List audioBuffer = Float64List.fromList(audioData.cast<double>());
      final Uint8List pcm16Data = _convertToPCM16(audioBuffer);

      // Process audio data in isolate for better performance
      _audioProcessor.processAudioData(pcm16Data);

    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Error processing audio data: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorStreamController?.add(error);
    }
  }
  
  /// Handle audio capture errors
  void _onAudioError(Object error) {
    final appError = AppError.audioRecordingFailed(
      details: 'Audio capture error: $error',
      originalException: error is Exception ? error : Exception(error.toString()),
    );
    _errorStreamController?.add(appError);
  }
  
  /// Convert Float64List audio data to PCM16 format (Uint8List)
  Uint8List _convertToPCM16(Float64List audioBuffer) {
    final int16Buffer = Int16List(audioBuffer.length);
    
    // Convert from Float64 [-1.0, 1.0] to Int16 [-32768, 32767]
    for (int i = 0; i < audioBuffer.length; i++) {
      // Clamp the value to prevent overflow
      double sample = audioBuffer[i].clamp(-1.0, 1.0);
      
      // Apply gain boost for quiet audio (optional, can be configured)
      sample *= 2.0; // Boost by 2x, adjust as needed
      sample = sample.clamp(-1.0, 1.0); // Clamp again after boost
      
      // Convert to 16-bit signed integer
      int16Buffer[i] = (sample * 32767).round();
    }
    
    // Convert Int16List to Uint8List (little-endian byte order)
    final byteData = ByteData(int16Buffer.length * 2);
    for (int i = 0; i < int16Buffer.length; i++) {
      byteData.setInt16(i * 2, int16Buffer[i], Endian.little);
    }
    
    return byteData.buffer.asUint8List();
  }
  
  /// Stop audio recording
  Future<void> stopRecording() async {
    if (!_isRecording) {
      if (kDebugMode) {
        print('Not recording, ignoring stop request');
      }
      return;
    }

    try {
      await _audioCapture.stop();
      _isRecording = false;
      _recordingStateController?.add(false);

      // Release audio focus when stopping recording
      await _audioFocusService.releaseAudioFocus();

      if (kDebugMode) {
        print('Real-time audio recording stopped');
      }

    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Failed to stop real-time audio recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorStreamController?.add(error);
    }
  }
  
  /// Pause audio recording
  Future<void> pauseRecording() async {
    if (!_isRecording) return;
    
    try {
      await _audioCapture.stop();
      if (kDebugMode) {
        print('Real-time audio recording paused');
      }
    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Failed to pause real-time audio recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorStreamController?.add(error);
    }
  }
  
  /// Resume audio recording
  Future<void> resumeRecording() async {
    if (!_isRecording) return;
    
    try {
      await _audioCapture.start(
        _onAudioData,
        _onAudioError,
        sampleRate: _config.sampleRate,
        bufferSize: _config.chunkSize,
      );
      if (kDebugMode) {
        print('Real-time audio recording resumed');
      }
    } catch (e) {
      final error = AppError.audioRecordingFailed(
        details: 'Failed to resume real-time audio recording: $e',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
      _errorStreamController?.add(error);
    }
  }
  
  /// Get audio service statistics including isolate processing stats
  Future<Map<String, dynamic>> getStats() async {
    final processingStats = await _audioProcessor.getProcessingStats();

    return {
      'isInitialized': _isInitialized,
      'isRecording': _isRecording,
      'sampleRate': _config.sampleRate,
      'chunkSize': _config.chunkSize,
      'wasRecordingBeforePause': _wasRecordingBeforePause,
      'audioProcessorInitialized': _audioProcessor.isInitialized,
      'processingStats': processingStats,
    };
  }
  
  /// Dispose of the service and clean up resources
  Future<void> dispose() async {
    if (_isDisposed) return;
    
    _isDisposed = true;
    
    if (_isRecording) {
      await stopRecording();
    }

    // Dispose audio processor
    await _audioProcessor.dispose();

    await _audioFocusSubscription?.cancel();
    await _lifecycleSubscription?.cancel();
    
    await _audioStreamController?.close();
    await _errorStreamController?.close();
    await _recordingStateController?.close();
    
    if (kDebugMode) {
      print('RealtimeAudioService disposed');
    }
  }
}
