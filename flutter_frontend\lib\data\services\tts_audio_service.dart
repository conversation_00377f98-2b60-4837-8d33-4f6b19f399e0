import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:audioplayers/audioplayers.dart';
import 'audio_cache_manager.dart';

/// Service for coordinating TTS requests with backend API and audio playback
/// Handles HTTP communication, caching, and audio playback functionality
class TTSAudioService {
  static const String _baseUrl = 'https://deutschkorrekt-backend-645996191396.europe-west3.run.app';
  static const String _ttsEndpoint = '/api/tts';
  static const Duration _requestTimeout = Duration(seconds: 10);
  static const int _maxRetryAttempts = 3;
  static const Duration _retryDelay = Duration(seconds: 1);
  
  final AudioCacheManager _cacheManager;
  final http.Client _httpClient;
  final AudioPlayer _audioPlayer;
  
  // Track current playback state
  String? _currentlyPlayingMessageId;
  bool _isPlaying = false;
  StreamSubscription<PlayerState>? _playerStateSubscription;
  StreamSubscription<Duration>? _positionSubscription;
  
  /// Initialize TTS audio service with cache manager and HTTP client
  TTSAudioService({
    AudioCacheManager? cacheManager,
    http.Client? httpClient,
  }) : _cacheManager = cacheManager ?? AudioCacheManager(),
       _httpClient = httpClient ?? http.Client(),
       _audioPlayer = AudioPlayer() {
    _initializeAudioPlayer();
  }
  
  /// Initialize audio player with proper configuration
  void _initializeAudioPlayer() {
    // Configure audio player for TTS playback
    _audioPlayer.setReleaseMode(ReleaseMode.stop);
    
    // Configure audio context for proper session management
    _configureAudioSession();
    
    // Listen to player state changes
    _playerStateSubscription = _audioPlayer.onPlayerStateChanged.listen((PlayerState state) {
      debugPrint('🎵 Audio player state changed: $state');
      
      switch (state) {
        case PlayerState.playing:
          _isPlaying = true;
          break;
        case PlayerState.paused:
        case PlayerState.stopped:
        case PlayerState.completed:
          _isPlaying = false;
          _currentlyPlayingMessageId = null;
          break;
        case PlayerState.disposed:
          _isPlaying = false;
          _currentlyPlayingMessageId = null;
          break;
      }
    });
    
    // Listen to position changes for debugging (disabled to reduce log spam)
    // _positionSubscription = _audioPlayer.onPositionChanged.listen((Duration position) {
    //   // Only log occasionally to avoid spam
    //   if (position.inSeconds % 2 == 0) {
    //     debugPrint('🎵 Audio position: ${position.inSeconds}s');
    //   }
    // });
    
    debugPrint('🎵 Audio player initialized');
  }
  
  /// Configure audio session for proper background/foreground handling
  void _configureAudioSession() {
    try {
      // Set audio context for TTS playback
      // This ensures proper audio session management on both iOS and Android
      _audioPlayer.setAudioContext(AudioContext(
        iOS: AudioContextIOS(
          category: AVAudioSessionCategory.playback,
          options: {
            AVAudioSessionOptions.duckOthers,
          },
        ),
        android: AudioContextAndroid(
          isSpeakerphoneOn: true,
          stayAwake: false,
          contentType: AndroidContentType.speech,
          usageType: AndroidUsageType.media,
          audioFocus: AndroidAudioFocus.gain,
        ),
      ));
      
      debugPrint('🎵 Audio session configured for TTS playback');
      
    } catch (e) {
      debugPrint('⚠️ Failed to configure audio session: $e');
      // Continue without custom audio session configuration
    }
  }
  
  /// Check if audio is currently playing
  bool get isPlaying => _isPlaying;
  
  /// Get the message ID of currently playing audio
  String? get currentlyPlayingMessageId => _currentlyPlayingMessageId;
  
  /// Play TTS audio for the given text and message ID
  /// Implements cache-first logic: checks cache before making API call
  Future<TTSPlaybackResult> playTTS(String text, String messageId) async {
    try {
      debugPrint('🎤 TTS playback requested for message: $messageId');
      debugPrint('📝 Text: "${text.length > 50 ? text.substring(0, 50) + "..." : text}"');
      
      // Stop any currently playing audio
      if (_isPlaying) {
        await stopPlayback();
      }
      
      // Step 1: Check cache first (cache-first playback logic)
      final cachedAudioPath = await _getCachedAudioWithFallback(messageId);
      
      if (cachedAudioPath != null) {
        debugPrint('✅ Found cached audio for message $messageId');
        final cacheResult = await _playFromCache(cachedAudioPath, messageId);
        
        // If cache playback fails, continue to backend request
        if (cacheResult.success) {
          return cacheResult;
        } else {
          debugPrint('⚠️ Cache playback failed, falling back to backend request');
          // Continue to backend request below
        }
      }
      
      // Step 2: No cache found, request from backend
      debugPrint('🌐 No cached audio found, requesting from backend...');
      final ttsResult = await _requestTTSFromBackend(text, messageId);
      
      if (!ttsResult.success) {
        return TTSPlaybackResult(
          success: false,
          errorMessage: ttsResult.errorMessage,
          errorCode: ttsResult.errorCode,
          fromCache: false,
          canRetry: true,
        );
      }
      
      // Step 3: Cache the audio data with enhanced error handling
      await _cacheAudioWithFallback(messageId, ttsResult.audioData!);
      
      // Step 4: Play the audio from cached file
      final newCachedAudioPath = await _cacheManager.getCachedAudio(messageId);
      if (newCachedAudioPath != null) {
        return await _playFromCache(newCachedAudioPath, messageId);
      } else {
        // Fallback: play from memory if caching failed
        return await _playFromBytes(ttsResult.audioData!, messageId, ttsResult.durationSeconds ?? 0.0);
      }
      
    } catch (e) {
      debugPrint('❌ Error in TTS playback: $e');
      return TTSPlaybackResult(
        success: false,
        errorMessage: 'TTS playback failed: $e',
        errorCode: 'PLAYBACK_ERROR',
        fromCache: false,
        canRetry: true,
        userFriendlyMessage: 'Audio playback failed. Please try again.',
      );
    }
  }
  
  /// Stop current audio playback
  Future<void> stopPlayback() async {
    try {
      if (!_isPlaying) return;
      
      debugPrint('🛑 Stopping TTS playback for message: $_currentlyPlayingMessageId');
      
      // Stop the audio player
      await _audioPlayer.stop();
      
      _isPlaying = false;
      _currentlyPlayingMessageId = null;
      
      debugPrint('✅ TTS playback stopped');
      
    } catch (e) {
      debugPrint('❌ Error stopping TTS playback: $e');
      _isPlaying = false;
      _currentlyPlayingMessageId = null;
    }
  }
  
  /// Play audio from cached file
  Future<TTSPlaybackResult> _playFromCache(String cachedAudioPath, String messageId) async {
    try {
      debugPrint('🎵 Playing cached audio from: $cachedAudioPath');
      
      // Verify file exists
      final file = File(cachedAudioPath);
      if (!await file.exists()) {
        debugPrint('❌ Cached audio file not found: $cachedAudioPath');
        return TTSPlaybackResult(
          success: false,
          errorMessage: 'Cached audio file not found',
          errorCode: 'FILE_NOT_FOUND',
          fromCache: true,
          canRetry: true,
        );
      }
      
      // Set current playing state
      _currentlyPlayingMessageId = messageId;
      
      // Play audio from file
      await _audioPlayer.play(DeviceFileSource(cachedAudioPath));
      
      debugPrint('✅ Started playing cached audio for message: $messageId');
      
      return TTSPlaybackResult(
        success: true,
        fromCache: true,
        cachedFilePath: cachedAudioPath,
      );
      
    } catch (e) {
      debugPrint('❌ Error playing cached audio: $e');
      _isPlaying = false;
      _currentlyPlayingMessageId = null;
      
      return TTSPlaybackResult(
        success: false,
        errorMessage: 'Failed to play cached audio: $e',
        errorCode: 'CACHE_PLAYBACK_ERROR',
        fromCache: true,
        canRetry: true,
      );
    }
  }
  
  /// Get cached audio with error handling and fallback behavior
  Future<String?> _getCachedAudioWithFallback(String messageId) async {
    try {
      return await _cacheManager.getCachedAudio(messageId);
    } catch (e) {
      debugPrint('⚠️ Cache error for message $messageId: $e');
      
      // Try to validate and clean cache if there are issues
      try {
        await _cacheManager.validateAndCleanCache();
        debugPrint('🧹 Cache validation completed, retrying...');
        
        // Retry once after cache cleanup
        return await _cacheManager.getCachedAudio(messageId);
      } catch (cleanupError) {
        debugPrint('❌ Cache cleanup failed: $cleanupError');
        return null;
      }
    }
  }
  
  /// Cache audio data with enhanced error handling and fallback behavior
  Future<void> _cacheAudioWithFallback(String messageId, Uint8List audioData) async {
    try {
      await _cacheManager.cacheAudio(messageId, audioData);
      debugPrint('✅ Audio cached successfully for message $messageId');
    } catch (e) {
      debugPrint('⚠️ Failed to cache audio for message $messageId: $e');
      
      // Try cache cleanup and retry once
      try {
        debugPrint('🧹 Attempting cache cleanup and retry...');
        await _cacheManager.validateAndCleanCache();
        
        // Retry caching after cleanup
        await _cacheManager.cacheAudio(messageId, audioData);
        debugPrint('✅ Audio cached successfully after cleanup for message $messageId');
      } catch (retryError) {
        debugPrint('❌ Cache retry failed for message $messageId: $retryError');
        
        // Final fallback: try to clear old cache and retry one more time
        try {
          await _cacheManager.clearOldCache();
          await _cacheManager.cacheAudio(messageId, audioData);
          debugPrint('✅ Audio cached successfully after clearing old cache for message $messageId');
        } catch (finalError) {
          debugPrint('❌ Final cache attempt failed for message $messageId: $finalError');
          // Continue without caching - audio will still play from memory
        }
      }
    }
  }
  
  /// Play audio from bytes (fallback when caching fails)
  Future<TTSPlaybackResult> _playFromBytes(Uint8List audioData, String messageId, double durationSeconds) async {
    try {
      debugPrint('🎵 Playing audio from bytes for message: $messageId (${audioData.length} bytes)');
      
      // Set current playing state
      _currentlyPlayingMessageId = messageId;
      
      // Play audio from bytes
      await _audioPlayer.play(BytesSource(audioData));
      
      debugPrint('✅ Started playing audio from bytes for message: $messageId');
      
      return TTSPlaybackResult(
        success: true,
        fromCache: false,
        audioData: audioData,
        durationSeconds: durationSeconds,
      );
      
    } catch (e) {
      debugPrint('❌ Error playing audio from bytes: $e');
      _isPlaying = false;
      _currentlyPlayingMessageId = null;
      
      return TTSPlaybackResult(
        success: false,
        errorMessage: 'Failed to play audio from bytes: $e',
        errorCode: 'BYTES_PLAYBACK_ERROR',
        fromCache: false,
        canRetry: true,
      );
    }
  }
  
  /// Request TTS audio from backend API with retry logic
  Future<TTSBackendResult> _requestTTSFromBackend(String text, String messageId) async {
    int attemptCount = 0;
    
    while (attemptCount < _maxRetryAttempts) {
      attemptCount++;
      
      try {
        debugPrint('🌐 TTS API request attempt $attemptCount/$_maxRetryAttempts');
        
        final requestBody = {
          'text': text,
          'message_id': messageId,
          'voice_config': 'de-DE-Chirp3-HD-Aoede',
        };
        
        final response = await _httpClient.post(
          Uri.parse('$_baseUrl$_ttsEndpoint'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'audio/mpeg',  // Accept binary audio data, not JSON
          },
          body: json.encode(requestBody),
        ).timeout(_requestTimeout);
        
        debugPrint('📡 TTS API response: ${response.statusCode}');
        
        if (response.statusCode == 200) {
          return await _parseSuccessResponse(response);
        } else {
          return await _parseErrorResponse(response, attemptCount);
        }
        
      } on TimeoutException {
        debugPrint('⏰ TTS API request timeout (attempt $attemptCount)');
        
        if (attemptCount >= _maxRetryAttempts) {
          return TTSBackendResult(
            success: false,
            errorMessage: 'TTS request timed out after $_maxRetryAttempts attempts',
            errorCode: 'TIMEOUT',
          );
        }
        
        await Future.delayed(_retryDelay * attemptCount);
        
      } catch (e) {
        debugPrint('❌ TTS API request error (attempt $attemptCount): $e');
        
        if (attemptCount >= _maxRetryAttempts) {
          return TTSBackendResult(
            success: false,
            errorMessage: 'TTS request failed: $e',
            errorCode: 'NETWORK_ERROR',
          );
        }
        
        await Future.delayed(_retryDelay * attemptCount);
      }
    }
    
    return TTSBackendResult(
      success: false,
      errorMessage: 'TTS request failed after $_maxRetryAttempts attempts',
      errorCode: 'MAX_RETRIES_EXCEEDED',
    );
  }
  
  /// Parse successful TTS API response
  Future<TTSBackendResult> _parseSuccessResponse(http.Response response) async {
    try {
      // The backend returns raw binary MP3 data, not JSON
      final audioData = response.bodyBytes;
      
      if (audioData.isEmpty) {
        return TTSBackendResult(
          success: false,
          errorMessage: 'No audio data in response',
          errorCode: 'INVALID_RESPONSE',
        );
      }
      
      // Extract metadata from response headers
      final contentType = response.headers['content-type'] ?? 'audio/mpeg';
      final durationSeconds = double.tryParse(response.headers['x-duration-seconds'] ?? '0') ?? 0.0;
      final processingTime = double.tryParse(response.headers['x-processing-time'] ?? '0') ?? 0.0;
      
      debugPrint('✅ TTS API success: ${audioData.length} bytes audio data');
      debugPrint('📊 Content-Type: $contentType, Duration: ${durationSeconds}s, Processing: ${processingTime}s');
      
      return TTSBackendResult(
        success: true,
        audioData: audioData,
        contentType: contentType,
        durationSeconds: durationSeconds,
        processingTime: processingTime,
        originalText: '', // Not available in binary response
        sanitizedText: '', // Not available in binary response
      );
      
    } catch (e) {
      debugPrint('❌ Error parsing TTS success response: $e');
      return TTSBackendResult(
        success: false,
        errorMessage: 'Failed to parse TTS response: $e',
        errorCode: 'PARSE_ERROR',
      );
    }
  }
  
  /// Parse error TTS API response
  Future<TTSBackendResult> _parseErrorResponse(http.Response response, int attemptCount) async {
    try {
      final responseData = json.decode(response.body) as Map<String, dynamic>;
      
      final errorMessage = responseData['error_message'] as String? ?? 'Unknown error';
      final errorCode = responseData['error_code'] as String? ?? 'UNKNOWN_ERROR';
      final retryAfter = responseData['retry_after'] as int?;
      
      debugPrint('❌ TTS API error (${response.statusCode}): $errorMessage');
      
      // Handle rate limiting
      if (response.statusCode == 429 && retryAfter != null && attemptCount < _maxRetryAttempts) {
        debugPrint('⏳ Rate limited, waiting ${retryAfter}s before retry...');
        await Future.delayed(Duration(seconds: retryAfter));
      }
      
      return TTSBackendResult(
        success: false,
        errorMessage: errorMessage,
        errorCode: errorCode,
        httpStatusCode: response.statusCode,
        retryAfter: retryAfter,
      );
      
    } catch (e) {
      debugPrint('❌ Error parsing TTS error response: $e');
      return TTSBackendResult(
        success: false,
        errorMessage: 'HTTP ${response.statusCode}: ${response.reasonPhrase}',
        errorCode: 'HTTP_ERROR',
        httpStatusCode: response.statusCode,
      );
    }
  }
  
  /// Clear cached audio for a specific message
  Future<void> clearCachedAudio(String messageId) async {
    try {
      await _cacheManager.deleteAudioFile(messageId);
      debugPrint('🗑️ Cleared cached audio for message: $messageId');
    } catch (e) {
      debugPrint('❌ Error clearing cached audio: $e');
    }
  }
  
  /// Clear all cached audio files
  Future<void> clearAllCachedAudio() async {
    try {
      await _cacheManager.clearOldCache();
      debugPrint('🗑️ Cleared all cached audio files');
    } catch (e) {
      debugPrint('❌ Error clearing all cached audio: $e');
    }
  }
  
  /// Clear cached audio when new Groq responses arrive
  /// This method should be called when the chat receives new responses
  /// to prevent storage bloat and ensure fresh TTS for new content
  Future<Map<String, dynamic>> onNewGroqResponse() async {
    final startTime = DateTime.now();
    
    try {
      debugPrint('🔄 New Groq response received, clearing old cached audio...');
      
      // Get cache stats before cleanup for reporting
      final cacheStatsBefore = await _cacheManager.getCacheStats();
      
      // Stop any currently playing audio
      if (_isPlaying) {
        debugPrint('🛑 Stopping current audio playback for cache cleanup');
        await stopPlayback();
      }
      
      // Clear all cached audio files to prevent storage bloat
      await _cacheManager.clearOldCache();
      
      // Get cache stats after cleanup for reporting
      final cacheStatsAfter = await _cacheManager.getCacheStats();
      
      final cleanupDuration = DateTime.now().difference(startTime);
      final freedSpace = (cacheStatsBefore['totalSizeBytes'] as int) - (cacheStatsAfter['totalSizeBytes'] as int);
      final freedFiles = (cacheStatsBefore['fileCount'] as int) - (cacheStatsAfter['fileCount'] as int);
      
      debugPrint('✅ Old cached audio cleared for new Groq response');
      debugPrint('📊 Cache cleanup stats: freed $freedFiles files (${freedSpace ~/ 1024}KB) in ${cleanupDuration.inMilliseconds}ms');
      
      return {
        'success': true,
        'cleanupDuration': cleanupDuration.inMilliseconds,
        'freedFiles': freedFiles,
        'freedSpaceBytes': freedSpace,
        'freedSpaceKB': freedSpace ~/ 1024,
        'cacheStatsBefore': cacheStatsBefore,
        'cacheStatsAfter': cacheStatsAfter,
        'timestamp': startTime.toIso8601String(),
      };
      
    } catch (e) {
      final cleanupDuration = DateTime.now().difference(startTime);
      
      debugPrint('❌ Error clearing cache for new Groq response: $e');
      
      return {
        'success': false,
        'error': e.toString(),
        'cleanupDuration': cleanupDuration.inMilliseconds,
        'timestamp': startTime.toIso8601String(),
      };
      
      // Don't throw error - this is a cleanup operation that shouldn't break the flow
    }
  }
  
  /// Get service statistics and status
  Future<Map<String, dynamic>> getServiceStats() async {
    try {
      final cacheStats = await _cacheManager.getCacheStats();
      
      return {
        'isPlaying': _isPlaying,
        'currentlyPlayingMessageId': _currentlyPlayingMessageId,
        'baseUrl': _baseUrl,
        'requestTimeout': _requestTimeout.inSeconds,
        'maxRetryAttempts': _maxRetryAttempts,
        'cacheStats': cacheStats,
      };
      
    } catch (e) {
      debugPrint('❌ Error getting service stats: $e');
      return {
        'error': e.toString(),
      };
    }
  }
  
  /// Dispose resources and cleanup
  void dispose() {
    try {
      // Cancel subscriptions
      _playerStateSubscription?.cancel();
      _positionSubscription?.cancel();
      
      // Dispose audio player
      _audioPlayer.dispose();
      
      // Close HTTP client
      _httpClient.close();
      
      // Reset state
      _isPlaying = false;
      _currentlyPlayingMessageId = null;
      
      debugPrint('🧹 TTS Audio Service disposed');
    } catch (e) {
      debugPrint('❌ Error disposing TTS Audio Service: $e');
    }
  }
}

/// Result of TTS playback operation
class TTSPlaybackResult {
  final bool success;
  final String? errorMessage;
  final String? errorCode;
  final bool fromCache;
  final Uint8List? audioData;
  final String? cachedFilePath;
  final double? durationSeconds;
  final double? processingTime;
  final bool canRetry;
  final String? userFriendlyMessage;
  
  TTSPlaybackResult({
    required this.success,
    this.errorMessage,
    this.errorCode,
    required this.fromCache,
    this.audioData,
    this.cachedFilePath,
    this.durationSeconds,
    this.processingTime,
    this.canRetry = true,
    this.userFriendlyMessage,
  });
  
  /// Get user-friendly error message based on error code
  String get displayMessage {
    if (success) return 'Audio playback successful';
    
    if (userFriendlyMessage != null) return userFriendlyMessage!;
    
    switch (errorCode) {
      case 'NETWORK_ERROR':
        return 'Network connection failed. Please check your internet connection and try again.';
      case 'TIMEOUT':
        return 'Request timed out. Please try again.';
      case 'FILE_NOT_FOUND':
        return 'Audio file not found. Tap to regenerate audio.';
      case 'CACHE_PLAYBACK_ERROR':
        return 'Cached audio playback failed. Tap to try again.';
      case 'BYTES_PLAYBACK_ERROR':
        return 'Audio playback failed. Please try again.';
      case 'PLAYBACK_ERROR':
        return 'Audio playback error. Please try again.';
      case 'MAX_RETRIES_EXCEEDED':
        return 'Service temporarily unavailable. Please try again later.';
      case 'RATE_LIMITED':
        return 'Too many requests. Please wait a moment and try again.';
      case 'INVALID_TEXT':
        return 'Text cannot be converted to speech.';
      case 'SERVICE_UNAVAILABLE':
        return 'Audio service is temporarily unavailable. Please try again later.';
      default:
        return 'Audio playback failed. Please try again.';
    }
  }
  
  @override
  String toString() {
    return 'TTSPlaybackResult(success: $success, fromCache: $fromCache, '
           'errorCode: $errorCode, errorMessage: $errorMessage)';
  }
}

/// Result of backend TTS API request
class TTSBackendResult {
  final bool success;
  final String? errorMessage;
  final String? errorCode;
  final Uint8List? audioData;
  final String? contentType;
  final double? durationSeconds;
  final double? processingTime;
  final String? originalText;
  final String? sanitizedText;
  final int? httpStatusCode;
  final int? retryAfter;
  
  TTSBackendResult({
    required this.success,
    this.errorMessage,
    this.errorCode,
    this.audioData,
    this.contentType,
    this.durationSeconds,
    this.processingTime,
    this.originalText,
    this.sanitizedText,
    this.httpStatusCode,
    this.retryAfter,
  });
  
  @override
  String toString() {
    return 'TTSBackendResult(success: $success, errorCode: $errorCode, '
           'httpStatusCode: $httpStatusCode, audioDataSize: ${audioData?.length})';
  }
}