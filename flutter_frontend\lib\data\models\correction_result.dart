/// Model for German correction results from the AI
class CorrectionResult {
  final String originalText;
  final String correctedText;
  final List<String> suggestions;
  final List<String> explanations;
  final double processingTime;
  
  const CorrectionResult({
    required this.originalText,
    required this.correctedText,
    required this.suggestions,
    required this.explanations,
    required this.processingTime,
  });
  
  /// Create from JSON response
  factory CorrectionResult.fromJson(Map<String, dynamic> json) {
    return CorrectionResult(
      originalText: json['original_text'] ?? '',
      correctedText: json['corrected_text'] ?? '',
      suggestions: List<String>.from(json['suggestions'] ?? []),
      explanations: List<String>.from(json['explanations'] ?? []),
      processingTime: (json['processing_time'] ?? 0.0).toDouble(),
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'original_text': originalText,
      'corrected_text': correctedText,
      'suggestions': suggestions,
      'explanations': explanations,
      'processing_time': processingTime,
    };
  }
  
  /// Check if there are any corrections
  bool get hasCorrections => originalText != correctedText;
  
  /// Check if there are suggestions
  bool get hasSuggestions => suggestions.isNotEmpty;
  
  /// Check if there are explanations
  bool get hasExplanations => explanations.isNotEmpty;
  
  @override
  String toString() {
    return 'CorrectionResult(original: $originalText, corrected: $correctedText, suggestions: ${suggestions.length}, explanations: ${explanations.length})';
  }
}