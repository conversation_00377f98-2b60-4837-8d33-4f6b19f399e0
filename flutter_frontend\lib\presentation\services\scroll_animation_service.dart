import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// Service for managing smooth scrolling animations and behaviors
/// Provides enhanced scrolling functionality for message lists
class ScrollAnimationService {
  final ScrollController _scrollController;
  bool _isAutoScrollEnabled;
  bool _isUserScrolling = false;
  bool _isAnimating = false;
  
  // Animation configurations
  static const Duration _fastScrollDuration = Duration(milliseconds: 300);
  static const Duration _smoothScrollDuration = Duration(milliseconds: 500);
  static const Duration _slowScrollDuration = Duration(milliseconds: 800);
  static const Curve _defaultCurve = Curves.easeInOut;
  static const Curve _fastCurve = Curves.easeOut;
  static const Curve _bounceCurve = Curves.elasticOut;
  
  // Scroll position tracking
  double _lastScrollPosition = 0.0;
  int _messageCount = 0;
  
  ScrollAnimationService({
    required ScrollController scrollController,
    bool autoScrollEnabled = true,
  }) : _scrollController = scrollController,
       _isAutoScrollEnabled = autoScrollEnabled {
    _setupScrollListener();
  }
  
  /// Set up scroll listener to detect user scrolling
  void _setupScrollListener() {
    _scrollController.addListener(() {
      final currentPosition = _scrollController.position.pixels;
      final maxExtent = _scrollController.position.maxScrollExtent;
      
      // Detect if user is manually scrolling
      if (!_isAnimating && (currentPosition - _lastScrollPosition).abs() > 5.0) {
        _isUserScrolling = true;
        
        // Re-enable auto-scroll if user scrolls to bottom
        if (currentPosition >= maxExtent - 50) {
          _isUserScrolling = false;
        }
      }
      
      _lastScrollPosition = currentPosition;
    });
  }
  
  /// Enable or disable auto-scroll
  void setAutoScrollEnabled(bool enabled) {
    _isAutoScrollEnabled = enabled;
    if (!enabled) {
      _isUserScrolling = false;
    }
  }
  
  /// Check if auto-scroll should be performed
  bool get shouldAutoScroll => _isAutoScrollEnabled && !_isUserScrolling;
  
  /// Scroll to bottom with smooth animation
  Future<void> scrollToBottom({
    Duration? duration,
    Curve? curve,
    bool force = false,
  }) async {
    if (!force && (!shouldAutoScroll || !_scrollController.hasClients)) {
      return;
    }
    
    _isAnimating = true;
    
    try {
      await _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: duration ?? _fastScrollDuration,
        curve: curve ?? _fastCurve,
      );
    } catch (e) {
      debugPrint('Error scrolling to bottom: $e');
    } finally {
      _isAnimating = false;
    }
  }
  
  /// Scroll to show specific message at top
  Future<void> scrollToMessage({
    required int messageIndex,
    required int totalMessages,
    double? itemHeight,
    Duration? duration,
    Curve? curve,
    bool force = false,
  }) async {
    if (!force && (!shouldAutoScroll || !_scrollController.hasClients)) {
      return;
    }
    
    _isAnimating = true;
    
    try {
      // Calculate target position
      final estimatedItemHeight = itemHeight ?? _estimateItemHeight();
      final targetOffset = messageIndex * estimatedItemHeight;
      final maxOffset = _scrollController.position.maxScrollExtent;
      final clampedOffset = targetOffset.clamp(0.0, maxOffset);
      
      await _scrollController.animateTo(
        clampedOffset,
        duration: duration ?? _smoothScrollDuration,
        curve: curve ?? _defaultCurve,
      );
    } catch (e) {
      debugPrint('Error scrolling to message: $e');
    } finally {
      _isAnimating = false;
    }
  }
  
  /// Scroll to show user message at top after AI response
  Future<void> scrollToShowUserMessage({
    required List<dynamic> messages,
    Duration? duration,
    Curve? curve,
  }) async {
    if (!shouldAutoScroll || !_scrollController.hasClients || messages.isEmpty) {
      return;
    }
    
    // Find the last user message
    int lastUserMessageIndex = -1;
    for (int i = messages.length - 1; i >= 0; i--) {
      if (messages[i].isUser) {
        lastUserMessageIndex = i;
        break;
      }
    }
    
    if (lastUserMessageIndex != -1) {
      await scrollToMessage(
        messageIndex: lastUserMessageIndex,
        totalMessages: messages.length,
        duration: duration ?? _smoothScrollDuration,
        curve: curve ?? _defaultCurve,
      );
    }
  }
  
  /// Smooth scroll with bounce effect for new messages
  Future<void> scrollToBottomWithBounce() async {
    if (!shouldAutoScroll || !_scrollController.hasClients) {
      return;
    }
    
    _isAnimating = true;
    
    try {
      // First, scroll slightly past the bottom
      final maxExtent = _scrollController.position.maxScrollExtent;
      final overshoot = 50.0;
      
      await _scrollController.animateTo(
        maxExtent + overshoot,
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeOut,
      );
      
      // Then bounce back to the actual bottom
      await _scrollController.animateTo(
        maxExtent,
        duration: const Duration(milliseconds: 300),
        curve: _bounceCurve,
      );
    } catch (e) {
      debugPrint('Error in bounce scroll: $e');
    } finally {
      _isAnimating = false;
    }
  }
  
  /// Scroll with momentum for smooth user experience
  Future<void> scrollWithMomentum({
    required double targetOffset,
    Duration? duration,
    Curve? curve,
  }) async {
    if (!_scrollController.hasClients) return;
    
    _isAnimating = true;
    
    try {
      final currentOffset = _scrollController.offset;
      final distance = (targetOffset - currentOffset).abs();
      
      // Adjust duration based on distance
      Duration adjustedDuration;
      if (distance < 200) {
        adjustedDuration = _fastScrollDuration;
      } else if (distance < 500) {
        adjustedDuration = _smoothScrollDuration;
      } else {
        adjustedDuration = _slowScrollDuration;
      }
      
      await _scrollController.animateTo(
        targetOffset,
        duration: duration ?? adjustedDuration,
        curve: curve ?? _defaultCurve,
      );
    } catch (e) {
      debugPrint('Error in momentum scroll: $e');
    } finally {
      _isAnimating = false;
    }
  }
  
  /// Handle new message arrival with appropriate scroll behavior
  Future<void> handleNewMessage({
    required bool isUserMessage,
    required bool isStreaming,
    required bool isProcessing,
    required List<dynamic> allMessages,
  }) async {
    if (!_scrollController.hasClients) return;
    
    // Wait for the widget to be built
    await Future.delayed(const Duration(milliseconds: 50));
    
    if (isUserMessage && isStreaming) {
      // User is speaking - scroll to bottom to show live transcription
      await scrollToBottom();
    } else if (!isUserMessage && !isProcessing) {
      // AI response completed - scroll to show user message at top
      await Future.delayed(const Duration(milliseconds: 200));
      await scrollToShowUserMessage(messages: allMessages);
    } else if (!isUserMessage && isStreaming) {
      // AI is responding - gentle scroll to bottom
      await scrollToBottom(
        duration: _smoothScrollDuration,
        curve: _defaultCurve,
      );
    }
  }
  
  /// Estimate item height based on current scroll position
  double _estimateItemHeight() {
    if (!_scrollController.hasClients || _messageCount == 0) {
      return 120.0; // Default estimate
    }
    
    final totalHeight = _scrollController.position.maxScrollExtent;
    return totalHeight / _messageCount;
  }
  
  /// Update message count for better height estimation
  void updateMessageCount(int count) {
    _messageCount = count;
  }
  
  /// Check if currently at bottom of scroll
  bool get isAtBottom {
    if (!_scrollController.hasClients) return false;
    
    final position = _scrollController.position;
    return position.pixels >= position.maxScrollExtent - 50;
  }
  
  /// Check if currently at top of scroll
  bool get isAtTop {
    if (!_scrollController.hasClients) return false;
    
    return _scrollController.position.pixels <= 50;
  }
  
  /// Get current scroll percentage (0.0 to 1.0)
  double get scrollPercentage {
    if (!_scrollController.hasClients) return 0.0;
    
    final position = _scrollController.position;
    if (position.maxScrollExtent == 0) return 0.0;
    
    return (position.pixels / position.maxScrollExtent).clamp(0.0, 1.0);
  }
  
  /// Smooth scroll to percentage of content
  Future<void> scrollToPercentage(double percentage) async {
    if (!_scrollController.hasClients) return;
    
    final targetOffset = _scrollController.position.maxScrollExtent * percentage.clamp(0.0, 1.0);
    await scrollWithMomentum(targetOffset: targetOffset);
  }
  
  /// Reset user scrolling state
  void resetUserScrolling() {
    _isUserScrolling = false;
  }
  
  /// Force enable auto-scroll (useful after user actions)
  void forceEnableAutoScroll() {
    _isUserScrolling = false;
    _isAutoScrollEnabled = true;
  }
  
  /// Dispose of the service
  void dispose() {
    // Note: Don't dispose the scroll controller here as it's managed externally
  }
}
