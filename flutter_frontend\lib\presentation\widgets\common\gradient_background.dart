import 'package:flutter/material.dart';
import '../../../core/constants/gradients.dart';

/// Gradient background widget matching the Expo version
class GradientBackground extends StatelessWidget {
  final Widget child;
  final Gradient? gradient;
  
  const GradientBackground({
    super.key,
    required this.child,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: gradient ?? AppGradients.mainBackground,
      ),
      child: child,
    );
  }
}