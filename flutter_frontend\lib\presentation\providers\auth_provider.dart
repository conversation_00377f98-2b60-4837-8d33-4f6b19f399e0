import 'dart:async';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../data/services/auth_service.dart';
import '../../data/repositories/user_repository.dart';
import '../../data/models/user_profile.dart';

/// Authentication provider for managing auth state
class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  final UserRepository _userRepository = UserRepository();
  
  User? _currentUser;
  UserProfile? _userProfile;
  bool _isAuthenticated = false;
  bool _isLoading = false;
  String? _errorMessage;
  StreamSubscription<AuthState>? _authSubscription;

  // Getters
  User? get currentUser => _currentUser;
  UserProfile? get userProfile => _userProfile;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  /// Initialize the auth provider
  Future<void> initialize() async {
    _setLoading(true);
    
    try {
      // Check current auth state
      await checkAuthStatus();
      
      // Listen to auth state changes
      _authSubscription = _authService.authStateChanges.listen(
        _handleAuthStateChange,
        onError: (error) {
          _setError('Authentication error: ${AuthService.getErrorMessage(error)}');
        },
      );
    } catch (e) {
      _setError('Failed to initialize authentication: ${AuthService.getErrorMessage(e)}');
    } finally {
      _setLoading(false);
    }
  }

  /// Check current authentication status
  Future<void> checkAuthStatus() async {
    try {
      final user = _authService.getCurrentUser();
      
      if (user != null) {
        await _setAuthenticatedUser(user);
      } else {
        _clearAuthState();
      }
    } catch (e) {
      _setError('Failed to check auth status: ${AuthService.getErrorMessage(e)}');
      _clearAuthState();
    }
  }

  /// Sign in with email and password
  Future<bool> signInWithEmail(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      // Validate input
      if (!AuthService.isValidEmail(email)) {
        _setError('Please enter a valid email address');
        return false;
      }

      if (!AuthService.isValidPassword(password)) {
        _setError('Password must be at least 8 characters long');
        return false;
      }

      final response = await _authService.signInWithEmail(email, password);
      
      if (response.user != null) {
        await _setAuthenticatedUser(response.user!);
        return true;
      } else {
        _setError('Sign in failed. Please try again.');
        return false;
      }
    } catch (e) {
      _setError(AuthService.getErrorMessage(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign up with email and password
  Future<bool> signUpWithEmail(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      // Validate input
      if (!AuthService.isValidEmail(email)) {
        _setError('Please enter a valid email address');
        return false;
      }

      if (!AuthService.isValidPassword(password)) {
        _setError('Password must be at least 8 characters long');
        return false;
      }

      final response = await _authService.signUpWithEmail(email, password);
      
      if (response.user != null) {
        // Create user profile
        try {
          await _userRepository.createUserProfile(email);
        } catch (profileError) {
          // Log error but don't fail the signup
          print('Warning: Failed to create user profile: $profileError');
        }
        
        // Note: User might need to confirm email before being fully authenticated
        if (response.session != null) {
          await _setAuthenticatedUser(response.user!);
        } else {
          _setError('Please check your email and click the confirmation link to complete signup.');
        }
        return true;
      } else {
        _setError('Sign up failed. Please try again.');
        return false;
      }
    } catch (e) {
      _setError(AuthService.getErrorMessage(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign in with Google OAuth
  Future<bool> signInWithGoogle() async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _authService.signInWithGoogle();
      
      if (success) {
        // The auth state change listener will handle setting the authenticated user
        return true;
      } else {
        _setError('Google sign in was cancelled or failed');
        return false;
      }
    } catch (e) {
      _setError(AuthService.getErrorMessage(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    _setLoading(true);
    
    try {
      await _authService.signOut();
      _clearAuthState();
    } catch (e) {
      _setError('Failed to sign out: ${AuthService.getErrorMessage(e)}');
    } finally {
      _setLoading(false);
    }
  }

  /// Reset password
  Future<bool> resetPassword(String email) async {
    _setLoading(true);
    _clearError();

    try {
      if (!AuthService.isValidEmail(email)) {
        _setError('Please enter a valid email address');
        return false;
      }

      await _authService.resetPassword(email);
      return true;
    } catch (e) {
      _setError(AuthService.getErrorMessage(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Resend email confirmation
  Future<bool> resendEmailConfirmation(String email) async {
    _setLoading(true);
    _clearError();

    try {
      if (!AuthService.isValidEmail(email)) {
        _setError('Please enter a valid email address');
        return false;
      }

      await _authService.resendEmailConfirmation(email);
      return true;
    } catch (e) {
      _setError(AuthService.getErrorMessage(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh user profile
  Future<void> refreshUserProfile() async {
    if (_currentUser?.email == null) return;

    try {
      final profile = await _userRepository.getUserProfile(_currentUser!.email!);
      _userProfile = profile;
      notifyListeners();
    } catch (e) {
      print('Failed to refresh user profile: $e');
    }
  }

  /// Handle auth state changes
  void _handleAuthStateChange(AuthState authState) async {
    switch (authState.event) {
      case AuthChangeEvent.signedIn:
        if (authState.session?.user != null) {
          await _setAuthenticatedUser(authState.session!.user);
        }
        break;
      case AuthChangeEvent.signedOut:
        _clearAuthState();
        break;
      case AuthChangeEvent.tokenRefreshed:
        if (authState.session?.user != null) {
          await _setAuthenticatedUser(authState.session!.user);
        }
        break;
      case AuthChangeEvent.userUpdated:
        if (authState.session?.user != null) {
          _currentUser = authState.session!.user;
          notifyListeners();
        }
        break;
      default:
        break;
    }
  }

  /// Set authenticated user and load profile
  Future<void> _setAuthenticatedUser(User user) async {
    _currentUser = user;
    _isAuthenticated = true;
    
    // Load or create user profile
    if (user.email != null) {
      try {
        UserProfile? profile = await _userRepository.getUserProfile(user.email!);
        
        // Create profile if it doesn't exist (for OAuth users)
        if (profile == null) {
          profile = await _userRepository.createUserProfile(user.email!);
        }
        
        _userProfile = profile;
      } catch (e) {
        print('Failed to load user profile: $e');
        // Continue without profile for now
      }
    }
    
    _clearError();
    notifyListeners();
  }

  /// Clear authentication state
  void _clearAuthState() {
    _currentUser = null;
    _userProfile = null;
    _isAuthenticated = false;
    _clearError();
    notifyListeners();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Check if session is expired
  bool isSessionExpired() {
    return _authService.isSessionExpired();
  }

  /// Get time until session expires
  Duration? getTimeUntilExpiry() {
    return _authService.getTimeUntilExpiry();
  }

  /// Validate password requirements
  Map<String, bool> getPasswordRequirements(String password) {
    return AuthService.getPasswordRequirements(password);
  }

  @override
  void dispose() {
    _authSubscription?.cancel();
    super.dispose();
  }
}