# Requirements Document

## Introduction

The audio icon in AI message bubbles is not positioned correctly and the animations are not working properly. The icon should be positioned higher (not touching the text) and should show proper loading, playing, and error animations with smooth state transitions.

## Requirements

### Requirement 1

**User Story:** As a user, I want the audio icon to be positioned properly above the message text so that it doesn't interfere with reading.

#### Acceptance Criteria

1. WHEN an AI message with extracted sentence is displayed THEN the audio icon SHALL be positioned at least 8 pixels above the message bubble container
2. WHEN the screen size is small THEN the audio icon SHALL be positioned 4 pixels from the right edge
3. WHEN the screen size is normal THEN the audio icon SHALL be positioned 6 pixels from the right edge
4. WHEN the audio icon is displayed THEN it SHALL NOT overlap or touch the message text

### Requirement 2

**User Story:** As a user, I want to see clear visual feedback when I tap the audio icon so I know the system is responding.

#### Acceptance Criteria

1. WHEN the audio icon is in idle state THEN it SHALL display a volume_up_outlined icon with 70% opacity
2. WHEN the audio icon is tapped for the first time THEN it SHALL immediately show a loading animation with a spinning sync icon
3. WHEN TTS audio starts playing THEN the audio icon SHALL show a pulsing animation scaling from 1.0 to 1.25
4. WHEN an error occurs THEN the audio icon SHALL show a shake animation and display a volume_off icon in red
5. WHEN audio playback completes THEN the audio icon SHALL smoothly transition back to idle state

### Requirement 3

**User Story:** As a user, I want the audio icon animations to work consistently every time I interact with it.

#### Acceptance Criteria

1. WHEN the MessageBubble updates the audio icon state THEN the AudioIconWidget SHALL receive the state change and trigger the appropriate animation
2. WHEN the audio icon state changes THEN the animation SHALL start immediately without delay
3. WHEN multiple state changes occur rapidly THEN each animation SHALL complete properly without conflicts
4. WHEN the widget is disposed THEN all animation controllers SHALL be properly cleaned up