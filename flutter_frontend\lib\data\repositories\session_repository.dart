import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/config/supabase_config.dart';
import '../models/session_data.dart';

/// Repository for managing user session data
class SessionRepository {
  static final SupabaseClient _client = SupabaseConfig.client;
  static const String _tableName = 'sessions';

  /// Log a new user session
  Future<SessionData> logSession(SessionData session) async {
    try {
      final response = await _client
          .from(_tableName)
          .insert(session.toInsertJson())
          .select()
          .single();

      return SessionData.fromJson(response);
    } catch (e) {
      throw Exception('Failed to log session: $e');
    }
  }

  /// Get user sessions with optional limit
  Future<List<SessionData>> getUserSessions(String email, {int limit = 50}) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('email', email)
          .order('datetime', ascending: false)
          .limit(limit);

      return response.map<SessionData>((json) => SessionData.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to get user sessions: $e');
    }
  }

  /// Get user sessions within a date range
  Future<List<SessionData>> getUserSessionsInRange(
    String email, {
    required DateTime startDate,
    required DateTime endDate,
    int limit = 100,
  }) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('email', email)
          .gte('datetime', startDate.toIso8601String())
          .lte('datetime', endDate.toIso8601String())
          .order('datetime', ascending: false)
          .limit(limit);

      return response.map<SessionData>((json) => SessionData.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to get user sessions in range: $e');
    }
  }

  /// Get total session count for user
  Future<int> getUserSessionCount(String email) async {
    try {
      final response = await _client
          .from(_tableName)
          .select('session_id', const FetchOptions(count: CountOption.exact))
          .eq('email', email);

      return response.count ?? 0;
    } catch (e) {
      throw Exception('Failed to get user session count: $e');
    }
  }

  /// Get recent sessions (last 24 hours)
  Future<List<SessionData>> getRecentSessions(String email) async {
    try {
      final yesterday = DateTime.now().subtract(const Duration(days: 1));
      
      final response = await _client
          .from(_tableName)
          .select()
          .eq('email', email)
          .gte('datetime', yesterday.toIso8601String())
          .order('datetime', ascending: false);

      return response.map<SessionData>((json) => SessionData.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to get recent sessions: $e');
    }
  }

  /// Get session statistics for user
  Future<Map<String, dynamic>> getSessionStats(String email) async {
    try {
      final sessions = await getUserSessions(email, limit: 1000);
      
      if (sessions.isEmpty) {
        return {
          'total_sessions': 0,
          'total_message_length': 0,
          'total_response_length': 0,
          'average_message_length': 0.0,
          'average_response_length': 0.0,
          'first_session': null,
          'last_session': null,
          'sessions_today': 0,
          'sessions_this_week': 0,
          'sessions_this_month': 0,
        };
      }

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final weekAgo = now.subtract(const Duration(days: 7));
      final monthAgo = DateTime(now.year, now.month - 1, now.day);

      final totalMessageLength = sessions.fold<int>(0, (sum, session) => sum + session.messageLength);
      final totalResponseLength = sessions.fold<int>(0, (sum, session) => sum + session.responseLength);
      
      final sessionsToday = sessions.where((s) => s.datetime.isAfter(today)).length;
      final sessionsThisWeek = sessions.where((s) => s.datetime.isAfter(weekAgo)).length;
      final sessionsThisMonth = sessions.where((s) => s.datetime.isAfter(monthAgo)).length;

      return {
        'total_sessions': sessions.length,
        'total_message_length': totalMessageLength,
        'total_response_length': totalResponseLength,
        'average_message_length': sessions.isNotEmpty ? totalMessageLength / sessions.length : 0.0,
        'average_response_length': sessions.isNotEmpty ? totalResponseLength / sessions.length : 0.0,
        'first_session': sessions.last.datetime,
        'last_session': sessions.first.datetime,
        'sessions_today': sessionsToday,
        'sessions_this_week': sessionsThisWeek,
        'sessions_this_month': sessionsThisMonth,
      };
    } catch (e) {
      throw Exception('Failed to get session stats: $e');
    }
  }

  /// Delete user sessions (for GDPR compliance)
  Future<void> deleteUserSessions(String email) async {
    try {
      await _client
          .from(_tableName)
          .delete()
          .eq('email', email);
    } catch (e) {
      throw Exception('Failed to delete user sessions: $e');
    }
  }

  /// Get session by ID
  Future<SessionData?> getSessionById(int sessionId) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('session_id', sessionId)
          .maybeSingle();

      if (response == null) {
        return null;
      }

      return SessionData.fromJson(response);
    } catch (e) {
      throw Exception('Failed to get session by ID: $e');
    }
  }

  /// Search sessions by message content
  Future<List<SessionData>> searchSessions(String email, String searchTerm) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('email', email)
          .ilike('message', '%$searchTerm%')
          .order('datetime', ascending: false)
          .limit(50);

      return response.map<SessionData>((json) => SessionData.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to search sessions: $e');
    }
  }

  /// Get daily session counts for the last 30 days
  Future<Map<String, int>> getDailySessionCounts(String email) async {
    try {
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      
      final sessions = await getUserSessionsInRange(
        email,
        startDate: thirtyDaysAgo,
        endDate: DateTime.now(),
        limit: 1000,
      );

      final dailyCounts = <String, int>{};
      
      for (final session in sessions) {
        final dateKey = '${session.datetime.year}-${session.datetime.month.toString().padLeft(2, '0')}-${session.datetime.day.toString().padLeft(2, '0')}';
        dailyCounts[dateKey] = (dailyCounts[dateKey] ?? 0) + 1;
      }

      return dailyCounts;
    } catch (e) {
      throw Exception('Failed to get daily session counts: $e');
    }
  }
}