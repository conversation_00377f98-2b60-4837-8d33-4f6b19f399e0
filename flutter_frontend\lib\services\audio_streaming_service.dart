import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

/// Models matching the backend/Expo implementation
class CorrectionResult {
  final String originalText;
  final String correctedText;
  final List<String> suggestions;
  final List<String> explanations;
  final double processingTime;

  CorrectionResult({
    required this.originalText,
    required this.correctedText,
    required this.suggestions,
    required this.explanations,
    required this.processingTime,
  });

  factory CorrectionResult.fromJson(Map<String, dynamic> json) {
    return CorrectionResult(
      originalText: json['original_text'] ?? '',
      correctedText: json['corrected_text'] ?? '',
      suggestions: List<String>.from(json['suggestions'] ?? []),
      explanations: List<String>.from(json['explanations'] ?? []),
      processingTime: (json['processing_time'] ?? 0).toDouble(),
    );
  }
}

class GroqResponse {
  final String originalText;
  final String responseText;
  final double processingTime;

  GroqResponse({
    required this.originalText,
    required this.responseText,
    required this.processingTime,
  });

  factory GroqResponse.fromJson(Map<String, dynamic> json) {
    return GroqResponse(
      originalText: json['original_text'] ?? '',
      responseText: json['response_text'] ?? '',
      processingTime: (json['processing_time'] ?? 0).toDouble(),
    );
  }
}

enum StreamingState {
  disconnected,
  connecting,
  connected,
  streaming,
  processing,
  error,
}

/// Audio Streaming Service for DeutschKorrekt
/// Handles WebSocket connection to backend and audio recording
class AudioStreamingService {
  static const String defaultBackendUrl = 'wss://deutschkorrekt-backend-645996191396.europe-west3.run.app/stt';
  static const int connectionTimeout = 10000;
  static const int maxRetries = 3;
  static const int retryDelay = 1000;

  // Audio format constants (matching Deepgram requirements)
  static const int sampleRate = 16000;
  static const int channels = 1;

  final String backendUrl;
  
  // Callbacks
  final Function(String)? onPartialTranscript;
  final Function(String)? onFinalTranscript;
  final Function(CorrectionResult)? onCorrection;
  final Function(GroqResponse)? onGroqResponse;
  final Function(String)? onError;
  final Function()? onTimeout;
  final Function(String)? onInfo;
  final Function(StreamingState)? onStateChange;

  // State
  StreamingState _state = StreamingState.disconnected;
  String _partialText = '';
  String _finalText = '';
  int _retryCount = 0;

  // State stream controller for broadcasting state changes
  final StreamController<StreamingState> _stateController = StreamController<StreamingState>.broadcast();

  // Services
  WebSocketChannel? _webSocketChannel;
  FlutterSoundRecorder? _audioRecorder;
  StreamController<Uint8List>? _audioStreamController;
  StreamSubscription<Uint8List>? _audioSubscription;

  // Getters
  StreamingState get state => _state;
  Stream<StreamingState> get stateStream => _stateController.stream;
  String get partialText => _partialText;
  String get finalText => _finalText;
  int get retryCount => _retryCount;
  bool get isConnected => _state == StreamingState.connected || _state == StreamingState.streaming;
  bool get isStreaming => _state == StreamingState.streaming;
  bool get isProcessing => _state == StreamingState.processing;
  bool get isConnecting => _state == StreamingState.connecting;

  AudioStreamingService({
    this.backendUrl = defaultBackendUrl,
    this.onPartialTranscript,
    this.onFinalTranscript,
    this.onCorrection,
    this.onGroqResponse,
    this.onError,
    this.onTimeout,
    this.onInfo,
    this.onStateChange,
  }) {
    _audioRecorder = FlutterSoundRecorder();
  }

  /// Connect to the WebSocket backend
  Future<void> connect() async {
    if (_state == StreamingState.connecting || _state == StreamingState.connected) {
      return;
    }

    _setState(StreamingState.connecting);
    
    try {
      debugPrint('🔌 Connecting to WebSocket: $backendUrl');
      
      _webSocketChannel = WebSocketChannel.connect(Uri.parse(backendUrl));
      
      // Listen for messages
      _webSocketChannel!.stream.listen(
        _handleWebSocketMessage,
        onError: _handleWebSocketError,
        onDone: _handleWebSocketClosed,
      );

      // Wait for connection to be established
      await Future.delayed(const Duration(milliseconds: 500));
      
      _setState(StreamingState.connected);
      _retryCount = 0;
      debugPrint('✅ WebSocket connected successfully');
      
    } catch (e) {
      debugPrint('❌ WebSocket connection failed: $e');
      _setState(StreamingState.error);
      onError?.call('Failed to connect: $e');
      
      if (_retryCount < maxRetries) {
        _retryCount++;
        debugPrint('🔄 Retrying connection in ${retryDelay}ms (attempt $_retryCount/$maxRetries)');
        await Future.delayed(Duration(milliseconds: retryDelay));
        await connect();
      }
    }
  }

  /// Start audio streaming
  Future<void> startStreaming() async {
    debugPrint('🔄 startStreaming() called - current state: $_state');

    // Prevent multiple simultaneous start attempts
    if (_state == StreamingState.streaming) {
      debugPrint('⚠️ Already streaming, ignoring start request');
      return;
    }

    debugPrint('🔄 Checking connection state...');
    if (_state != StreamingState.connected) {
      debugPrint('🔄 Not connected, calling connect()...');
      await connect();
    }

    debugPrint('🔄 Connection check complete - state: $_state');
    if (_state != StreamingState.connected) {
      debugPrint('❌ Still not connected after connect() call');
      throw Exception('Not connected to backend');
    }

    debugPrint('🔄 Requesting microphone permission...');

    try {
      // Request microphone permission with timeout
      final permission = await Permission.microphone.request().timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          debugPrint('⏰ Microphone permission request timed out');
          return PermissionStatus.denied;
        },
      );

      debugPrint('🔄 Microphone permission result: $permission');

      if (permission != PermissionStatus.granted) {
        debugPrint('❌ Microphone permission denied or timed out');
        throw Exception('Microphone permission denied: $permission');
      }

      debugPrint('✅ Microphone permission granted, starting audio recording...');

    } catch (e) {
      debugPrint('❌ Error requesting microphone permission: $e');
      throw Exception('Failed to get microphone permission: $e');
    }

    try {
      debugPrint('🎤 Starting audio recording...');

      // Initialize recorder if needed (check if it's stopped)
      if (_audioRecorder!.isStopped) {
        debugPrint('🔧 Opening audio recorder...');
        await _audioRecorder!.openRecorder();
        debugPrint('✅ Audio recorder opened');
      }

      // Create stream controller for audio data
      _audioStreamController = StreamController<Uint8List>();
      debugPrint('✅ Audio stream controller created');

      _setState(StreamingState.streaming);

      // Listen to audio stream and send to backend
      _audioSubscription = _audioStreamController!.stream.listen(
        (audioData) {
          if (_webSocketChannel != null && _state == StreamingState.streaming) {
            _webSocketChannel!.sink.add(audioData);
          }
        },
        onError: (error) {
          debugPrint('❌ Audio recording error: $error');
          onError?.call('Audio recording error: $error');
          stopStreaming();
        },
      );
      debugPrint('✅ Audio stream listener set up');

      // Start recording to stream with Deepgram-compatible settings
      debugPrint('🎙️ Starting recorder with settings: codec=pcm16, sampleRate=$sampleRate, channels=$channels');
      await _audioRecorder!.startRecorder(
        toStream: _audioStreamController!.sink,
        codec: Codec.pcm16,
        sampleRate: sampleRate,
        numChannels: channels,
      );

      debugPrint('✅ Audio streaming started successfully');

    } catch (e) {
      debugPrint('❌ Failed to start audio streaming: $e');
      debugPrint('❌ Error type: ${e.runtimeType}');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      onError?.call('Failed to start audio streaming: $e');
      _setState(StreamingState.connected);
      rethrow; // Re-throw so the UI can handle it
    }
  }

  /// Manually stop streaming and process current transcript
  Future<void> manualStop() async {
    if (_state != StreamingState.streaming) return;

    try {
      debugPrint('🛑 Manual stop requested...');

      // Immediately change state to processing so button shows German flag
      _setState(StreamingState.processing);

      // Send manual stop command to backend
      if (_webSocketChannel != null) {
        _webSocketChannel!.sink.add('manual_stop');
        debugPrint('📤 Sent manual stop command to backend');
      }

      // Stop audio recording
      await _audioSubscription?.cancel();
      _audioSubscription = null;

      await _audioRecorder?.stopRecorder();

      await _audioStreamController?.close();
      _audioStreamController = null;

      debugPrint('✅ Manual stop completed - waiting for Groq response');

    } catch (e) {
      debugPrint('❌ Error in manual stop: $e');
      _setState(StreamingState.connected);
    }
  }

  /// Stop audio streaming (automatic)
  Future<void> stopStreaming() async {
    if (_state != StreamingState.streaming) return;

    try {
      debugPrint('🛑 Stopping audio streaming...');

      await _audioSubscription?.cancel();
      _audioSubscription = null;

      await _audioRecorder?.stopRecorder();

      await _audioStreamController?.close();
      _audioStreamController = null;

      _setState(StreamingState.connected);
      debugPrint('✅ Audio streaming stopped');

    } catch (e) {
      debugPrint('❌ Error stopping audio streaming: $e');
    }
  }

  /// Disconnect from backend
  Future<void> disconnect() async {
    try {
      debugPrint('🔌 Disconnecting from WebSocket...');

      await stopStreaming();

      await _webSocketChannel?.sink.close();
      _webSocketChannel = null;

      await _audioStreamController?.close();
      _audioStreamController = null;

      _setState(StreamingState.disconnected);
      _partialText = '';
      _finalText = '';

      debugPrint('✅ WebSocket disconnected');

    } catch (e) {
      debugPrint('❌ Error disconnecting: $e');
    }
  }

  void _setState(StreamingState newState) {
    _state = newState;
    debugPrint('🔄 State changed to: $newState');
    _stateController.add(newState);
    onStateChange?.call(newState);
  }

  void _handleWebSocketMessage(dynamic message) {
    try {
      final data = jsonDecode(message);
      debugPrint('📨 Received message: ${data['message_type']}');
      
      switch (data['message_type']) {
        case 'partial_transcript':
          _partialText = data['data']['text'] ?? '';
          onPartialTranscript?.call(_partialText);
          break;
          
        case 'final_transcript':
          _finalText = data['data']['text'] ?? '';
          _partialText = '';
          _setState(StreamingState.processing);
          onFinalTranscript?.call(_finalText);
          
          // Auto-stop if Deepgram ended the session
          if (data['data']['auto_ended'] == true) {
            debugPrint('🛑 Auto-stopping due to Deepgram endpointing');
            stopStreaming();
            Future.delayed(const Duration(seconds: 1), disconnect);
          }
          break;
          
        case 'correction':
          _setState(StreamingState.connected);
          final correction = CorrectionResult.fromJson(data['data']);
          onCorrection?.call(correction);
          break;
          
        case 'groq_response':
          _setState(StreamingState.connected);
          final response = GroqResponse.fromJson(data['data']);
          onGroqResponse?.call(response);
          disconnect(); // Clean disconnect after response
          break;
          
        case 'processing':
          debugPrint('⚡ Processing with Groq...');
          _setState(StreamingState.processing);
          break;
          
        case 'error':
          onError?.call(data['data']['message'] ?? 'Unknown error');
          break;

        case 'info':
          debugPrint('ℹ️ Deepgram info: ${data['data'] ?? 'No data'}');
          break;

        case 'session_started':
          debugPrint('🎬 Deepgram session started');
          break;

        case 'timeout':
          debugPrint('⏰ Session reached time limit - processing with Groq');
          // Don't call onError - this is a normal timeout, not an error
          // The manual_stop_session will handle Groq processing
          onTimeout?.call();
          break;

        default:
          debugPrint('❓ Unknown message type: ${data['message_type']}');
      }
      
    } catch (e) {
      debugPrint('❌ Error parsing WebSocket message: $e');
      onError?.call('Error parsing message: $e');
    }
  }

  void _handleWebSocketError(error) {
    debugPrint('❌ WebSocket error: $error');
    _setState(StreamingState.error);
    onError?.call('WebSocket error: $error');
  }

  void _handleWebSocketClosed() {
    debugPrint('🔌 WebSocket connection closed');
    _setState(StreamingState.disconnected);
  }

  /// Dispose resources
  void dispose() {
    disconnect();
    _audioRecorder?.closeRecorder();
    _stateController.close();
  }
}
