import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/gradients.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/correction_result.dart';
import '../common/formatted_text.dart';

/// Standalone widget for displaying German correction results
class CorrectionResultWidget extends StatelessWidget {
  final CorrectionResult correctionResult;
  final bool showAgentBadge;
  final bool showProcessingTime;
  final EdgeInsets? padding;
  final VoidCallback? onTap;
  
  const CorrectionResultWidget({
    super.key,
    required this.correctionResult,
    this.showAgentBadge = true,
    this.showProcessingTime = false,
    this.padding,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: padding ?? const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: AppGradients.correctionContainerGradient,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.slate600.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowLight,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Agent badge
            if (showAgentBadge)
              _buildAgentBadge(),
            
            if (showAgentBadge)
              const SizedBox(height: 12),
            
            // Original vs Corrected text
            if (correctionResult.hasCorrections)
              _buildCorrectionComparison(),
            
            // Suggestions
            if (correctionResult.hasSuggestions) ...[
              if (correctionResult.hasCorrections)
                const SizedBox(height: 12),
              _buildSuggestions(),
            ],
            
            // Explanations
            if (correctionResult.hasExplanations) ...[
              if (correctionResult.hasCorrections || correctionResult.hasSuggestions)
                const SizedBox(height: 12),
              _buildExplanations(),
            ],
            
            // Processing time
            if (showProcessingTime) ...[
              const SizedBox(height: 12),
              _buildProcessingTime(),
            ],
          ],
        ),
      ),
    );
  }
  
  /// Build agent badge with gradient styling
  Widget _buildAgentBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        gradient: AppGradients.agentBadgeGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.psychology,
            size: 16,
            color: AppColors.lightText,
          ),
          const SizedBox(width: 6),
          Text(
            'German Correction Agent',
            style: AppTextStyles.agentBadge,
          ),
        ],
      ),
    );
  }
  
  /// Build original vs corrected text comparison with color coding
  Widget _buildCorrectionComparison() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.slate800.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.slate600.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Row(
            children: [
              Icon(
                Icons.compare_arrows,
                size: 16,
                color: AppColors.lightText,
              ),
              const SizedBox(width: 6),
              Text(
                'Correction',
                style: AppTextStyles.agentBadge.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Original text (red)
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.errorRed.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Original',
                  style: AppTextStyles.correctionOriginal.copyWith(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: FormattedTextExtensions.correctionOriginal(
                  correctionResult.originalText,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Corrected text (green)
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.successGreen.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Corrected',
                  style: AppTextStyles.correctionCorrected.copyWith(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: FormattedTextExtensions.correctionCorrected(
                  correctionResult.correctedText,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  /// Build suggestions with blue styling and bullet points
  Widget _buildSuggestions() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.infoBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.infoBlue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: 16,
                color: AppColors.infoBlue,
              ),
              const SizedBox(width: 6),
              Text(
                'Suggestions',
                style: AppTextStyles.suggestionText.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Suggestions list
          ...correctionResult.suggestions.asMap().entries.map((entry) {
            final index = entry.key;
            final suggestion = entry.value;
            
            return Padding(
              padding: EdgeInsets.only(
                bottom: index < correctionResult.suggestions.length - 1 ? 6 : 0,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 2),
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.infoBlue,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: FormattedTextExtensions.suggestion(suggestion),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
  
  /// Build explanations with yellow styling
  Widget _buildExplanations() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.warningYellow.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.warningYellow.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: AppColors.warningYellow,
              ),
              const SizedBox(width: 6),
              Text(
                'Explanations',
                style: AppTextStyles.explanationText.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Explanations list
          ...correctionResult.explanations.asMap().entries.map((entry) {
            final index = entry.key;
            final explanation = entry.value;
            
            return Padding(
              padding: EdgeInsets.only(
                bottom: index < correctionResult.explanations.length - 1 ? 6 : 0,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 2),
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.warningYellow,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: FormattedTextExtensions.explanation(explanation),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
  
  /// Build processing time display
  Widget _buildProcessingTime() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.slate700.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.timer,
            size: 12,
            color: AppColors.lightText.withOpacity(0.7),
          ),
          const SizedBox(width: 4),
          Text(
            'Processed in ${correctionResult.processingTime.toStringAsFixed(2)}s',
            style: AppTextStyles.captionText.copyWith(
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }
}

/// Compact correction result widget for smaller spaces
class CompactCorrectionResultWidget extends StatelessWidget {
  final CorrectionResult correctionResult;
  final VoidCallback? onTap;
  
  const CompactCorrectionResultWidget({
    super.key,
    required this.correctionResult,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          gradient: AppGradients.correctionContainerGradient,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppColors.slate600.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Compact agent badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
              decoration: BoxDecoration(
                gradient: AppGradients.agentBadgeGradient,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                'Correction',
                style: AppTextStyles.agentBadge.copyWith(fontSize: 10),
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Compact correction display
            if (correctionResult.hasCorrections) ...[
              FormattedTextExtensions.correctionOriginal(
                correctionResult.originalText,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              FormattedTextExtensions.correctionCorrected(
                correctionResult.correctedText,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            
            // Summary of suggestions and explanations
            if (correctionResult.hasSuggestions || correctionResult.hasExplanations) ...[
              const SizedBox(height: 6),
              Text(
                '${correctionResult.suggestions.length} suggestions, ${correctionResult.explanations.length} explanations',
                style: AppTextStyles.captionText.copyWith(fontSize: 10),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Expandable correction result widget
class ExpandableCorrectionResultWidget extends StatefulWidget {
  final CorrectionResult correctionResult;
  final bool initiallyExpanded;
  
  const ExpandableCorrectionResultWidget({
    super.key,
    required this.correctionResult,
    this.initiallyExpanded = false,
  });

  @override
  State<ExpandableCorrectionResultWidget> createState() => 
      _ExpandableCorrectionResultWidgetState();
}

class _ExpandableCorrectionResultWidgetState 
    extends State<ExpandableCorrectionResultWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _expandAnimation;
  bool _isExpanded = false;
  
  @override
  void initState() {
    super.initState();
    
    _isExpanded = widget.initiallyExpanded;
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _expandAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
    
    if (_isExpanded) {
      _controller.value = 1.0;
    }
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: AppGradients.correctionContainerGradient,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.slate600.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Header (always visible)
          GestureDetector(
            onTap: _toggleExpanded,
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Agent badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      gradient: AppGradients.agentBadgeGradient,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'German Correction Agent',
                      style: AppTextStyles.agentBadge,
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // Expand/collapse icon
                  AnimatedRotation(
                    turns: _isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: AppColors.lightText,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Expandable content
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: Container(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: CorrectionResultWidget(
                correctionResult: widget.correctionResult,
                showAgentBadge: false,
                showProcessingTime: true,
                padding: EdgeInsets.zero,
              ),
            ),
          ),
        ],
      ),
    );
  }
}