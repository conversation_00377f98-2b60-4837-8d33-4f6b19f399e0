# Design Document

## Overview

This design implements a professional authentication and database system using Supabase as the backend service for the German language learning app. The system integrates seamlessly with the existing Flutter frontend architecture, adding user authentication, profile management, session tracking, and a credit-based usage system. The design leverages Supabase's built-in authentication, database, and security features while maintaining the current provider-based state management pattern and focusing on simplicity and reliability.

## Architecture

### Architecture Patterns

This design follows modern development best practices:

- **Clean Architecture**: Clear separation of concerns with domain, data, and presentation layers
- **Repository Pattern**: Abstracted data access with interface-based design
- **Provider Pattern**: Reactive state management with dependency injection
- **Error Handling**: Graceful error handling with user-friendly messaging
- **Security**: Leveraging Supabase's built-in security features

### High-Level Architecture

```mermaid
graph TB
    subgraph "Flutter Frontend"
        A[App Initializer] --> B[Auth Provider]
        B --> C[Profile Provider]
        B --> D[Chat Provider]
        C --> E[Settings Provider]
        F[Auth Screen] --> B
        G[Chat Screen] --> D
        H[Profile Screen] --> C
    end
    
    subgraph "Supabase Backend"
        I[Supabase Auth] --> J[Users Table]
        I --> K[Sessions Table]
        L[Supabase Client] --> I
        L --> J
        L --> K
    end
    
    subgraph "Existing Backend"
        M[FastAPI Backend] --> N[STT/TTS Services]
        M --> O[Language Service]
    end
    
    B --> L
    C --> L
    D --> M
    D --> L
```

### Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant A as Auth Screen
    participant AP as Auth Provider
    participant S as Supabase
    participant PP as Profile Provider
    participant CS as Chat Screen

    U->>A: Open App
    A->>AP: Check Auth Status
    AP->>S: Check Session
    
    alt User Not Authenticated
        S-->>AP: No Session
        AP-->>A: Show Auth Screen
        U->>A: Login/Signup
        A->>AP: Authenticate
        AP->>S: Auth Request
        S-->>AP: Auth Success
        AP->>PP: Create/Load Profile
        PP->>S: Query Users Table
        S-->>PP: Profile Data
        AP-->>A: Navigate to Chat
        A->>CS: Show Chat Screen
    else User Authenticated
        S-->>AP: Valid Session
        AP->>PP: Load Profile
        PP->>S: Query Users Table
        S-->>PP: Profile Data
        AP-->>A: Navigate to Chat
        A->>CS: Show Chat Screen
    end
```

## Components and Interfaces

### 1. Authentication Components

#### AuthProvider
```dart
class AuthProvider extends ChangeNotifier {
  User? _currentUser;
  bool _isAuthenticated = false;
  bool _isLoading = false;
  String? _errorMessage;
  
  // Authentication methods
  Future<bool> signInWithEmail(String email, String password);
  Future<bool> signUpWithEmail(String email, String password);
  Future<bool> signInWithGoogle();
  Future<void> signOut();
  Future<void> checkAuthStatus();
  
  // Getters
  User? get currentUser => _currentUser;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
}
```

#### AuthScreen
- Login/Signup form with email/password fields
- Google OAuth button
- Form validation and error handling
- Loading states and user feedback

#### AuthService
```dart
class AuthService {
  static final SupabaseClient _supabase = Supabase.instance.client;
  
  Future<AuthResponse> signInWithEmail(String email, String password);
  Future<AuthResponse> signUpWithEmail(String email, String password);
  Future<AuthResponse> signInWithGoogle();
  Future<void> signOut();
  User? getCurrentUser();
}
```

### 2. Database Components

#### UserRepository
```dart
class UserRepository {
  static final SupabaseClient _supabase = Supabase.instance.client;
  
  Future<UserProfile?> getUserProfile(String email);
  Future<UserProfile> createUserProfile(String email);
  Future<void> updateUserProfile(UserProfile profile);
  Future<void> updateCredits(String email, int newCredits);
}
```

#### SessionRepository
```dart
class SessionRepository {
  static final SupabaseClient _supabase = Supabase.instance.client;
  
  Future<void> logSession(SessionData session);
  Future<List<SessionData>> getUserSessions(String email, {int limit = 50});
  Future<int> getUserSessionCount(String email);
}
```

### 3. Profile Management Components

#### Enhanced ProfileProvider
```dart
class ProfileProvider extends ChangeNotifier {
  UserProfile? _userProfile;
  bool _isLoading = false;
  String? _errorMessage;
  
  // Existing methods enhanced with database integration
  Future<void> loadUserProfile(String email);
  Future<void> updateProfile(UserProfile profile);
  Future<bool> consumeCredit();
  Future<void> refreshCreditsIfNeeded();
  
  // Credit management
  bool get hasCreditsAvailable => _userProfile?.currentCredits ?? 0 > 0;
  DateTime? get nextRefreshDate;
}
```

#### ProfileScreen Enhancement
- Display user email, plan details, and credit information
- Show next refresh date calculation
- Add logout functionality
- Display usage statistics

### 4. Session Tracking Components

#### SessionTracker
```dart
class SessionTracker {
  static final SessionRepository _repository = SessionRepository();
  
  Future<void> logUserSession({
    required String email,
    required String message,
    required String response,
  });
  
  Future<bool> validateUserCanMakeRequest(String email);
}
```

## Data Models

### UserProfile Model
```dart
class UserProfile {
  final String email;
  final String plan;
  final DateTime dateJoined;
  final DateTime datePlan;
  final int maxCredits;
  final int currentCredits;
  
  UserProfile({
    required this.email,
    required this.plan,
    required this.dateJoined,
    required this.datePlan,
    required this.maxCredits,
    required this.currentCredits,
  });
  
  // Utility methods
  DateTime get nextRefreshDate;
  bool get needsRefresh;
  bool get hasCreditsAvailable;
  
  // JSON serialization
  factory UserProfile.fromJson(Map<String, dynamic> json);
  Map<String, dynamic> toJson();
}
```

### SessionData Model
```dart
class SessionData {
  final int? sessionId;
  final String email;
  final String message;
  final String response;
  final DateTime datetime;
  
  SessionData({
    this.sessionId,
    required this.email,
    required this.message,
    required this.response,
    required this.datetime,
  });
  
  factory SessionData.fromJson(Map<String, dynamic> json);
  Map<String, dynamic> toJson();
}
```

### Database Schema

#### Users Table
```sql
CREATE TABLE users (
  email TEXT PRIMARY KEY,
  plan TEXT NOT NULL DEFAULT 'Trial',
  date_joined TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  date_plan TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  max_credits INTEGER NOT NULL DEFAULT 20,
  current_credits INTEGER NOT NULL DEFAULT 20,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Sessions Table
```sql
CREATE TABLE sessions (
  session_id SERIAL PRIMARY KEY,
  email TEXT NOT NULL REFERENCES users(email) ON DELETE CASCADE,
  message TEXT NOT NULL,
  response TEXT NOT NULL,
  datetime TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_sessions_email ON sessions(email);
CREATE INDEX idx_sessions_datetime ON sessions(datetime);
```

## Error Handling

### Authentication Errors
- **Invalid Credentials**: Display user-friendly error messages
- **Network Errors**: Retry mechanism with exponential backoff
- **Email Verification**: Guide users through verification process
- **OAuth Errors**: Handle Google OAuth failures gracefully

### Database Errors
- **Connection Issues**: Offline mode with local caching
- **Rate Limiting**: Implement request queuing and user notification
- **Data Validation**: Client-side and server-side validation
- **Constraint Violations**: Handle duplicate entries and foreign key errors

### Credit System Errors
- **Insufficient Credits**: Block requests and show upgrade options
- **Refresh Calculation**: Handle edge cases in date calculations
- **Concurrent Updates**: Use database transactions for credit operations

## Testing Strategy

### Unit Tests
- **AuthProvider**: Test all authentication methods and state management
- **UserRepository**: Test CRUD operations with mock Supabase client
- **SessionRepository**: Test session logging and retrieval
- **ProfileProvider**: Test credit management and profile updates
- **Data Models**: Test serialization, validation, and utility methods

### Integration Tests
- **Authentication Flow**: End-to-end login/signup testing
- **Database Operations**: Test with real Supabase instance
- **Credit System**: Test credit consumption and refresh logic
- **Session Tracking**: Test session logging during chat interactions

### Widget Tests
- **AuthScreen**: Test form validation and user interactions
- **ProfileScreen**: Test profile display and credit information
- **Chat Integration**: Test credit validation before requests

### Performance Tests
- **Database Queries**: Optimize query performance with proper indexing
- **Authentication Speed**: Measure login/signup response times
- **Credit Checks**: Ensure credit validation doesn't slow down requests

## Security Considerations

### Authentication Security
- **Password Requirements**: Enforce strong password policies (minimum 8 characters)
- **Session Management**: Secure token storage and automatic refresh
- **OAuth Security**: Validate OAuth tokens and handle scope permissions
- **Brute Force Protection**: Basic rate limiting on auth endpoints

### Database Security
- **Row Level Security**: Use Supabase's built-in RLS policies for user data isolation
- **API Key Management**: Secure storage of Supabase credentials
- **Data Encryption**: Leverage Supabase's encryption at rest and TLS in transit
- **Access Control**: Ensure users can only access their own data

### Privacy Protection
- **Data Minimization**: Only collect necessary user information
- **Error Information**: Avoid exposing sensitive data in error messages
- **Session Privacy**: Ensure user sessions are not accessible by others

## Integration Points

### Existing Chat System
- **Credit Validation**: Check credits before processing chat requests
- **Session Logging**: Log all chat interactions to Sessions table
- **User Context**: Pass authenticated user context to chat operations
- **Error Handling**: Handle credit exhaustion during chat sessions

### Backend API Integration
- **Authentication Headers**: Pass user tokens to backend services
- **User Identification**: Include user email in API requests
- **Rate Limiting**: Coordinate rate limiting between frontend and backend
- **Session Correlation**: Link frontend sessions with backend processing

### State Management Integration
- **Provider Dependencies**: Ensure proper initialization order
- **State Synchronization**: Keep auth state synchronized across providers
- **Error Propagation**: Handle authentication errors in dependent providers
- **Cleanup**: Proper cleanup when user logs out

## Deployment Considerations

### Environment Configuration
- **Supabase URLs**: Different instances for development/production
- **API Keys**: Secure management of environment-specific keys
- **OAuth Configuration**: Platform-specific OAuth client IDs
- **Database Migrations**: Automated schema deployment

### Performance Optimization
- **Caching**: Cache user profiles and session data locally
- **Lazy Loading**: Load user data only when needed
- **Background Sync**: Sync data in background for better UX
- **Database Optimization**: Use proper indexing for query performance

### Monitoring and Analytics
- **Authentication Metrics**: Track login success rates and failures
- **Usage Analytics**: Monitor credit consumption patterns
- **Error Tracking**: Log errors for debugging and monitoring
- **Performance Monitoring**: Track database query performance