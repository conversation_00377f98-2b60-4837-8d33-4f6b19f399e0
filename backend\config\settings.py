"""
Configuration settings for the Deutschkorrekt backend application.
Handles environment variables and Google Secret Manager integration.
"""

import os
from typing import Op<PERSON>
from pydantic_settings import BaseSettings
from dotenv import load_dotenv
from google.cloud import secretmanager

# Load environment variables from .env file if it exists
load_dotenv()

class Settings(BaseSettings):
    """Application settings with environment variable support and Secret Manager integration."""
    
    # Basic app settings
    app_name: str = "deutschkorrekt-backend"
    debug: bool = False
    log_level: str = "INFO"
    max_recording_duration: int = 30
    
    # API Keys
    deepgram_api_key: str = ""
    groq_api_key: str = ""
    
    # Google Cloud TTS settings
    tts_voice_name: str = "de-DE-Chirp3-HD-Aoede"
    tts_speech_speed: float = 0.9
    tts_audio_format: str = "MP3"
    tts_sample_rate: int = 24000
    tts_max_text_length: int = 500
    
    # Google Cloud settings
    google_project_id: str = os.getenv("GOOGLE_PROJECT_ID", "")
    use_secret_manager: bool = os.getenv("USE_SECRET_MANAGER", "False").lower() == "true"
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"  # Ignore extra fields in .env file

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Load secrets from Google Secret Manager if enabled
        if self.use_secret_manager and self.google_project_id:
            self._load_secrets_from_secret_manager()
        else:
            # Otherwise, load from environment variables
            self.deepgram_api_key = os.getenv("DEEPGRAM_API_KEY", "")
            self.groq_api_key = os.getenv("GROQ_API_KEY", "")
    
    def _load_secrets_from_secret_manager(self) -> None:
        """Load secrets from Google Secret Manager."""
        try:
            client = secretmanager.SecretManagerServiceClient()
            
            # Load Deepgram API key
            if not self.deepgram_api_key:
                secret_name = f"projects/{self.google_project_id}/secrets/DEEPGRAM_API_KEY/versions/latest"
                response = client.access_secret_version(name=secret_name)
                self.deepgram_api_key = response.payload.data.decode("UTF-8")
            
            # Load Groq API key
            if not self.groq_api_key:
                secret_name = f"projects/{self.google_project_id}/secrets/GROQ_API_KEY/versions/latest"
                response = client.access_secret_version(name=secret_name)
                self.groq_api_key = response.payload.data.decode("UTF-8")
                
        except Exception as e:
            print(f"Error loading secrets from Secret Manager: {e}")
            # Fall back to environment variables
            self.deepgram_api_key = os.getenv("DEEPGRAM_API_KEY", "")
            self.groq_api_key = os.getenv("GROQ_API_KEY", "")

    @property
    def DEEPGRAM_API_KEY(self) -> Optional[str]:
        """Uppercase property for backward compatibility"""
        return self.deepgram_api_key

# Create a global settings instance
settings = Settings()