import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/app_error.dart';
import '../../presentation/widgets/common/enhanced_error_dialog.dart';
import '../../presentation/widgets/common/loading_state_widget.dart';
import 'retry_service.dart';

/// Comprehensive error handling service with user feedback and retry mechanisms
class ErrorHandlingService {
  static final ErrorHandlingService _instance = ErrorHandlingService._();
  static ErrorHandlingService get instance => _instance;
  
  final RetryService _retryService = RetryService.instance;
  final Map<String, Timer> _errorCooldowns = {};
  final Set<String> _activeErrors = {};
  
  ErrorHandlingService._();
  
  /// Handle error with user feedback and retry options
  Future<void> handleError({
    required BuildContext context,
    required AppError error,
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
    bool showDialog = true,
    bool allowRetry = true,
    String? customTitle,
    String? customMessage,
  }) async {
    // Prevent duplicate error dialogs
    final errorKey = '${error.type}_${error.details}';
    if (_activeErrors.contains(errorKey)) {
      return;
    }
    
    _activeErrors.add(errorKey);
    
    try {
      if (showDialog && context.mounted) {
        await EnhancedErrorDialog.show(
          context: context,
          error: error,
          onRetry: allowRetry ? () async {
            if (onRetry != null) {
              await _handleRetryWithFeedback(
                context: context,
                error: error,
                retryOperation: onRetry,
              );
            }
          } : null,
          onDismiss: onDismiss,
          showRetryButton: allowRetry && onRetry != null,
          customTitle: customTitle,
          customMessage: customMessage,
        );
      }
    } finally {
      _activeErrors.remove(errorKey);
    }
  }
  
  /// Handle retry operation with loading feedback
  Future<void> _handleRetryWithFeedback({
    required BuildContext context,
    required AppError error,
    required VoidCallback retryOperation,
  }) async {
    if (!context.mounted) return;
    
    // Show loading dialog
    final loadingDialog = LoadingStateWidget.showDialog(
      context: context,
      message: _getRetryMessage(error),
      subMessage: 'Please wait while we try again...',
      showProgress: false,
      icon: Icons.refresh,
      color: _getErrorColor(error),
      barrierDismissible: false,
    );
    
    try {
      // Execute retry operation
      retryOperation();
      
      // Wait a moment to show the loading state
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }
      
    } catch (retryError) {
      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }
      
      // Show retry failed error
      final retryAppError = AppError.unknown(
        details: 'Retry failed: ${retryError.toString()}',
        originalException: retryError is Exception ? retryError : Exception(retryError.toString()),
      );
      
      await handleError(
        context: context,
        error: retryAppError,
        showDialog: true,
        allowRetry: false,
        customTitle: 'Retry Failed',
        customMessage: 'The retry attempt was unsuccessful. Please try again later or check your connection.',
      );
    }
  }
  
  /// Execute operation with automatic error handling and retry
  Future<T> executeWithErrorHandling<T>({
    required BuildContext context,
    required Future<T> Function() operation,
    required String operationName,
    RetryType retryType = RetryType.network,
    int maxRetries = 3,
    bool showLoadingDialog = false,
    bool showErrorDialog = true,
    String? loadingMessage,
    String? errorTitle,
    String? errorMessage,
  }) async {
    OverlayEntry? loadingOverlay;
    
    try {
      // Show loading dialog if requested
      if (showLoadingDialog && context.mounted) {
        loadingOverlay = LoadingStateWidget.showOverlay(
          context: context,
          message: loadingMessage ?? 'Processing...',
          subMessage: 'Please wait',
          showProgress: false,
          barrierDismissible: false,
        );
      }
      
      // Execute operation with retry
      final result = await _retryService.executeWithRetry<T>(
        operation: operation,
        maxRetries: maxRetries,
        shouldRetry: _getShouldRetryFunction(retryType),
        onRetry: (attempt, error) {
          if (kDebugMode) {
            print('$operationName retry attempt $attempt: $error');
          }
        },
      );
      
      return result;
      
    } catch (error) {
      // Convert to AppError
      final appError = _convertToAppError(error, operationName);
      
      // Show error dialog if requested
      if (showErrorDialog && context.mounted) {
        await handleError(
          context: context,
          error: appError,
          customTitle: errorTitle,
          customMessage: errorMessage,
          allowRetry: false, // Already retried automatically
        );
      }
      
      rethrow;
    } finally {
      // Remove loading overlay
      loadingOverlay?.remove();
    }
  }
  
  /// Handle network connectivity issues
  Future<void> handleNetworkError({
    required BuildContext context,
    required dynamic error,
    VoidCallback? onRetry,
  }) async {
    final appError = AppError.networkError(
      details: error.toString(),
      originalException: error is Exception ? error : Exception(error.toString()),
    );
    
    await handleError(
      context: context,
      error: appError,
      onRetry: onRetry,
      customMessage: 'Please check your internet connection and try again. Make sure you\'re connected to Wi-Fi or mobile data.',
    );
  }
  
  /// Handle WebSocket connection errors
  Future<void> handleWebSocketError({
    required BuildContext context,
    required dynamic error,
    VoidCallback? onRetry,
  }) async {
    final appError = AppError.websocketConnectionFailed(
      details: error.toString(),
      originalException: error is Exception ? error : Exception(error.toString()),
    );
    
    await handleError(
      context: context,
      error: appError,
      onRetry: onRetry,
      customMessage: 'We\'re having trouble connecting to our servers. This is usually temporary.',
    );
  }
  
  /// Handle audio recording errors
  Future<void> handleAudioError({
    required BuildContext context,
    required dynamic error,
    VoidCallback? onRetry,
  }) async {
    final appError = AppError.audioRecordingFailed(
      details: error.toString(),
      originalException: error is Exception ? error : Exception(error.toString()),
    );
    
    await handleError(
      context: context,
      error: appError,
      onRetry: onRetry,
      customMessage: 'We couldn\'t record your audio. Please check that no other apps are using the microphone.',
    );
  }
  
  /// Handle permission errors
  Future<void> handlePermissionError({
    required BuildContext context,
    required String permission,
    VoidCallback? onRetry,
  }) async {
    final appError = AppError.microphonePermissionDenied();
    
    await handleError(
      context: context,
      error: appError,
      onRetry: onRetry,
      allowRetry: true,
      customMessage: 'DeutschKorrekt needs $permission permission to work properly. Please grant permission in your device settings.',
    );
  }
  
  /// Show graceful degradation message
  Future<void> showGracefulDegradation({
    required BuildContext context,
    required String feature,
    required String reason,
    List<String>? alternatives,
  }) async {
    if (!context.mounted) return;
    
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.info_outline, color: Colors.orange),
            const SizedBox(width: 8),
            Text('Feature Temporarily Unavailable'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('$feature is currently unavailable: $reason'),
            if (alternatives != null && alternatives.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text('You can still:', style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              ...alternatives.map((alt) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('• ', style: TextStyle(color: Colors.blue)),
                    Expanded(child: Text(alt)),
                  ],
                ),
              )),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }
  
  /// Convert generic error to AppError
  AppError _convertToAppError(dynamic error, String operationName) {
    if (error is AppError) {
      return error;
    }
    
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('permission')) {
      return AppError.microphonePermissionDenied();
    } else if (errorString.contains('network') || errorString.contains('connection')) {
      return AppError.networkError(
        details: error.toString(),
        originalException: error is Exception ? error : Exception(error.toString()),
      );
    } else if (errorString.contains('websocket')) {
      return AppError.websocketConnectionFailed(
        details: error.toString(),
        originalException: error is Exception ? error : Exception(error.toString()),
      );
    } else if (errorString.contains('audio') || errorString.contains('recording')) {
      return AppError.audioRecordingFailed(
        details: error.toString(),
        originalException: error is Exception ? error : Exception(error.toString()),
      );
    } else {
      return AppError.unknown(
        details: '$operationName failed: ${error.toString()}',
        originalException: error is Exception ? error : Exception(error.toString()),
      );
    }
  }
  
  /// Get should retry function for retry type
  bool Function(dynamic) _getShouldRetryFunction(RetryType retryType) {
    switch (retryType) {
      case RetryType.network:
        return _retryService.createConfig(type: RetryType.network).shouldRetry;
      case RetryType.websocket:
        return _retryService.createConfig(type: RetryType.websocket).shouldRetry;
      case RetryType.audio:
        return _retryService.createConfig(type: RetryType.audio).shouldRetry;
      case RetryType.custom:
        return (error) => true;
    }
  }
  
  /// Get retry message for error type
  String _getRetryMessage(AppError error) {
    switch (error.type) {
      case AppErrorType.networkError:
        return 'Reconnecting...';
      case AppErrorType.websocketConnectionFailed:
        return 'Reconnecting to server...';
      case AppErrorType.audioRecordingFailed:
        return 'Restarting audio...';
      case AppErrorType.microphonePermissionDenied:
        return 'Checking permissions...';
      default:
        return 'Retrying...';
    }
  }
  
  /// Get error color for UI
  Color _getErrorColor(AppError error) {
    switch (error.type) {
      case AppErrorType.microphonePermissionDenied:
        return Colors.orange;
      case AppErrorType.audioRecordingFailed:
        return Colors.red;
      case AppErrorType.websocketConnectionFailed:
      case AppErrorType.networkError:
        return Colors.blue;
      default:
        return Colors.orange;
    }
  }
  
  /// Clear error cooldowns
  void clearCooldowns() {
    for (final timer in _errorCooldowns.values) {
      timer.cancel();
    }
    _errorCooldowns.clear();
    _activeErrors.clear();
  }
  
  /// Dispose of the service
  void dispose() {
    clearCooldowns();
  }
}
