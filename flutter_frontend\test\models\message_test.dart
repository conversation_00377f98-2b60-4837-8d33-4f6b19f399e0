import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/data/models/message.dart';

void main() {
  group('Message', () {
    group('AI message factory', () {
      test('should extract sentence from curly braces in AI message', () {
        const messageText = 'Here is the response: {Hello world! This is a test.}';
        
        final message = Message.ai(
          id: 'test_id',
          text: messageText,
        );
        
        expect(message.extractedSentence, equals('Hello world!'));
        expect(message.isUser, isFalse);
        expect(message.text, equals(messageText));
      });

      test('should handle AI message without curly braces', () {
        const messageText = 'This is a regular response without braces.';
        
        final message = Message.ai(
          id: 'test_id',
          text: messageText,
        );
        
        expect(message.extractedSentence, isNull);
        expect(message.isUser, isFalse);
        expect(message.text, equals(messageText));
      });

      test('should handle AI message with empty curly braces', () {
        const messageText = 'Response: {}';
        
        final message = Message.ai(
          id: 'test_id',
          text: messageText,
        );
        
        expect(message.extractedSentence, isNull);
        expect(message.isUser, isFalse);
        expect(message.text, equals(messageText));
      });

      test('should extract German sentence from AI message', () {
        const messageText = 'Antwort: {Hallo Welt! Wie geht es dir?}';
        
        final message = Message.ai(
          id: 'test_id',
          text: messageText,
        );
        
        expect(message.extractedSentence, equals('Hallo Welt!'));
        expect(message.isUser, isFalse);
        expect(message.text, equals(messageText));
      });

      test('should handle multiple curly braces and extract from first', () {
        const messageText = 'First: {Hello world!} Second: {Goodbye world!}';
        
        final message = Message.ai(
          id: 'test_id',
          text: messageText,
        );
        
        expect(message.extractedSentence, equals('Hello world!'));
        expect(message.isUser, isFalse);
        expect(message.text, equals(messageText));
      });
    });

    group('User message factory', () {
      test('should not extract sentence from user message', () {
        const messageText = 'User input: {Hello world!}';
        
        final message = Message.user(
          id: 'test_id',
          text: messageText,
        );
        
        expect(message.extractedSentence, isNull);
        expect(message.isUser, isTrue);
        expect(message.text, equals(messageText));
      });
    });

    group('copyWith method', () {
      test('should preserve extracted sentence when copying', () {
        final originalMessage = Message.ai(
          id: 'test_id',
          text: 'Response: {Hello world!}',
        );
        
        final copiedMessage = originalMessage.copyWith(
          text: 'Updated response: {Hello world!}',
        );
        
        expect(copiedMessage.extractedSentence, equals('Hello world!'));
        expect(copiedMessage.text, equals('Updated response: {Hello world!}'));
      });

      test('should update extracted sentence when explicitly set', () {
        final originalMessage = Message.ai(
          id: 'test_id',
          text: 'Response: {Hello world!}',
        );
        
        final copiedMessage = originalMessage.copyWith(
          extractedSentence: 'New sentence!',
        );
        
        expect(copiedMessage.extractedSentence, equals('New sentence!'));
        expect(copiedMessage.text, equals('Response: {Hello world!}'));
      });

      test('should clear extracted sentence when set to null', () {
        final originalMessage = Message.ai(
          id: 'test_id',
          text: 'Response: {Hello world!}',
        );
        
        final copiedMessage = originalMessage.copyWith(
          extractedSentence: null,
        );
        
        expect(copiedMessage.extractedSentence, isNull);
        expect(copiedMessage.text, equals('Response: {Hello world!}'));
      });
    });
  });
}