-- Enable Row Level Security (RLS) on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see and modify their own profile
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.email() = email);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.email() = email);

CREATE POLICY "Users can insert own profile" ON users
    FOR INSERT WITH CHECK (auth.email() = email);

-- Enable Row Level Security (RLS) on sessions table
ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own sessions
CREATE POLICY "Users can view own sessions" ON sessions
    FOR SELECT USING (auth.email() = email);

CREATE POLICY "Users can insert own sessions" ON sessions
    FOR INSERT WITH CHECK (auth.email() = email);

-- Policy: Prevent users from updating or deleting sessions (immutable audit trail)
CREATE POLICY "Sessions are immutable" ON sessions
    FOR UPDATE USING (false);

CREATE POLICY "Sessions cannot be deleted by users" ON sessions
    FOR DELETE USING (false);

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON users TO authenticated;
GRANT SELECT, INSERT ON sessions TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE sessions_session_id_seq TO authenticated;