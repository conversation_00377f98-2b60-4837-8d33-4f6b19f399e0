import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:provider/provider.dart';
import '../../core/constants/colors.dart';
import '../../services/audio_streaming_service.dart';
import '../../data/models/message.dart';
import '../../data/services/tts_audio_service.dart';
import '../widgets/profile/profile_popup.dart';
import '../providers/auth_provider.dart';
import '../providers/enhanced_profile_provider.dart';

/// DeutschKorrekt Chat Screen - German Speech Correction App
/// Professional Flutter implementation matching the Expo version exactly
class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final List<Message> _messages = [
    Message(
      id: '1',
      text: '''**Hallo! Ich bin DeutschKorrekt** 🇩🇪

De<PERSON> persönlicher Sprachtrainer für **perfektes Deutsch**!

**How it works:**
* Press the microphone button
* Speak in German or English
* Get instant corrections and improvements

Let's get started! 🎯''',
      isUser: false,
      timestamp: DateTime.now(),
    ),
  ];

  bool _showSettings = false;
  bool _showProfile = false;
  String? _currentMessageId;
  late AudioStreamingService _audioService;
  late TTSAudioService _ttsService;
  final ScrollController _scrollController = ScrollController();
  final Map<String, GlobalKey> _messageKeys = {};
  bool _isProcessingButtonPress = false;

  @override
  void initState() {
    super.initState();
    _initializeAudioService();
    _initializeTTSService();
  }

  @override
  void dispose() {
    _audioService.dispose();
    _ttsService.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _initializeAudioService() {
    _audioService = AudioStreamingService(
      onPartialTranscript: _handlePartialTranscript,
      onFinalTranscript: _handleFinalTranscript,
      onGroqResponse: _handleGroqResponse,
      onError: _handleError,
    );
  }

  void _initializeTTSService() {
    _ttsService = TTSAudioService();

    // Listen to state changes for debugging and UI updates
    _audioService.stateStream.listen((state) {
      debugPrint('🔄 Audio service state changed to: $state');
      debugPrint('🔄 isStreaming: ${_audioService.isStreaming}, isProcessing: ${_audioService.isProcessing}');
      // Force UI rebuild when state changes
      if (mounted) {
        setState(() {
          // This will trigger a rebuild to update button appearance
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          body: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xFF64748b),
                  Color(0xFF475569),
                  Color(0xFF334155),
                  Color(0xFF1e293b),
                  Color(0xFF0f172a),
                ],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
              ),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  // Header
                  _buildHeader(),

                  // Messages List
                  Expanded(
                    child: Container(
                      color: const Color(0xFFf8fafc),
                      child: ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(20),
                        itemCount: _messages.length,
                        itemBuilder: (context, index) {
                          return _buildMessage(_messages[index]);
                        },
                      ),
                    ),
                  ),

                  // Microphone Section
                  _buildMicSection(),
                ],
              ),
            ),
          ),
        ),
        
        // Profile popup overlay
        if (_showProfile)
          GestureDetector(
            onTap: () => setState(() => _showProfile = false),
            child: Container(
              color: Colors.black54,
              child: Center(
                child: GestureDetector(
                  onTap: () {}, // Prevent closing when tapping the popup itself
                  child: const ProfilePopup(),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF1e293b), Color(0xFF334155)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border(
          bottom: BorderSide(color: Color(0xFF475569), width: 2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 12,
            offset: Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              // German Flag
              Container(
                width: 32,
                height: 20,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: const Color(0xFF64748b)),
                ),
                child: Column(
                  children: [
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        color: AppColors.germanBlack,
                      ),
                    ),
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        color: AppColors.germanRed,
                      ),
                    ),
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        color: AppColors.germanYellow,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'DeutschKorrekt',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFf1f5f9),
                ),
              ),
            ],
          ),
          Row(
            children: [
              IconButton(
                onPressed: () => setState(() => _showProfile = true),
                icon: const Icon(Icons.person, color: Color(0xFFf8fafc)),
              ),
              IconButton(
                onPressed: () => setState(() => _showSettings = true),
                icon: const Icon(Icons.settings, color: Color(0xFFf8fafc)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMessage(Message message) {
    // Create or get GlobalKey for this message
    _messageKeys[message.id] ??= GlobalKey();

    return Container(
      key: _messageKeys[message.id],
      margin: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.85,
              ),
              padding: const EdgeInsets.fromLTRB(20, 16, 20, 20), // Left and bottom padding so text won't touch icon
              decoration: BoxDecoration(
                gradient: message.isUser
                    ? const LinearGradient(
                        colors: [Color(0xFF64748b), Color(0xFF475569)],
                      )
                    : null,
                color: message.isUser ? null : Colors.white,
                borderRadius: BorderRadius.circular(18),
                border: message.isUser ? null : Border.all(color: const Color(0xFFe2e8f0), width: 2),
                boxShadow: const [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 6,
                    offset: Offset(0, 3),
                  ),
                ],
              ),
              child: message.isUser
                  ? Text(
                      message.text,
                      style: const TextStyle(
                        fontSize: 17,
                        color: Color(0xFFf8fafc),
                        height: 1.4,
                      ),
                    )
                  : Stack(
                      children: [
                        MarkdownBody(
                          data: message.text,
                          styleSheet: MarkdownStyleSheet(
                        p: const TextStyle(
                          fontSize: 17,
                          color: Color(0xFF1e293b),
                          height: 1.4,
                        ),
                        strong: const TextStyle(
                          fontSize: 17,
                          color: Color(0xFF1e293b),
                          fontWeight: FontWeight.bold,
                          height: 1.4,
                        ),
                        listBullet: const TextStyle(
                          fontSize: 17,
                          color: Color(0xFF1e293b),
                          height: 1.4,
                        ),
                        h1: const TextStyle(
                          fontSize: 20,
                          color: Color(0xFF1e293b),
                          fontWeight: FontWeight.bold,
                          height: 1.4,
                        ),
                        h2: const TextStyle(
                          fontSize: 19,
                          color: Color(0xFF1e293b),
                          fontWeight: FontWeight.bold,
                          height: 1.4,
                        ),
                        h3: const TextStyle(
                          fontSize: 18,
                          color: Color(0xFF1e293b),
                          fontWeight: FontWeight.bold,
                          height: 1.4,
                        ),
                        code: TextStyle(
                          fontSize: 16,
                          color: const Color(0xFF1e293b),
                          backgroundColor: Colors.grey[100],
                          fontFamily: 'monospace',
                        ),
                        blockquote: const TextStyle(
                          fontSize: 17,
                          color: Color(0xFF64748b),
                          fontStyle: FontStyle.italic,
                          height: 1.4,
                        ),
                      ),
                    ),
                    // Audio icon for AI messages with extracted sentences
                    if (!message.isUser &&
                        message.extractedSentence != null &&
                        message.extractedSentence!.isNotEmpty)
                      Positioned(
                        top: 0,    // Not cut off
                        right: 0,  // As far right as possible
                        child: _AudioIconWidget(
                          message: message,
                          ttsService: _ttsService,
                        ),
                      ),
                  ],
                ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMicSection() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF1e293b), Color(0xFF334155)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border(
          top: BorderSide(color: Color(0xFF475569), width: 2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 12,
            offset: Offset(0, -4),
          ),
        ],
      ),
      padding: const EdgeInsets.fromLTRB(20, 16, 20, 20),
      child: Center(
        child: GestureDetector(
          onTap: _handleMicPress,
          child: Transform.scale(
            scale: (_audioService.isStreaming || _audioService.isProcessing) ? 1.15 : 1.0,
            child: Container(
              width: 70,
              height: 70,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(35),
                border: Border.all(color: Colors.white24, width: 2),
                boxShadow: const [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 12,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: _audioService.isStreaming
                  ? Container(
                      width: 70,
                      height: 70,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(35),
                        gradient: LinearGradient(
                          colors: [AppColors.germanRed, AppColors.germanRed.withOpacity(0.8)],
                        ),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.mic,
                          size: 24,
                          color: Colors.white,
                        ),
                      ),
                    )
                  : _audioService.isConnecting
                      ? Container(
                          width: 70,
                          height: 70,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(35),
                            gradient: const LinearGradient(
                              colors: [Color(0xFF6b7280), Color(0xFF4b5563)],
                            ),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.mic,
                              size: 24,
                              color: Colors.white,
                            ),
                          ),
                        )
                      : _audioService.isProcessing
                          ? Container(
                              width: 70,
                              height: 70,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(35),
                                gradient: LinearGradient(
                                  colors: [AppColors.infoBlue, AppColors.infoBlue.withOpacity(0.8)],
                                ),
                              ),
                              child: const Center(
                                child: SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 3,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                ),
                              ),
                            )
                          : Stack(
                          children: [
                            // German flag background
                            Container(
                              width: 70,
                              height: 70,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(35),
                              ),
                              child: Column(
                                children: [
                                  Expanded(
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: AppColors.germanBlack,
                                        borderRadius: const BorderRadius.only(
                                          topLeft: Radius.circular(35),
                                          topRight: Radius.circular(35),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Container(
                                      width: double.infinity,
                                      color: AppColors.germanRed,
                                    ),
                                  ),
                                  Expanded(
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: AppColors.germanYellow,
                                        borderRadius: const BorderRadius.only(
                                          bottomLeft: Radius.circular(35),
                                          bottomRight: Radius.circular(35),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Microphone/Stop icon overlay
                            Center(
                              child: Icon(
                                _audioService.isStreaming ? Icons.stop : Icons.mic,
                                size: 24,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleMicPress() async {
    debugPrint('🎯 Button pressed - isStreaming: ${_audioService.isStreaming}, isConnecting: ${_audioService.isConnecting}, currentMessageId: $_currentMessageId, isProcessingButtonPress: $_isProcessingButtonPress');

    // Prevent rapid button presses that cause state conflicts
    if (_isProcessingButtonPress) {
      debugPrint('⚠️ Button press ignored - already processing');
      return;
    }

    if (_audioService.isStreaming) {
      // Stop current streaming session
      _isProcessingButtonPress = true;
      debugPrint('🛑 Stop button pressed - manual stop');
      debugPrint('🛑 BEFORE manualStop - isStreaming: ${_audioService.isStreaming}');

      try {
        // Create placeholder AI message bubble immediately for responsiveness
        _createPlaceholderAIMessage();
        
        await _audioService.manualStop();
        debugPrint('✅ Manual stop completed');
        debugPrint('✅ AFTER manualStop - isStreaming: ${_audioService.isStreaming}');
        // Force UI rebuild after manual stop
        setState(() {});
      } catch (e) {
        debugPrint('❌ Error in manual stop: $e');
      } finally {
        Future.delayed(const Duration(milliseconds: 500), () {
          _isProcessingButtonPress = false;
          debugPrint('🔓 Button processing flag cleared');
        });
      }
    } else if (!_audioService.isStreaming && _currentMessageId == null && !_audioService.isConnecting) {
      // Start new streaming session
      _isProcessingButtonPress = true;
      debugPrint('🎯 Button pressed - starting new session');

      try {
        await _startStreaming();
      } catch (e) {
        debugPrint('❌ Error in _startStreaming: $e');
      } finally {
        // Reset flag after a delay to prevent rapid presses
        Future.delayed(const Duration(milliseconds: 500), () {
          _isProcessingButtonPress = false;
          debugPrint('🔓 Button processing flag cleared');
        });
      }
    } else {
      debugPrint('🚫 Button press ignored - already active or connecting');
    }
  }

  Future<void> _startStreaming() async {
    try {
      debugPrint('🚀 Starting streaming session...');

      // Create message for this session
      final messageId = '${DateTime.now().millisecondsSinceEpoch}-${DateTime.now().microsecond}';
      setState(() {
        _currentMessageId = messageId;
      });

      final newMessage = Message(
        id: messageId,
        text: 'Connecting...',
        isUser: true,
        timestamp: DateTime.now(),
        isAudio: true,
        isStreaming: true,
      );

      setState(() {
        _messages.add(newMessage);
      });

      // Auto-scroll to show the new message
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });

      // Start streaming
      await _audioService.startStreaming();

      // Update message to show listening state
      _updateMessage(messageId, 'Listening...');

    } catch (error) {
      debugPrint('❌ Failed to start streaming: $error');

      if (_currentMessageId != null) {
        _updateMessage(_currentMessageId!, 'Failed to start streaming');
      }

      // Show specific error message
      String errorMessage = 'Failed to start audio streaming. Please check microphone permissions.';
      if (error.toString().contains('Microphone permission')) {
        errorMessage = 'Microphone permission required. Please grant permission in settings and try again.';
      } else if (error.toString().contains('Not connected')) {
        errorMessage = 'Connection failed. Please check your internet connection.';
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $errorMessage'),
          backgroundColor: AppColors.errorRed,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  void _handlePartialTranscript(String text) {
    debugPrint('🎯 Partial transcript: $text');
    if (_currentMessageId != null) {
      _updateMessage(_currentMessageId!, text);
    }
  }

  void _handleFinalTranscript(String text) {
    debugPrint('🎯 Final transcript: $text');
    if (_currentMessageId != null) {
      _updateMessage(_currentMessageId!, text, isStreaming: false, isProcessing: false);
      setState(() {
        _currentMessageId = null;
      });
    }
  }

  void _handleGroqResponse(GroqResponse response) {
    debugPrint('🤖 Groq response received: ${response.responseText}');



    // Find and replace the placeholder message with the real response
    final placeholderIndex = _messages.indexWhere((msg) => msg.id.startsWith('placeholder_'));
    
    if (placeholderIndex != -1) {
      debugPrint('📝 Replacing placeholder message with real Groq response');
      
      // Create AI response message using the proper factory method for sentence extraction
      final aiMessage = Message.ai(
        id: 'groq_${DateTime.now().millisecondsSinceEpoch}',
        text: response.responseText,
      );
      
      setState(() {
        _messages[placeholderIndex] = aiMessage; // Replace placeholder
        _currentMessageId = null;
      });
      
      debugPrint('📝 Placeholder message replaced successfully');
    } else {
      debugPrint('⚠️ No placeholder message found, adding new message');
      
      // Fallback: add new message if no placeholder found
      final aiMessage = Message.ai(
        id: 'groq_${DateTime.now().millisecondsSinceEpoch}',
        text: response.responseText,
      );

      setState(() {
        _messages.add(aiMessage);
        _currentMessageId = null;
      });
    }

    debugPrint('🤖 AI message replaced - positioning already handled by placeholder');
  }

  void _handleError(String error) {
    debugPrint('❌ Audio service error: $error');

    if (_currentMessageId != null) {
      _updateMessage(_currentMessageId!, 'Error: $error', isStreaming: false);
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Error: $error'),
        backgroundColor: AppColors.errorRed,
      ),
    );
  }

  void _updateMessage(String messageId, String text, {bool? isStreaming, bool? isProcessing}) {
    setState(() {
      final index = _messages.indexWhere((msg) => msg.id == messageId);
      if (index != -1) {
        _messages[index] = Message(
          id: _messages[index].id,
          text: text,
          isUser: _messages[index].isUser,
          timestamp: _messages[index].timestamp,
          isAudio: _messages[index].isAudio,
          isStreaming: isStreaming ?? _messages[index].isStreaming,
          isProcessing: isProcessing ?? _messages[index].isProcessing,
        );
      }
    });

    // Auto-scroll to bottom when message is updated
    _scrollToBottom();
  }

  /// Auto-scroll to the bottom of the chat (for new recordings)
  void _scrollToBottom({bool animated = true}) {
    if (_scrollController.hasClients) {
      if (animated) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      } else {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    }
  }



  /// Create placeholder AI message bubble immediately for responsiveness
  void _createPlaceholderAIMessage() {
    debugPrint('📝 Creating placeholder AI message bubble for responsiveness');
    
    // Create a placeholder AI message with loading indicator and invisible text for proper sizing
    final placeholderMessage = Message(
      id: 'placeholder_${DateTime.now().millisecondsSinceEpoch}',
      text: '''⚡ Processing with AI agents...



This is invisible placeholder text to ensure the message bubble has the proper height for positioning. This paragraph helps maintain consistent spacing and layout while the AI processes your request and generates a comprehensive response.

Additional invisible content continues here to provide adequate vertical space for the message bubble. This ensures that when we position the chat window, everything aligns correctly and the user experience remains smooth and predictable throughout the processing phase.

The final paragraph of invisible placeholder text completes the sizing requirements for optimal positioning. This approach allows us to pre-position the chat interface accurately before the actual AI response content arrives, creating a more responsive and polished user experience.''',
      isUser: false,
      timestamp: DateTime.now(),
      isProcessing: true,
    );
    
    setState(() {
      _messages.add(placeholderMessage);
    });
    
    // Start precise positioning immediately for the placeholder
    _scrollToLastUserMessageImmediately();
    
    debugPrint('📝 Placeholder AI message created and positioned');
  }

  /// Smooth positioning for placeholder message
  void _scrollToLastUserMessageImmediately() {
    debugPrint('🎯 Starting smooth positioning for placeholder');

    // Single smooth scroll with minimal delay
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _scrollToLastUserMessage();
      }
    });
  }



  /// Scroll to position the last user message at the top (for Groq responses)
  void _scrollToLastUserMessage() {
    debugPrint('🎯 _scrollToLastUserMessage() called');

    // Find the last user message
    String? lastUserMessageId;
    for (int i = _messages.length - 1; i >= 0; i--) {
      if (_messages[i].isUser) {
        lastUserMessageId = _messages[i].id;
        break;
      }
    }

    if (lastUserMessageId == null) return;

    // Wait for UI to settle, then scroll smoothly
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 200), () {
        if (!mounted) return;

        // Get the GlobalKey for the last user message
        final GlobalKey? messageKey = _messageKeys[lastUserMessageId];
        if (messageKey?.currentContext == null) {
          debugPrint('❌ Message key context is null for ID: $lastUserMessageId');
          return;
        }

        debugPrint('✅ Scrolling to user message: $lastUserMessageId');

        // Use Flutter's built-in Scrollable.ensureVisible with smooth animation
        Scrollable.ensureVisible(
          messageKey!.currentContext!,
          duration: const Duration(milliseconds: 600), // Slightly longer for smoother animation
          curve: Curves.easeInOutCubic, // Smoother curve
          alignment: 0.0, // 0.0 = top of viewport, 1.0 = bottom of viewport
        ).then((_) {
          debugPrint('✅ Scroll animation completed');
        }).catchError((error) {
          debugPrint('❌ Scroll error: $error');
        });
      });
    });
  }
}

/// Custom audio icon widget with proper animations and state management
class _AudioIconWidget extends StatefulWidget {
  final Message message;
  final TTSAudioService ttsService;

  const _AudioIconWidget({
    required this.message,
    required this.ttsService,
  });

  @override
  State<_AudioIconWidget> createState() => _AudioIconWidgetState();
}

class _AudioIconWidgetState extends State<_AudioIconWidget>
    with TickerProviderStateMixin {
  late AnimationController _loadingController;
  late AnimationController _playingController;
  late AnimationController _tapController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _tapAnimation;
  
  bool _isLoading = false;
  bool _isPlaying = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _loadingController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _playingController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _tapController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    // Initialize animations
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingController,
      curve: Curves.linear,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _playingController,
      curve: Curves.easeInOutSine,
    ));
    
    _tapAnimation = Tween<double>(
      begin: 1.0,
      end: 0.85,
    ).animate(CurvedAnimation(
      parent: _tapController,
      curve: Curves.easeInOutBack,
    ));
  }

  @override
  void dispose() {
    _loadingController.dispose();
    _playingController.dispose();
    _tapController.dispose();
    super.dispose();
  }

  void _startLoadingAnimation() {
    setState(() {
      _isLoading = true;
      _isPlaying = false;
      _hasError = false;
    });
    _playingController.stop();
    _loadingController.repeat();
  }

  void _startPlayingAnimation() {
    setState(() {
      _isLoading = false;
      _isPlaying = true;
      _hasError = false;
    });
    _loadingController.stop();
    _playingController.repeat(reverse: true);
  }

  void _stopAllAnimations() {
    setState(() {
      _isLoading = false;
      _isPlaying = false;
    });
    _loadingController.stop();
    _playingController.stop();
  }

  void _showError() {
    setState(() {
      _isLoading = false;
      _isPlaying = false;
      _hasError = true;
    });
    _loadingController.stop();
    _playingController.stop();
    
    // Reset error state after 3 seconds
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _hasError = false;
        });
      }
    });
  }

  IconData _getIcon() {
    if (_hasError) return Icons.volume_off;
    if (_isLoading) return Icons.sync;
    if (_isPlaying) return Icons.volume_up;
    return Icons.volume_up_outlined;
  }

  Color _getColor() {
    if (_hasError) return Colors.red;
    if (_isLoading) return Colors.blue;
    if (_isPlaying) return Colors.green;
    return Colors.grey;
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _tapAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _tapAnimation.value,
          child: Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: _getColor().withOpacity(0.1),
              borderRadius: BorderRadius.circular(18),
              border: Border.all(color: _getColor().withOpacity(0.3)),
            ),
            child: IconButton(
              icon: AnimatedBuilder(
                animation: _isLoading ? _rotationAnimation : _pulseAnimation,
                builder: (context, child) {
                  Widget iconWidget = Icon(
                    _getIcon(),
                    size: 18,
                    color: _getColor(),
                  );
                  
                  if (_isLoading) {
                    return Transform.rotate(
                      angle: _rotationAnimation.value * 2.0 * 3.14159,
                      child: iconWidget,
                    );
                  } else if (_isPlaying) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: iconWidget,
                    );
                  } else {
                    return iconWidget;
                  }
                },
              ),
              onPressed: () async {
                // Tap animation
                _tapController.forward().then((_) {
                  _tapController.reverse();
                });
                
                debugPrint('🔊 TTS requested for: "${widget.message.extractedSentence}"');
                
                _startLoadingAnimation();
                
                try {
                  final result = await widget.ttsService.playTTS(
                    widget.message.extractedSentence!,
                    widget.message.id,
                  );
                  
                  if (result.success) {
                    debugPrint('✅ TTS playback started successfully');
                    _startPlayingAnimation();
                    
                    // Listen for playback completion
                    _listenForPlaybackCompletion();
                  } else {
                    debugPrint('❌ TTS failed: ${result.errorMessage}');
                    _showError();
                    
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(result.userFriendlyMessage ?? 'Audio playback failed'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                } catch (e) {
                  debugPrint('❌ TTS error: $e');
                  _showError();
                  
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Audio playback failed'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              padding: EdgeInsets.zero,
            ),
          ),
        );
      },
    );
  }

  void _listenForPlaybackCompletion() {
    // Simple polling to check if playback is still active
    Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      
      // Check if this message is still the currently playing one
      if (widget.ttsService.currentlyPlayingMessageId != widget.message.id) {
        _stopAllAnimations();
        timer.cancel();
      }
      
      // Also check if service is no longer playing
      if (!widget.ttsService.isPlaying && _isPlaying) {
        _stopAllAnimations();
        timer.cancel();
      }
    });
  }
}
