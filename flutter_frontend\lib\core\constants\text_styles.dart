import 'package:flutter/material.dart';
import 'colors.dart';

/// Text styles matching the Expo version exactly
class AppTextStyles {
  static const String fontFamily = 'Inter';
  
  static const TextStyle headerTitle = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.lightText,
    fontFamily: fontFamily,
  );
  
  static const TextStyle messageText = TextStyle(
    fontSize: 17,
    fontWeight: FontWeight.normal,
    height: 1.4,
    fontFamily: fontFamily,
  );
  
  static const TextStyle userMessageText = TextStyle(
    fontSize: 17,
    fontWeight: FontWeight.normal,
    height: 1.4,
    color: AppColors.userMessageText,
    fontFamily: fontFamily,
  );
  
  static const TextStyle aiMessageText = TextStyle(
    fontSize: 17,
    fontWeight: FontWeight.normal,
    height: 1.4,
    color: AppColors.aiMessageText,
    fontFamily: fontFamily,
  );
  
  static const TextStyle boldText = TextStyle(
    fontWeight: FontWeight.bold,
  );
  
  static const TextStyle streamingIndicator = TextStyle(
    fontSize: 15,
    fontStyle: FontStyle.italic,
    color: AppColors.slate600,
    fontFamily: fontFamily,
  );
  
  static const TextStyle processingIndicator = TextStyle(
    fontSize: 15,
    fontWeight: FontWeight.w500,
    color: AppColors.infoBlue,
    fontFamily: fontFamily,
  );
  
  static const TextStyle agentBadge = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w600,
    color: AppColors.lightText,
    fontFamily: fontFamily,
  );
  
  static const TextStyle correctionOriginal = TextStyle(
    color: AppColors.errorRed,
    fontWeight: FontWeight.w500,
  );
  
  static const TextStyle correctionCorrected = TextStyle(
    color: AppColors.successGreen,
    fontWeight: FontWeight.w500,
  );
  
  static const TextStyle suggestionText = TextStyle(
    color: AppColors.infoBlue,
    fontWeight: FontWeight.normal,
  );
  
  static const TextStyle explanationText = TextStyle(
    color: AppColors.warningYellow,
    fontWeight: FontWeight.normal,
  );
  
  // Standard text styles following Flutter Material Design
  static const TextStyle heading1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.lightText,
    fontFamily: fontFamily,
  );

  static const TextStyle heading2 = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: AppColors.lightText,
    fontFamily: fontFamily,
  );

  static const TextStyle heading3 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.lightText,
    fontFamily: fontFamily,
  );

  static const TextStyle bodyText1 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.lightText,
    fontFamily: fontFamily,
  );

  static const TextStyle bodyText2 = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.lightText,
    fontFamily: fontFamily,
  );

  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.disabledText,
    fontFamily: fontFamily,
  );

  static const TextStyle button = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    fontFamily: fontFamily,
  );

  // Button text styles
  static const TextStyle buttonText = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    fontFamily: fontFamily,
  );

  static const TextStyle smallButtonText = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    fontFamily: fontFamily,
  );
  
  // Modal and dialog text styles
  static const TextStyle modalTitle = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: AppColors.lightText,
    fontFamily: fontFamily,
  );
  
  static const TextStyle modalSubtitle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.lightText,
    fontFamily: fontFamily,
  );
  
  // Status and indicator text styles
  static const TextStyle statusText = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    fontFamily: fontFamily,
  );
  
  static const TextStyle captionText = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.disabledText,
    fontFamily: fontFamily,
  );
  
  // Timestamp text style
  static const TextStyle timestampText = TextStyle(
    fontSize: 11,
    fontWeight: FontWeight.normal,
    color: AppColors.disabledText,
    fontFamily: fontFamily,
  );
  
  // Welcome message style
  static const TextStyle welcomeText = TextStyle(
    fontSize: 17,
    fontWeight: FontWeight.normal,
    height: 1.4,
    color: AppColors.aiMessageText,
    fontFamily: fontFamily,
  );
  
  // Private constructor to prevent instantiation
  const AppTextStyles._();
}