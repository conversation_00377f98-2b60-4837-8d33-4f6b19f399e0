import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';

/// Individual setting item widget with consistent styling
class SettingItem extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final VoidCallback? onTap;
  final Widget? trailing;
  final bool enabled;
  final Color? iconColor;
  final bool showBorder;
  
  const SettingItem({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    this.onTap,
    this.trailing,
    this.enabled = true,
    this.iconColor,
    this.showBorder = true,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: enabled ? onTap : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: showBorder
              ? const Border(
                  bottom: BorderSide(
                    color: AppColors.slate600,
                    width: 0.5,
                  ),
                )
              : null,
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: enabled 
                  ? (iconColor ?? AppColors.lightText.withOpacity(0.7))
                  : AppColors.lightText.withOpacity(0.3),
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.messageText.copyWith(
                      fontWeight: FontWeight.w500,
                      color: enabled 
                          ? AppColors.lightText 
                          : AppColors.lightText.withOpacity(0.5),
                    ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      subtitle!,
                      style: AppTextStyles.captionText.copyWith(
                        color: enabled 
                            ? AppColors.lightText.withOpacity(0.7)
                            : AppColors.lightText.withOpacity(0.3),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (trailing != null) trailing!,
          ],
        ),
      ),
    );
  }
}

/// Setting item with navigation arrow
class NavigationSettingItem extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final VoidCallback? onTap;
  final bool enabled;
  final Color? iconColor;
  
  const NavigationSettingItem({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    this.onTap,
    this.enabled = true,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return SettingItem(
      title: title,
      subtitle: subtitle,
      icon: icon,
      onTap: onTap,
      enabled: enabled,
      iconColor: iconColor,
      trailing: Icon(
        Icons.chevron_right,
        color: enabled 
            ? AppColors.lightText.withOpacity(0.7)
            : AppColors.lightText.withOpacity(0.3),
      ),
    );
  }
}

/// Setting item with external link icon
class ExternalLinkSettingItem extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final VoidCallback? onTap;
  final bool enabled;
  final Color? iconColor;
  
  const ExternalLinkSettingItem({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    this.onTap,
    this.enabled = true,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return SettingItem(
      title: title,
      subtitle: subtitle,
      icon: icon,
      onTap: onTap,
      enabled: enabled,
      iconColor: iconColor,
      trailing: Icon(
        Icons.open_in_new,
        color: enabled 
            ? AppColors.lightText.withOpacity(0.7)
            : AppColors.lightText.withOpacity(0.3),
        size: 18,
      ),
    );
  }
}

/// Setting item with info icon
class InfoSettingItem extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final VoidCallback? onTap;
  final bool enabled;
  final Color? iconColor;
  
  const InfoSettingItem({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    this.onTap,
    this.enabled = true,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return SettingItem(
      title: title,
      subtitle: subtitle,
      icon: icon,
      onTap: onTap,
      enabled: enabled,
      iconColor: iconColor,
      trailing: Icon(
        Icons.info_outline,
        color: enabled 
            ? AppColors.infoBlue.withOpacity(0.7)
            : AppColors.lightText.withOpacity(0.3),
        size: 18,
      ),
    );
  }
}

/// Setting item with custom trailing widget
class CustomTrailingSettingItem extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final Widget trailing;
  final VoidCallback? onTap;
  final bool enabled;
  final Color? iconColor;
  
  const CustomTrailingSettingItem({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    required this.trailing,
    this.onTap,
    this.enabled = true,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return SettingItem(
      title: title,
      subtitle: subtitle,
      icon: icon,
      onTap: onTap,
      enabled: enabled,
      iconColor: iconColor,
      trailing: trailing,
    );
  }
}

/// Setting item with badge
class BadgeSettingItem extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final String badgeText;
  final Color badgeColor;
  final VoidCallback? onTap;
  final bool enabled;
  final Color? iconColor;
  
  const BadgeSettingItem({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    required this.badgeText,
    this.badgeColor = AppColors.infoBlue,
    this.onTap,
    this.enabled = true,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return SettingItem(
      title: title,
      subtitle: subtitle,
      icon: icon,
      onTap: onTap,
      enabled: enabled,
      iconColor: iconColor,
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: badgeColor.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: badgeColor.withOpacity(0.5),
            width: 1,
          ),
        ),
        child: Text(
          badgeText,
          style: AppTextStyles.captionText.copyWith(
            color: badgeColor,
            fontSize: 11,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}

/// Setting item with status indicator
class StatusSettingItem extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final SettingStatus status;
  final VoidCallback? onTap;
  final bool enabled;
  final Color? iconColor;
  
  const StatusSettingItem({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    required this.status,
    this.onTap,
    this.enabled = true,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return SettingItem(
      title: title,
      subtitle: subtitle,
      icon: icon,
      onTap: onTap,
      enabled: enabled,
      iconColor: iconColor,
      trailing: Container(
        width: 12,
        height: 12,
        decoration: BoxDecoration(
          color: _getStatusColor(status),
          shape: BoxShape.circle,
        ),
      ),
    );
  }
  
  Color _getStatusColor(SettingStatus status) {
    switch (status) {
      case SettingStatus.active:
        return AppColors.successGreen;
      case SettingStatus.inactive:
        return AppColors.lightText.withOpacity(0.3);
      case SettingStatus.warning:
        return AppColors.warningYellow;
      case SettingStatus.error:
        return AppColors.errorRed;
    }
  }
}

/// Status enumeration for settings
enum SettingStatus {
  active,
  inactive,
  warning,
  error,
}