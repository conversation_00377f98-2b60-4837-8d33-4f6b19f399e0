import { Platform } from 'react-native';
import AudioRecord from 'react-native-audio-record';

interface MobileAudioConfig {
  sampleRate: number;
  channels: number;
  bitsPerSample: number;
  audioSource: number;
  wavFile?: string;
}

export class MobileAudioStreamer {
  private isRecording = false;
  private onAudioData?: (audioData: string) => void;
  private recordingInterval?: NodeJS.Timeout;

  constructor() {
    if (Platform.OS !== 'web') {
      this.initializeAudioRecord();
    }
  }

  private initializeAudioRecord() {
    const options: MobileAudioConfig = {
      sampleRate: 16000,  // Deepgram requirement
      channels: 1,        // Mono
      bitsPerSample: 16,  // 16-bit
      audioSource: 6,     // VOICE_RECOGNITION
    };

    AudioRecord.init(options);
  }

  async startStreaming(onAudioData: (audioData: string) => void): Promise<void> {
    if (Platform.OS === 'web') {
      throw new Error('Use Web Audio API for web platform');
    }

    this.onAudioData = onAudioData;
    this.isRecording = true;

    try {
      // Start recording
      AudioRecord.start();
      console.log('🎤 Mobile audio recording started');

      // Stream audio data in chunks
      this.recordingInterval = setInterval(() => {
        if (this.isRecording) {
          // Get audio data and send to callback
          // Note: react-native-audio-record doesn't support real-time streaming
          // We'll need to use a different approach
        }
      }, 100); // 100ms chunks

    } catch (error) {
      console.error('Failed to start mobile audio recording:', error);
      throw error;
    }
  }

  async stopStreaming(): Promise<string | null> {
    if (Platform.OS === 'web') {
      return null;
    }

    this.isRecording = false;

    if (this.recordingInterval) {
      clearInterval(this.recordingInterval);
      this.recordingInterval = undefined;
    }

    try {
      const audioFile = await AudioRecord.stop();
      console.log('🛑 Mobile audio recording stopped:', audioFile);
      return audioFile;
    } catch (error) {
      console.error('Failed to stop mobile audio recording:', error);
      return null;
    }
  }

  isSupported(): boolean {
    return Platform.OS !== 'web';
  }
}