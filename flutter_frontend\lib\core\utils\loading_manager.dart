import 'package:flutter/material.dart';

/// Loading state manager for handling multiple loading states
class LoadingManager extends ChangeNotifier {
  final Map<String, bool> _loadingStates = {};
  final Map<String, String> _loadingMessages = {};

  /// Check if any operation is loading
  bool get isLoading => _loadingStates.values.any((loading) => loading);

  /// Check if specific operation is loading
  bool isLoadingOperation(String operation) {
    return _loadingStates[operation] ?? false;
  }

  /// Get loading message for specific operation
  String? getLoadingMessage(String operation) {
    return _loadingMessages[operation];
  }

  /// Get all currently loading operations
  List<String> get loadingOperations {
    return _loadingStates.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();
  }

  /// Start loading for specific operation
  void startLoading(String operation, {String? message}) {
    _loadingStates[operation] = true;
    if (message != null) {
      _loadingMessages[operation] = message;
    }
    notifyListeners();
  }

  /// Stop loading for specific operation
  void stopLoading(String operation) {
    _loadingStates[operation] = false;
    _loadingMessages.remove(operation);
    notifyListeners();
  }

  /// Stop all loading operations
  void stopAllLoading() {
    _loadingStates.clear();
    _loadingMessages.clear();
    notifyListeners();
  }

  /// Execute operation with loading state management
  Future<T> executeWithLoading<T>(
    String operation,
    Future<T> Function() task, {
    String? loadingMessage,
  }) async {
    startLoading(operation, message: loadingMessage);
    
    try {
      final result = await task();
      return result;
    } finally {
      stopLoading(operation);
    }
  }
}

/// Loading overlay widget
class LoadingOverlay extends StatelessWidget {
  final bool isLoading;
  final String? message;
  final Widget child;
  final Color? backgroundColor;
  final Color? indicatorColor;

  const LoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.message,
    this.backgroundColor,
    this.indicatorColor,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: backgroundColor ?? Colors.black54,
            child: Center(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          indicatorColor ?? Theme.of(context).primaryColor,
                        ),
                      ),
                      if (message != null) ...[
                        const SizedBox(height: 16),
                        Text(
                          message!,
                          style: const TextStyle(
                            fontSize: 16,
                            fontFamily: 'Inter',
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}

/// Loading button widget
class LoadingButton extends StatelessWidget {
  final bool isLoading;
  final VoidCallback? onPressed;
  final Widget child;
  final Widget? loadingChild;
  final ButtonStyle? style;

  const LoadingButton({
    super.key,
    required this.isLoading,
    required this.onPressed,
    required this.child,
    this.loadingChild,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: style,
      child: isLoading
          ? loadingChild ??
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
          : child,
    );
  }
}

/// Loading state mixin for StatefulWidgets
mixin LoadingStateMixin<T extends StatefulWidget> on State<T> {
  final LoadingManager _loadingManager = LoadingManager();

  LoadingManager get loadingManager => _loadingManager;

  bool get isLoading => _loadingManager.isLoading;

  bool isLoadingOperation(String operation) {
    return _loadingManager.isLoadingOperation(operation);
  }

  void startLoading(String operation, {String? message}) {
    _loadingManager.startLoading(operation, message: message);
    if (mounted) setState(() {});
  }

  void stopLoading(String operation) {
    _loadingManager.stopLoading(operation);
    if (mounted) setState(() {});
  }

  Future<T> executeWithLoading<T>(
    String operation,
    Future<T> Function() task, {
    String? loadingMessage,
  }) async {
    return _loadingManager.executeWithLoading(
      operation,
      task,
      loadingMessage: loadingMessage,
    );
  }

  @override
  void dispose() {
    _loadingManager.dispose();
    super.dispose();
  }
}

/// Progress indicator with message
class ProgressIndicatorWithMessage extends StatelessWidget {
  final String message;
  final double? progress;
  final Color? color;

  const ProgressIndicatorWithMessage({
    super.key,
    required this.message,
    this.progress,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (progress != null)
          LinearProgressIndicator(
            value: progress,
            color: color,
            backgroundColor: color?.withOpacity(0.3),
          )
        else
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? Theme.of(context).primaryColor,
            ),
          ),
        const SizedBox(height: 16),
        Text(
          message,
          style: const TextStyle(
            fontSize: 14,
            fontFamily: 'Inter',
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

/// Skeleton loading widget
class SkeletonLoader extends StatefulWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;

  const SkeletonLoader({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius,
  });

  @override
  State<SkeletonLoader> createState() => _SkeletonLoaderState();
}

class _SkeletonLoaderState extends State<SkeletonLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius ?? BorderRadius.circular(4),
            gradient: LinearGradient(
              colors: [
                Colors.grey[300]!,
                Colors.grey[100]!,
                Colors.grey[300]!,
              ],
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
        );
      },
    );
  }
}