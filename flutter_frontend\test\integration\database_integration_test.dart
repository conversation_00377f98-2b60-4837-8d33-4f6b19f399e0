import 'package:flutter_test/flutter_test.dart';
import 'package:deutschkorrekt_flutter/data/repositories/user_repository.dart';
import 'package:deutschkorrekt_flutter/data/repositories/session_repository.dart';
import 'package:deutschkorrekt_flutter/data/models/user_profile.dart';
import 'package:deutschkorrekt_flutter/data/models/session_data.dart';
import 'package:deutschkorrekt_flutter/core/config/supabase_config.dart';

void main() {
  group('Database Integration Tests', () {
    late UserRepository userRepository;
    late SessionRepository sessionRepository;
    late String testEmail;

    setUpAll(() async {
      // Initialize Supabase for testing
      await SupabaseConfig.initialize();
      
      userRepository = UserRepository();
      sessionRepository = SessionRepository();
      testEmail = 'test_${DateTime.now().millisecondsSinceEpoch}@example.com';
    });

    group('User Repository Integration', () {
      test('should create and retrieve user profile', () async {
        // Create user profile
        final createdProfile = await userRepository.createUserProfile(testEmail);
        
        expect(createdProfile.email, equals(testEmail));
        expect(createdProfile.plan, equals('Trial'));
        expect(createdProfile.maxCredits, equals(20));
        expect(createdProfile.currentCredits, equals(20));

        // Retrieve user profile
        final retrievedProfile = await userRepository.getUserProfile(testEmail);
        
        expect(retrievedProfile, isNotNull);
        expect(retrievedProfile!.email, equals(testEmail));
        expect(retrievedProfile.plan, equals('Trial'));
      });

      test('should update user profile', () async {
        // Get existing profile
        final profile = await userRepository.getUserProfile(testEmail);
        expect(profile, isNotNull);

        // Update profile
        final updatedProfile = profile!.copyWith(
          plan: 'Premium',
          maxCredits: 50,
          currentCredits: 45,
        );

        final result = await userRepository.updateUserProfile(updatedProfile);
        
        expect(result.plan, equals('Premium'));
        expect(result.maxCredits, equals(50));
        expect(result.currentCredits, equals(45));
      });

      test('should consume credits', () async {
        // Get current profile
        final profile = await userRepository.getUserProfile(testEmail);
        expect(profile, isNotNull);
        
        final initialCredits = profile!.currentCredits;

        // Consume a credit
        final updatedProfile = await userRepository.consumeCredit(testEmail);
        
        expect(updatedProfile.currentCredits, equals(initialCredits - 1));
      });

      test('should refresh credits', () async {
        // Refresh credits
        final refreshedProfile = await userRepository.refreshCredits(testEmail);
        
        expect(refreshedProfile.currentCredits, equals(refreshedProfile.maxCredits));
        expect(refreshedProfile.datePlan.day, equals(DateTime.now().day));
      });

      test('should get user statistics', () async {
        final stats = await userRepository.getUserStats(testEmail);
        
        expect(stats, isNotNull);
        expect(stats['email'], equals(testEmail));
        expect(stats['plan'], isNotNull);
        expect(stats['credits_used'], isA<int>());
        expect(stats['credits_remaining'], isA<int>());
        expect(stats['next_refresh'], isA<DateTime>());
      });

      test('should check if user exists', () async {
        final exists = await userRepository.userExists(testEmail);
        expect(exists, isTrue);

        final nonExistentUser = await userRepository.userExists('<EMAIL>');
        expect(nonExistentUser, isFalse);
      });
    });

    group('Session Repository Integration', () {
      test('should log and retrieve sessions', () async {
        // Create test session
        final sessionData = SessionData.create(
          email: testEmail,
          message: 'Test message for integration',
          response: 'Test response for integration',
        );

        // Log session
        final loggedSession = await sessionRepository.logSession(sessionData);
        
        expect(loggedSession.sessionId, isNotNull);
        expect(loggedSession.email, equals(testEmail));
        expect(loggedSession.message, equals('Test message for integration'));
        expect(loggedSession.response, equals('Test response for integration'));

        // Retrieve sessions
        final sessions = await sessionRepository.getUserSessions(testEmail, limit: 10);
        
        expect(sessions, isNotEmpty);
        expect(sessions.first.email, equals(testEmail));
      });

      test('should get session count', () async {
        final count = await sessionRepository.getUserSessionCount(testEmail);
        expect(count, isA<int>());
        expect(count, greaterThan(0)); // Should have at least one session from previous test
      });

      test('should get recent sessions', () async {
        final recentSessions = await sessionRepository.getRecentSessions(testEmail);
        expect(recentSessions, isA<List<SessionData>>());
        
        // All sessions should be from the last 24 hours
        final yesterday = DateTime.now().subtract(const Duration(days: 1));
        for (final session in recentSessions) {
          expect(session.datetime.isAfter(yesterday), isTrue);
        }
      });

      test('should get sessions in date range', () async {
        final now = DateTime.now();
        final startDate = now.subtract(const Duration(hours: 1));
        final endDate = now.add(const Duration(hours: 1));

        final sessionsInRange = await sessionRepository.getUserSessionsInRange(
          testEmail,
          startDate: startDate,
          endDate: endDate,
        );

        expect(sessionsInRange, isA<List<SessionData>>());
        
        // All sessions should be within the specified range
        for (final session in sessionsInRange) {
          expect(session.datetime.isAfter(startDate), isTrue);
          expect(session.datetime.isBefore(endDate), isTrue);
        }
      });

      test('should get session statistics', () async {
        final stats = await sessionRepository.getSessionStats(testEmail);
        
        expect(stats, isNotNull);
        expect(stats['total_sessions'], isA<int>());
        expect(stats['total_message_length'], isA<int>());
        expect(stats['total_response_length'], isA<int>());
        expect(stats['average_message_length'], isA<double>());
        expect(stats['average_response_length'], isA<double>());
        expect(stats['sessions_today'], isA<int>());
        expect(stats['sessions_this_week'], isA<int>());
        expect(stats['sessions_this_month'], isA<int>());
      });

      test('should search sessions', () async {
        // Log a session with specific content
        final searchableSession = SessionData.create(
          email: testEmail,
          message: 'This is a searchable test message',
          response: 'Response to searchable message',
        );

        await sessionRepository.logSession(searchableSession);

        // Search for sessions
        final searchResults = await sessionRepository.searchSessions(testEmail, 'searchable');
        
        expect(searchResults, isNotEmpty);
        expect(searchResults.any((s) => s.message.contains('searchable')), isTrue);
      });

      test('should get daily session counts', () async {
        final dailyCounts = await sessionRepository.getDailySessionCounts(testEmail);
        
        expect(dailyCounts, isA<Map<String, int>>());
        
        // Should have data for today
        final today = DateTime.now();
        final todayKey = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
        expect(dailyCounts.containsKey(todayKey), isTrue);
        expect(dailyCounts[todayKey], greaterThan(0));
      });
    });

    group('Credit Management Integration', () {
      test('should handle credit consumption and refresh cycle', () async {
        // Get initial profile
        final initialProfile = await userRepository.getUserProfile(testEmail);
        expect(initialProfile, isNotNull);

        // Consume multiple credits
        var currentProfile = initialProfile!;
        final creditsToConsume = 3;
        
        for (int i = 0; i < creditsToConsume; i++) {
          currentProfile = await userRepository.consumeCredit(testEmail);
        }

        expect(currentProfile.currentCredits, 
               equals(initialProfile.currentCredits - creditsToConsume));

        // Refresh credits
        final refreshedProfile = await userRepository.refreshCredits(testEmail);
        expect(refreshedProfile.currentCredits, equals(refreshedProfile.maxCredits));
      });

      test('should prevent credit consumption when no credits available', () async {
        // First, consume all credits
        var profile = await userRepository.getUserProfile(testEmail);
        expect(profile, isNotNull);

        // Consume all credits
        while (profile!.currentCredits > 0) {
          profile = await userRepository.consumeCredit(testEmail);
        }

        expect(profile.currentCredits, equals(0));

        // Try to consume another credit - should fail
        expect(
          () => userRepository.consumeCredit(testEmail),
          throwsException,
        );
      });
    });

    group('Data Integrity Tests', () {
      test('should maintain referential integrity between users and sessions', () async {
        // Create a session for the test user
        final sessionData = SessionData.create(
          email: testEmail,
          message: 'Referential integrity test',
          response: 'Testing foreign key relationship',
        );

        final loggedSession = await sessionRepository.logSession(sessionData);
        expect(loggedSession.email, equals(testEmail));

        // Verify the user exists
        final userExists = await userRepository.userExists(testEmail);
        expect(userExists, isTrue);

        // Get sessions for the user
        final sessions = await sessionRepository.getUserSessions(testEmail);
        expect(sessions.any((s) => s.sessionId == loggedSession.sessionId), isTrue);
      });

      test('should handle concurrent credit operations', () async {
        // This test would verify that concurrent credit consumption
        // is handled properly with database transactions
        
        // Get initial credits
        final profile = await userRepository.getUserProfile(testEmail);
        expect(profile, isNotNull);
        
        final initialCredits = profile!.currentCredits;
        
        // Simulate concurrent credit consumption
        final futures = List.generate(2, (index) => 
          userRepository.consumeCredit(testEmail)
        );
        
        try {
          await Future.wait(futures);
          
          // Check final credit count
          final finalProfile = await userRepository.getUserProfile(testEmail);
          expect(finalProfile!.currentCredits, equals(initialCredits - 2));
        } catch (e) {
          // One of the operations should succeed, one might fail
          // This is acceptable behavior for concurrent operations
          print('Concurrent operation result: $e');
        }
      });
    });

    tearDownAll(() async {
      // Clean up test data
      try {
        await userRepository.deleteUserProfile(testEmail);
        await sessionRepository.deleteUserSessions(testEmail);
      } catch (e) {
        print('Cleanup error: $e');
      }
    });
  });
}