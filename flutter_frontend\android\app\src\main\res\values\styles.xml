<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- DeutschKorrekt App Styles -->
    
    <!-- Launch Theme for splash screen -->
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Show launch background drawable until Flutter draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@color/status_bar_color</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>
    
    <!-- Normal Theme for the app after launch -->
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@color/status_bar_color</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>
</resources>
