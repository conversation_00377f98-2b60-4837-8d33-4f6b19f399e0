import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/audio_config.dart';

/// Base settings dialog with consistent styling
class BaseSettingsDialog extends StatelessWidget {
  final String title;
  final Widget content;
  final List<Widget>? actions;
  final EdgeInsets? contentPadding;
  
  const BaseSettingsDialog({
    super.key,
    required this.title,
    required this.content,
    this.actions,
    this.contentPadding,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppColors.slate800,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      title: Text(
        title,
        style: AppTextStyles.modalTitle,
      ),
      content: Container(
        constraints: const BoxConstraints(
          maxWidth: 400,
          maxHeight: 500,
        ),
        child: content,
      ),
      contentPadding: contentPadding ?? const EdgeInsets.fromLTRB(24, 20, 24, 24),
      actions: actions,
    );
  }
}

/// Selection dialog for choosing from a list of options
class SelectionDialog<T> extends StatelessWidget {
  final String title;
  final List<T> options;
  final T selectedValue;
  final String Function(T) getDisplayText;
  final ValueChanged<T> onChanged;
  final String? subtitle;
  
  const SelectionDialog({
    super.key,
    required this.title,
    required this.options,
    required this.selectedValue,
    required this.getDisplayText,
    required this.onChanged,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return BaseSettingsDialog(
      title: title,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (subtitle != null) ...[
            Text(
              subtitle!,
              style: AppTextStyles.captionText,
            ),
            const SizedBox(height: 16),
          ],
          ...options.map((option) => RadioListTile<T>(
            title: Text(
              getDisplayText(option),
              style: AppTextStyles.messageText,
            ),
            value: option,
            groupValue: selectedValue,
            onChanged: (value) {
              if (value != null) {
                onChanged(value);
                Navigator.pop(context);
              }
            },
            activeColor: AppColors.infoBlue,
            contentPadding: EdgeInsets.zero,
          )),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: AppTextStyles.smallButtonText.copyWith(
              color: AppColors.lightText,
            ),
          ),
        ),
      ],
    );
  }
}

/// Multi-selection dialog for choosing multiple options
class MultiSelectionDialog<T> extends StatefulWidget {
  final String title;
  final List<T> options;
  final List<T> selectedValues;
  final String Function(T) getDisplayText;
  final ValueChanged<List<T>> onChanged;
  final String? subtitle;
  final int? maxSelections;
  
  const MultiSelectionDialog({
    super.key,
    required this.title,
    required this.options,
    required this.selectedValues,
    required this.getDisplayText,
    required this.onChanged,
    this.subtitle,
    this.maxSelections,
  });

  @override
  State<MultiSelectionDialog<T>> createState() => _MultiSelectionDialogState<T>();
}

class _MultiSelectionDialogState<T> extends State<MultiSelectionDialog<T>> {
  late List<T> _selectedValues;
  
  @override
  void initState() {
    super.initState();
    _selectedValues = List.from(widget.selectedValues);
  }
  
  @override
  Widget build(BuildContext context) {
    return BaseSettingsDialog(
      title: widget.title,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.subtitle != null) ...[
            Text(
              widget.subtitle!,
              style: AppTextStyles.captionText,
            ),
            const SizedBox(height: 16),
          ],
          if (widget.maxSelections != null) ...[
            Text(
              'Select up to ${widget.maxSelections} options',
              style: AppTextStyles.captionText.copyWith(
                color: AppColors.warningYellow,
              ),
            ),
            const SizedBox(height: 8),
          ],
          ...widget.options.map((option) => CheckboxListTile(
            title: Text(
              widget.getDisplayText(option),
              style: AppTextStyles.messageText,
            ),
            value: _selectedValues.contains(option),
            onChanged: (checked) {
              setState(() {
                if (checked == true) {
                  if (widget.maxSelections == null || 
                      _selectedValues.length < widget.maxSelections!) {
                    _selectedValues.add(option);
                  }
                } else {
                  _selectedValues.remove(option);
                }
              });
            },
            activeColor: AppColors.infoBlue,
            contentPadding: EdgeInsets.zero,
          )),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: AppTextStyles.smallButtonText.copyWith(
              color: AppColors.lightText,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onChanged(_selectedValues);
            Navigator.pop(context);
          },
          child: Text(
            'Save',
            style: AppTextStyles.smallButtonText,
          ),
        ),
      ],
    );
  }
}

/// Audio quality configuration dialog
class AudioQualityDialog extends StatefulWidget {
  final AudioConfig initialConfig;
  final ValueChanged<AudioConfig> onChanged;
  
  const AudioQualityDialog({
    super.key,
    required this.initialConfig,
    required this.onChanged,
  });

  @override
  State<AudioQualityDialog> createState() => _AudioQualityDialogState();
}

class _AudioQualityDialogState extends State<AudioQualityDialog> {
  late AudioConfig _config;
  
  @override
  void initState() {
    super.initState();
    _config = widget.initialConfig;
  }
  
  @override
  Widget build(BuildContext context) {
    return BaseSettingsDialog(
      title: 'Audio Quality Settings',
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Configure audio recording quality settings. Higher quality may use more bandwidth.',
            style: AppTextStyles.captionText,
          ),
          const SizedBox(height: 20),
          
          // Sample rate
          _buildDropdownField(
            label: 'Sample Rate',
            value: _config.sampleRate,
            items: [8000, 16000, 22050, 44100, 48000],
            onChanged: (value) {
              setState(() {
                _config = _config.copyWith(sampleRate: value);
              });
            },
            suffix: 'Hz',
            description: 'Higher sample rates provide better audio quality',
          ),
          
          const SizedBox(height: 16),
          
          // Channels
          _buildDropdownField(
            label: 'Channels',
            value: _config.channels,
            items: [1, 2],
            onChanged: (value) {
              setState(() {
                _config = _config.copyWith(channels: value);
              });
            },
            suffix: _config.channels == 1 ? 'Mono' : 'Stereo',
            description: 'Mono is recommended for speech recognition',
          ),
          
          const SizedBox(height: 16),
          
          // Chunk size
          _buildDropdownField(
            label: 'Chunk Size',
            value: _config.chunkSize,
            items: [512, 1024, 2048, 4096],
            onChanged: (value) {
              setState(() {
                _config = _config.copyWith(chunkSize: value);
              });
            },
            suffix: 'samples',
            description: 'Smaller chunks provide lower latency',
          ),
          
          const SizedBox(height: 20),
          
          // Quality indicator
          _buildQualityIndicator(),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: AppTextStyles.smallButtonText.copyWith(
              color: AppColors.lightText,
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            setState(() {
              _config = AudioConfig.defaultConfig();
            });
          },
          child: Text(
            'Reset',
            style: AppTextStyles.smallButtonText.copyWith(
              color: AppColors.warningYellow,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onChanged(_config);
            Navigator.pop(context);
          },
          child: Text(
            'Save',
            style: AppTextStyles.smallButtonText,
          ),
        ),
      ],
    );
  }
  
  Widget _buildDropdownField({
    required String label,
    required int value,
    required List<int> items,
    required ValueChanged<int> onChanged,
    String? suffix,
    String? description,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.messageText.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        if (description != null) ...[
          const SizedBox(height: 2),
          Text(
            description,
            style: AppTextStyles.captionText,
          ),
        ],
        const SizedBox(height: 8),
        DropdownButtonFormField<int>(
          value: value,
          style: AppTextStyles.messageText,
          dropdownColor: AppColors.slate700,
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.slate600),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.slate600),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.infoBlue),
            ),
          ),
          items: items
              .map((item) => DropdownMenuItem(
                value: item,
                child: Text('$item${suffix != null ? ' $suffix' : ''}'),
              ))
              .toList(),
          onChanged: (newValue) {
            if (newValue != null) {
              onChanged(newValue);
            }
          },
        ),
      ],
    );
  }
  
  Widget _buildQualityIndicator() {
    final quality = _calculateQuality();
    final qualityColor = _getQualityColor(quality);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: qualityColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: qualityColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getQualityIcon(quality),
            color: qualityColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Quality: ${quality.name}',
                  style: AppTextStyles.messageText.copyWith(
                    color: qualityColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  _getQualityDescription(quality),
                  style: AppTextStyles.captionText.copyWith(
                    color: qualityColor.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  AudioQuality _calculateQuality() {
    if (_config.sampleRate >= 44100 && _config.channels == 2) {
      return AudioQuality.high;
    } else if (_config.sampleRate >= 22050) {
      return AudioQuality.medium;
    } else {
      return AudioQuality.low;
    }
  }
  
  Color _getQualityColor(AudioQuality quality) {
    switch (quality) {
      case AudioQuality.low:
        return AppColors.warningYellow;
      case AudioQuality.medium:
        return AppColors.infoBlue;
      case AudioQuality.high:
        return AppColors.successGreen;
    }
  }
  
  IconData _getQualityIcon(AudioQuality quality) {
    switch (quality) {
      case AudioQuality.low:
        return Icons.signal_wifi_statusbar_1_bar;
      case AudioQuality.medium:
        return Icons.signal_wifi_statusbar_2_bar;
      case AudioQuality.high:
        return Icons.signal_wifi_4_bar;
    }
  }
  
  String _getQualityDescription(AudioQuality quality) {
    switch (quality) {
      case AudioQuality.low:
        return 'Basic quality, lower bandwidth usage';
      case AudioQuality.medium:
        return 'Good quality, balanced performance';
      case AudioQuality.high:
        return 'High quality, higher bandwidth usage';
    }
  }
}

/// Confirmation dialog for destructive actions
class ConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final VoidCallback onConfirm;
  final Color confirmColor;
  final IconData? icon;
  
  const ConfirmationDialog({
    super.key,
    required this.title,
    required this.message,
    this.confirmText = 'Confirm',
    this.cancelText = 'Cancel',
    required this.onConfirm,
    this.confirmColor = AppColors.errorRed,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return BaseSettingsDialog(
      title: title,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: 48,
              color: confirmColor,
            ),
            const SizedBox(height: 16),
          ],
          Text(
            message,
            style: AppTextStyles.messageText,
            textAlign: TextAlign.center,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            cancelText,
            style: AppTextStyles.smallButtonText.copyWith(
              color: AppColors.lightText,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
            onConfirm();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: confirmColor,
            foregroundColor: AppColors.white,
          ),
          child: Text(
            confirmText,
            style: AppTextStyles.smallButtonText,
          ),
        ),
      ],
    );
  }
}

/// Information dialog for displaying details
class InfoDialog extends StatelessWidget {
  final String title;
  final String message;
  final String buttonText;
  final IconData? icon;
  final Color? iconColor;
  
  const InfoDialog({
    super.key,
    required this.title,
    required this.message,
    this.buttonText = 'OK',
    this.icon,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return BaseSettingsDialog(
      title: title,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: 48,
              color: iconColor ?? AppColors.infoBlue,
            ),
            const SizedBox(height: 16),
          ],
          Text(
            message,
            style: AppTextStyles.messageText,
            textAlign: TextAlign.center,
          ),
        ],
      ),
      actions: [
        ElevatedButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            buttonText,
            style: AppTextStyles.smallButtonText,
          ),
        ),
      ],
    );
  }
}

/// Audio quality enumeration
enum AudioQuality {
  low,
  medium,
  high,
}