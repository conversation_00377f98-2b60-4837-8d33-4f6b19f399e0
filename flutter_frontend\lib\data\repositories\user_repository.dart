import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/config/supabase_config.dart';
import '../models/user_profile.dart';

/// Repository for managing user profile data
class UserRepository {
  static final SupabaseClient _client = SupabaseConfig.client;
  static const String _tableName = 'users';

  /// Get user profile by email
  Future<UserProfile?> getUserProfile(String email) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('email', email)
          .maybeSingle();

      if (response == null) {
        return null;
      }

      return UserProfile.fromJson(response);
    } catch (e) {
      throw Exception('Failed to get user profile: $e');
    }
  }

  /// Create a new user profile
  Future<UserProfile> createUserProfile(String email) async {
    try {
      final newProfile = UserProfile.createTrial(email);
      
      final response = await _client
          .from(_tableName)
          .insert(newProfile.toJson())
          .select()
          .single();

      return UserProfile.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create user profile: $e');
    }
  }

  /// Update user profile
  Future<UserProfile> updateUserProfile(UserProfile profile) async {
    try {
      final response = await _client
          .from(_tableName)
          .update(profile.toJson())
          .eq('email', profile.email)
          .select()
          .single();

      return UserProfile.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update user profile: $e');
    }
  }

  /// Update user credits
  Future<UserProfile> updateCredits(String email, int newCredits) async {
    try {
      // Ensure credits don't go below 0
      final safeCredits = newCredits < 0 ? 0 : newCredits;
      
      final response = await _client
          .from(_tableName)
          .update({'current_credits': safeCredits})
          .eq('email', email)
          .select()
          .single();

      return UserProfile.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update credits: $e');
    }
  }

  /// Consume one credit (decrement by 1)
  Future<UserProfile> consumeCredit(String email) async {
    try {
      // Use RPC function for atomic credit consumption
      final response = await _client.rpc('consume_credit', params: {
        'user_email': email,
      });

      if (response == null) {
        throw Exception('Failed to consume credit - user not found');
      }

      // Get updated profile
      return await getUserProfile(email) ?? 
             (throw Exception('Failed to get updated profile'));
    } catch (e) {
      // Fallback to manual decrement if RPC doesn't exist
      try {
        final profile = await getUserProfile(email);
        if (profile == null) {
          throw Exception('User profile not found');
        }

        if (profile.currentCredits <= 0) {
          throw Exception('No credits available');
        }

        return await updateCredits(email, profile.currentCredits - 1);
      } catch (fallbackError) {
        throw Exception('Failed to consume credit: $fallbackError');
      }
    }
  }

  /// Refresh user credits (reset to max_credits)
  Future<UserProfile> refreshCredits(String email) async {
    try {
      final profile = await getUserProfile(email);
      if (profile == null) {
        throw Exception('User profile not found');
      }

      final now = DateTime.now();
      final updatedProfile = profile.copyWith(
        currentCredits: profile.maxCredits,
        datePlan: now,
      );

      return await updateUserProfile(updatedProfile);
    } catch (e) {
      throw Exception('Failed to refresh credits: $e');
    }
  }

  /// Check if user exists
  Future<bool> userExists(String email) async {
    try {
      final response = await _client
          .from(_tableName)
          .select('email')
          .eq('email', email)
          .maybeSingle();

      return response != null;
    } catch (e) {
      throw Exception('Failed to check if user exists: $e');
    }
  }

  /// Delete user profile (for GDPR compliance)
  Future<void> deleteUserProfile(String email) async {
    try {
      await _client
          .from(_tableName)
          .delete()
          .eq('email', email);
    } catch (e) {
      throw Exception('Failed to delete user profile: $e');
    }
  }

  /// Get users by plan (for admin purposes)
  Future<List<UserProfile>> getUsersByPlan(String plan) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('plan', plan);

      return response.map<UserProfile>((json) => UserProfile.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to get users by plan: $e');
    }
  }

  /// Get user statistics
  Future<Map<String, dynamic>> getUserStats(String email) async {
    try {
      final profile = await getUserProfile(email);
      if (profile == null) {
        throw Exception('User profile not found');
      }

      return {
        'email': profile.email,
        'plan': profile.plan,
        'credits_used': profile.maxCredits - profile.currentCredits,
        'credits_remaining': profile.currentCredits,
        'credits_percentage': profile.remainingCreditsPercentage,
        'days_since_joined': DateTime.now().difference(profile.dateJoined).inDays,
        'next_refresh': profile.nextRefreshDate,
        'needs_refresh': profile.needsRefresh,
      };
    } catch (e) {
      throw Exception('Failed to get user stats: $e');
    }
  }
}