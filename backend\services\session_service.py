"""
Session management service for audio streaming sessions.
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, Optional, List
import asyncio

from fastapi import WebSocket

from models.data_models import AudioSession
from config.settings import settings

logger = logging.getLogger(__name__)

class SessionService:
    """
    Manages audio streaming sessions and their lifecycle.
    Provides methods for creating, retrieving, and cleaning up sessions.
    """
    
    def __init__(self):
        """Initialize the session service with an empty sessions dictionary."""
        self.sessions: Dict[str, AudioSession] = {}
        self.cleanup_task = None
    
    async def start(self):
        """Start the session cleanup background task."""
        if self.cleanup_task is None:
            self.cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
            logger.info("Session cleanup task started")
    
    async def stop(self):
        """Stop the session cleanup background task."""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
            self.cleanup_task = None
            logger.info("Session cleanup task stopped")
    
    def create_session(self, websocket: WebSocket) -> str:
        """
        Create a new audio session.
        
        Args:
            websocket: The WebSocket connection for this session
            
        Returns:
            str: The session ID
        """
        session_id = str(uuid.uuid4())
        self.sessions[session_id] = AudioSession(
            session_id=session_id,
            websocket=websocket,
            start_time=datetime.now()
        )
        logger.info(f"Created new session: {session_id}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[AudioSession]:
        """
        Get an audio session by ID.
        
        Args:
            session_id: The session ID to retrieve
            
        Returns:
            Optional[AudioSession]: The session if found, None otherwise
        """
        return self.sessions.get(session_id)
    
    def update_session_text(self, session_id: str, text: str) -> bool:
        """
        Update the complete text for a session.
        
        Args:
            session_id: The session ID to update
            text: The complete transcribed text
            
        Returns:
            bool: True if the session was updated, False otherwise
        """
        session = self.get_session(session_id)
        if session:
            session.complete_text = text
            return True
        return False
    
    def close_session(self, session_id: str) -> bool:
        """
        Close and remove a session.
        
        Args:
            session_id: The session ID to close
            
        Returns:
            bool: True if the session was closed, False if not found
        """
        if session_id in self.sessions:
            session = self.sessions[session_id]
            session.is_active = False
            del self.sessions[session_id]
            logger.info(f"Closed session: {session_id}")
            return True
        return False
    
    def get_active_sessions(self) -> List[AudioSession]:
        """
        Get all active sessions.
        
        Returns:
            List[AudioSession]: List of active sessions
        """
        return [session for session in self.sessions.values() if session.is_active]
    
    async def _cleanup_expired_sessions(self):
        """Background task to clean up expired sessions."""
        while True:
            try:
                # Check for sessions that exceed the maximum duration
                current_time = datetime.now()
                expired_sessions = []
                
                for session_id, session in self.sessions.items():
                    # Check if session has exceeded the maximum duration
                    duration = (current_time - session.start_time).total_seconds()
                    if duration > settings.max_recording_duration:
                        expired_sessions.append(session_id)
                
                # Close expired sessions
                for session_id in expired_sessions:
                    logger.info(f"Cleaning up expired session: {session_id}")
                    self.close_session(session_id)
                
                # Sleep for a while before checking again
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except asyncio.CancelledError:
                # Task was cancelled, exit gracefully
                break
            except Exception as e:
                logger.error(f"Error in session cleanup task: {str(e)}", exc_info=True)
                await asyncio.sleep(10)  # Wait a bit longer if there was an error

# Global instance
session_service = SessionService()