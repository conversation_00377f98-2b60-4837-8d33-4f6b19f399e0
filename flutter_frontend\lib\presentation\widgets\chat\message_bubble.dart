import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/gradients.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/message.dart';
import '../common/formatted_text.dart';
import 'audio_icon_widget.dart';
import '../../../data/services/tts_audio_service.dart';

/// Message bubble widget with user/AI differentiation matching Expo version
class MessageBubble extends StatefulWidget {
  final Message message;
  final bool showTimestamp;
  final bool showAvatar;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  
  const MessageBubble({
    super.key,
    required this.message,
    this.showTimestamp = false,
    this.showAvatar = false,
    this.onTap,
    this.onLongPress,
  });

  @override
  State<MessageBubble> createState() => _MessageBubbleState();
}

class _MessageBubbleState extends State<MessageBubble> {
  late TTSAudioService _ttsService;
  AudioIconState _audioIconState = AudioIconState.idle;
  bool _isFirstTime = true;
  final GlobalKey<AudioIconWidgetState> _audioIconKey = GlobalKey<AudioIconWidgetState>();
  
  @override
  void initState() {
    super.initState();
    _ttsService = TTSAudioService();
  }
  
  @override
  void dispose() {
    _ttsService.dispose();
    super.dispose();
  }
  
  /// Helper method to update both local state and audio icon widget state
  void _updateAudioIconState(AudioIconState newState) {
    if (kDebugMode) {
      debugPrint('🎵 MessageBubble: Updating audio icon state to $newState for message ${widget.message.id}');
    }
    
    setState(() {
      _audioIconState = newState;
    });
    
    // Update the audio icon widget's state for animations
    final audioIconState = _audioIconKey.currentState;
    if (audioIconState != null) {
      if (kDebugMode) {
        debugPrint('🎵 MessageBubble: Calling updateState($newState) on audio icon widget - SUCCESS');
      }
      audioIconState.updateState(newState);
    } else {
      if (kDebugMode) {
        debugPrint('🎵 MessageBubble: ⚠️ Audio icon widget state is null, cannot update animation - WIDGET NOT READY');
      }
      // Try again after a short delay if widget isn't ready
      Future.delayed(const Duration(milliseconds: 100), () {
        final retryState = _audioIconKey.currentState;
        if (retryState != null) {
          if (kDebugMode) {
            debugPrint('🎵 MessageBubble: Retry updateState($newState) - SUCCESS');
          }
          retryState.updateState(newState);
        } else {
          if (kDebugMode) {
            debugPrint('🎵 MessageBubble: Retry updateState($newState) - STILL FAILED');
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      child: Row(
        mainAxisAlignment: widget.message.isUser 
            ? MainAxisAlignment.end 
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // AI avatar (left side)
          if (!widget.message.isUser && widget.showAvatar)
            _buildAvatar(),
          
          // Message content
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              child: Column(
                crossAxisAlignment: widget.message.isUser 
                    ? CrossAxisAlignment.end 
                    : CrossAxisAlignment.start,
                children: [
                  // Message bubble
                  _buildMessageBubble(context),
                  
                  // Timestamp
                  if (widget.showTimestamp)
                    _buildTimestamp(),
                ],
              ),
            ),
          ),
          
          // User avatar (right side)
          if (widget.message.isUser && widget.showAvatar)
            _buildAvatar(),
        ],
      ),
    );
  }
  
  /// Build message bubble with appropriate styling
  Widget _buildMessageBubble(BuildContext context) {
    // Check if we should show audio icon (only for AI messages with extracted sentence)
    final shouldShowAudioIcon = !widget.message.isUser && 
                               widget.message.extractedSentence != null && 
                               widget.message.extractedSentence!.isNotEmpty;
    
    // Calculate responsive padding based on screen size
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 400;
    
    // Audio icon dimensions for layout calculations
    const audioIconSize = 20.0;
    const audioIconPadding = 8.0;
    final audioIconSpace = shouldShowAudioIcon ? audioIconSize + (audioIconPadding * 2) : 0.0;
    
    return GestureDetector(
      onTap: widget.onTap,
      onLongPress: widget.onLongPress,
      child: Stack(
        children: [
          Container(
            padding: EdgeInsets.only(
              left: 16,
              right: shouldShowAudioIcon 
                  ? (isSmallScreen ? 44 : 48) // Responsive padding for audio icon
                  : 16,
              top: 12,
              bottom: 12,
            ),
            decoration: BoxDecoration(
              gradient: widget.message.isUser ? AppGradients.userMessageGradient : null,
              color: widget.message.isUser ? null : AppColors.aiMessageBackground,
              border: widget.message.isUser 
                  ? null 
                  : Border.all(color: AppColors.aiMessageBorder, width: 1),
              borderRadius: BorderRadius.circular(18),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowLight,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Streaming indicator
                if (widget.message.isStreaming)
                  _buildStreamingIndicator(),
                
                // Processing indicator
                if (widget.message.isProcessing)
                  _buildProcessingIndicator(),
                
                // Message text with proper constraints
                _buildMessageTextWithConstraints(context, shouldShowAudioIcon),
                
                // Correction results
                if (widget.message.correctionResult != null)
                  _buildCorrectionResult(),
              ],
            ),
          ),
          
          // Audio icon positioned in top-right corner
          if (shouldShowAudioIcon)
            _buildAudioIcon(context),
        ],
      ),
    );
  }
  
  /// Build message text with appropriate formatting
  Widget _buildMessageText() {
    if (widget.message.text.isEmpty) {
      return const SizedBox.shrink();
    }
    
    if (widget.message.isUser) {
      return FormattedTextExtensions.userMessage(
        widget.message.text,
        textAlign: TextAlign.left,
      );
    } else {
      return FormattedTextExtensions.aiMessage(
        widget.message.text,
        textAlign: TextAlign.left,
      );
    }
  }
  
  /// Build message text with proper constraints to avoid overlap with audio icon
  Widget _buildMessageTextWithConstraints(BuildContext context, bool hasAudioIcon) {
    if (widget.message.text.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // Calculate available width considering audio icon space
    final screenWidth = MediaQuery.of(context).size.width;
    final maxBubbleWidth = screenWidth * 0.75;
    final audioIconSpace = hasAudioIcon ? 36.0 : 0.0; // Icon + padding space
    final availableTextWidth = maxBubbleWidth - 32 - audioIconSpace; // Subtract bubble padding
    
    // Use message text directly (already cleaned of braces in Message.ai factory)
    Widget textWidget;
    if (widget.message.isUser) {
      textWidget = FormattedTextExtensions.userMessage(
        widget.message.text, // Text is already clean
        textAlign: TextAlign.left,
      );
    } else {
      textWidget = FormattedTextExtensions.aiMessage(
        widget.message.text, // Text is already clean
        textAlign: TextAlign.left,
      );
    }
    
    // Wrap text widget with constraints to prevent overflow
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: availableTextWidth > 100 ? availableTextWidth : 100, // Minimum width
      ),
      child: textWidget,
    );
  }
  
  /// Build streaming indicator
  Widget _buildStreamingIndicator() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildPulsingDot(0),
          const SizedBox(width: 4),
          _buildPulsingDot(200),
          const SizedBox(width: 4),
          _buildPulsingDot(400),
          const SizedBox(width: 8),
          Text(
            'Live transcription...',
            style: AppTextStyles.streamingIndicator,
          ),
        ],
      ),
    );
  }
  
  /// Build pulsing dot for streaming indicator
  Widget _buildPulsingDot(int delay) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.3, end: 1.0),
      builder: (context, value, child) {
        return AnimatedBuilder(
          animation: AlwaysStoppedAnimation(value),
          builder: (context, child) {
            return Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.infoBlue.withOpacity(value),
              ),
            );
          },
        );
      },
    );
  }
  
  /// Build processing indicator
  Widget _buildProcessingIndicator() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.infoBlue),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '⚡ Processing with AI agents...',
            style: AppTextStyles.processingIndicator,
          ),
        ],
      ),
    );
  }
  
  /// Build correction result display
  Widget _buildCorrectionResult() {
    final correction = widget.message.correctionResult!;
    
    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: AppGradients.correctionContainerGradient,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.slate600.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Agent badge
          _buildAgentBadge(),
          
          const SizedBox(height: 8),
          
          // Original vs Corrected text
          if (correction.hasCorrections)
            _buildCorrectionComparison(correction),
          
          // Suggestions
          if (correction.hasSuggestions)
            _buildSuggestions(correction),
          
          // Explanations
          if (correction.hasExplanations)
            _buildExplanations(correction),
        ],
      ),
    );
  }
  
  /// Build agent badge
  Widget _buildAgentBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        gradient: AppGradients.agentBadgeGradient,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        'German Correction Agent',
        style: AppTextStyles.agentBadge,
      ),
    );
  }
  
  /// Build correction comparison (original vs corrected)
  Widget _buildCorrectionComparison(correction) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Original text
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Original: ',
              style: AppTextStyles.correctionOriginal,
            ),
            Expanded(
              child: FormattedTextExtensions.correctionOriginal(
                correction.originalText,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 4),
        
        // Corrected text
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Corrected: ',
              style: AppTextStyles.correctionCorrected,
            ),
            Expanded(
              child: FormattedTextExtensions.correctionCorrected(
                correction.correctedText,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  /// Build suggestions list
  Widget _buildSuggestions(correction) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8),
        Text(
          'Suggestions:',
          style: AppTextStyles.suggestionText.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        ...correction.suggestions.map((suggestion) => Padding(
          padding: const EdgeInsets.only(left: 8, bottom: 2),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '• ',
                style: AppTextStyles.suggestionText,
              ),
              Expanded(
                child: FormattedTextExtensions.suggestion(suggestion),
              ),
            ],
          ),
        )),
      ],
    );
  }
  
  /// Build explanations list
  Widget _buildExplanations(correction) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8),
        Text(
          'Explanations:',
          style: AppTextStyles.explanationText.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        ...correction.explanations.map((explanation) => Padding(
          padding: const EdgeInsets.only(left: 8, bottom: 2),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '• ',
                style: AppTextStyles.explanationText,
              ),
              Expanded(
                child: FormattedTextExtensions.explanation(explanation),
              ),
            ],
          ),
        )),
      ],
    );
  }
  
  /// Build avatar
  Widget _buildAvatar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: CircleAvatar(
        radius: 16,
        backgroundColor: widget.message.isUser 
            ? AppColors.slate600 
            : AppColors.infoBlue,
        child: Icon(
          widget.message.isUser ? Icons.person : Icons.smart_toy,
          size: 16,
          color: AppColors.white,
        ),
      ),
    );
  }
  
  /// Handle TTS audio playback for first-time interaction
  Future<void> _handleFirstTap() async {
    if (kDebugMode) {
      debugPrint('🎵 MessageBubble: _handleFirstTap called for message ${widget.message.id}');
    }
    
    if (widget.message.extractedSentence == null) return;
    
    _updateAudioIconState(AudioIconState.loading);
    
    try {
      debugPrint('🎤 Starting TTS for message ${widget.message.id}');
      
      final result = await _ttsService.playTTS(
        widget.message.extractedSentence!,
        widget.message.id,
      );
      
      if (result.success) {
        _updateAudioIconState(AudioIconState.playing);
        _isFirstTime = false;
        
        debugPrint('✅ TTS playback started successfully (from ${result.fromCache ? 'cache' : 'backend'})');
        
        // Listen for playback completion to reset state
        _listenForPlaybackCompletion();
        
      } else {
        _updateAudioIconState(AudioIconState.error);
        
        debugPrint('❌ TTS playback failed: ${result.errorMessage}');
        
        // Show user-friendly error message
        if (mounted) {
          _showErrorSnackBar(result.displayMessage);
        }
        
        // Reset to idle state after showing error briefly (only if retry is not available)
        if (!result.canRetry) {
          Future.delayed(const Duration(seconds: 3), () {
            if (mounted) {
              setState(() {
                _audioIconState = AudioIconState.idle;
              });
            }
          });
        }
      }
      
    } catch (e) {
      _updateAudioIconState(AudioIconState.error);
      
      debugPrint('❌ TTS error: $e');
      
      // Show user-friendly error message
      if (mounted) {
        _showErrorSnackBar('Audio playback failed. Please try again.');
      }
      
      // Reset to idle state after showing error briefly
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          _updateAudioIconState(AudioIconState.idle);
        }
      });
    }
  }
  
  /// Handle TTS audio playback for subsequent interactions (cached audio)
  Future<void> _handleSubsequentTap() async {
    if (kDebugMode) {
      debugPrint('🎵 MessageBubble: _handleSubsequentTap called for message ${widget.message.id}');
    }
    
    if (widget.message.extractedSentence == null) return;
    
    // If currently playing, stop playback
    if (_audioIconState == AudioIconState.playing) {
      await _ttsService.stopPlayback();
      _updateAudioIconState(AudioIconState.idle);
      return;
    }
    
    _updateAudioIconState(AudioIconState.loading);
    
    try {
      debugPrint('🎤 Playing cached TTS for message ${widget.message.id}');
      
      final result = await _ttsService.playTTS(
        widget.message.extractedSentence!,
        widget.message.id,
      );
      
      if (result.success) {
        _updateAudioIconState(AudioIconState.playing);
        
        debugPrint('✅ Cached TTS playback started successfully');
        
        // Listen for playback completion to reset state
        _listenForPlaybackCompletion();
        
      } else {
        _updateAudioIconState(AudioIconState.error);
        
        debugPrint('❌ Cached TTS playback failed: ${result.errorMessage}');
        
        // Show user-friendly error message
        if (mounted) {
          _showErrorSnackBar(result.displayMessage);
        }
        
        // Reset to idle state after showing error briefly (only if retry is not available)
        if (!result.canRetry) {
          Future.delayed(const Duration(seconds: 3), () {
            if (mounted) {
              setState(() {
                _audioIconState = AudioIconState.idle;
              });
            }
          });
        }
      }
      
    } catch (e) {
      _updateAudioIconState(AudioIconState.error);
      
      debugPrint('❌ Cached TTS error: $e');
      
      // Show user-friendly error message
      if (mounted) {
        _showErrorSnackBar('Audio playback failed. Please try again.');
      }
      
      // Reset to idle state after showing error briefly
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _audioIconState = AudioIconState.idle;
          });
        }
      });
    }
  }
  
  /// Handle retry functionality when in error state
  Future<void> _handleRetryTap() async {
    debugPrint('🔄 Retrying TTS for message ${widget.message.id}');
    
    try {
      // Clear any cached audio for this message to force fresh request
      await _ttsService.clearCachedAudio(widget.message.id);
      
      // Reset first-time flag to ensure fresh backend request
      _isFirstTime = true;
      
      // Reset error state before retry
      _updateAudioIconState(AudioIconState.idle);
      
      // Add small delay to show state change
      await Future.delayed(const Duration(milliseconds: 200));
      
      // Retry as first-time interaction
      await _handleFirstTap();
      
    } catch (e) {
      debugPrint('❌ Error during TTS retry: $e');
      
      // Log retry error for monitoring
      _logTTSError('RETRY_FAILED', e.toString(), {
        'messageId': widget.message.id,
        'retryAttempt': true,
      });
      
      // Show error state
      _updateAudioIconState(AudioIconState.error);
      
      // Show user-friendly error message
      if (mounted) {
        _showErrorSnackBar('Retry failed. Please try again later.');
      }
    }
  }
  
  /// Log TTS-related errors for debugging and monitoring
  void _logTTSError(String errorCode, String errorMessage, Map<String, dynamic> context) {
    final logEntry = {
      'type': 'TTS_MESSAGE_BUBBLE_ERROR',
      'errorCode': errorCode,
      'errorMessage': errorMessage,
      'messageId': widget.message.id,
      'hasExtractedSentence': widget.message.extractedSentence != null,
      'extractedSentenceLength': widget.message.extractedSentence?.length ?? 0,
      'isFirstTime': _isFirstTime,
      'currentState': _audioIconState.toString(),
      'context': context,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    if (kDebugMode) {
      debugPrint('TTS Message Bubble Error Log: ${json.encode(logEntry)}');
    }
    
    // In production, this could be sent to a logging service
  }
  
  /// Show user-friendly error message via SnackBar
  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.error_outline,
              color: AppColors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.errorRed,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        action: SnackBarAction(
          label: 'Retry',
          textColor: AppColors.white,
          onPressed: _handleRetryTap,
        ),
      ),
    );
  }

  /// Listen for audio playback completion to update icon state
  void _listenForPlaybackCompletion() {
    // Simple polling approach to check if playback is still active
    // This could be enhanced with proper stream listening in the future
    Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      
      if (kDebugMode) {
        debugPrint('🎵 Polling playback status: currentlyPlayingMessageId=${_ttsService.currentlyPlayingMessageId}, thisMessageId=${widget.message.id}, isPlaying=${_ttsService.isPlaying}, currentState=$_audioIconState');
      }
      
      // Check if this message is still the currently playing one
      if (_ttsService.currentlyPlayingMessageId != widget.message.id) {
        // Playback completed or stopped
        if (_audioIconState == AudioIconState.playing) {
          if (kDebugMode) {
            debugPrint('🎵 Playback completed - different message is playing, resetting to idle');
          }
          _updateAudioIconState(AudioIconState.idle);
        }
        timer.cancel();
      }
      
      // Also check if service is no longer playing
      if (!_ttsService.isPlaying && _audioIconState == AudioIconState.playing) {
        if (kDebugMode) {
          debugPrint('🎵 Playback completed - service is no longer playing, resetting to idle');
        }
        _updateAudioIconState(AudioIconState.idle);
        timer.cancel();
      }
    });
  }

  /// Build audio icon positioned in top-right corner
  Widget _buildAudioIcon(BuildContext context) {
    // Calculate responsive positioning based on screen size
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 400;
    
    return Positioned(
      top: -20, // Much higher positioning - well above the text
      right: isSmallScreen ? 8 : 12, // More space from edge
      child: AudioIconWidget(
        key: _audioIconKey, // Use GlobalKey to access widget state
        text: widget.message.extractedSentence!,
        messageId: widget.message.id,
        initialState: AudioIconState.idle, // Always start with idle
        size: isSmallScreen ? 18 : 20, // Smaller icon on small screens
        isFirstTime: _isFirstTime,
        onFirstTap: _handleFirstTap,
        onSubsequentTap: _handleSubsequentTap,
        onRetryTap: _handleRetryTap,
        onLongPress: () {
          debugPrint('TTS: Long press for message ${widget.message.id}');
          // Could implement additional functionality like showing TTS options
        },
        semanticsLabel: 'Play audio for this message',
      ),
    );
  }

  /// Build timestamp
  Widget _buildTimestamp() {
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Text(
        _formatTimestamp(widget.message.timestamp),
        style: AppTextStyles.captionText,
      ),
    );
  }
  
  /// Format timestamp for display
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

/// Animated message bubble that slides in when added
class AnimatedMessageBubble extends StatefulWidget {
  final Message message;
  final bool showTimestamp;
  final bool showAvatar;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Duration animationDuration;
  
  const AnimatedMessageBubble({
    super.key,
    required this.message,
    this.showTimestamp = false,
    this.showAvatar = false,
    this.onTap,
    this.onLongPress,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  State<AnimatedMessageBubble> createState() => _AnimatedMessageBubbleState();
}

class _AnimatedMessageBubbleState extends State<AnimatedMessageBubble>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: Offset(widget.message.isUser ? 1.0 : -1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));
    
    // Start animation
    _controller.forward();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: MessageBubble(
          message: widget.message,
          showTimestamp: widget.showTimestamp,
          showAvatar: widget.showAvatar,
          onTap: widget.onTap,
          onLongPress: widget.onLongPress,
        ),
      ),
    );
  }
}

// CompactMessageBubble removed - was causing confusion with TODO implementation
// Use MessageBubble instead for all message display needs