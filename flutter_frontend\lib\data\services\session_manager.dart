import 'dart:async';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/config/supabase_config.dart';
import '../../core/utils/error_handler.dart';

/// Session management service for handling user sessions and tokens
class SessionManager {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: IOSAccessibility.first_unlock_this_device,
    ),
  );

  static const String _sessionKey = 'user_session';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _accessTokenKey = 'access_token';
  static const String _sessionExpiryKey = 'session_expiry';
  static const String _userIdKey = 'user_id';
  static const String _userEmailKey = 'user_email';

  static Timer? _refreshTimer;
  static StreamSubscription<AuthState>? _authSubscription;

  /// Initialize session management
  static Future<void> initialize() async {
    try {
      // Listen to auth state changes
      _authSubscription = SupabaseConfig.client.auth.onAuthStateChange.listen(
        _handleAuthStateChange,
        onError: (error) {
          ErrorHandler.logError('Auth state change error', error);
        },
      );

      // Check for existing session
      await _restoreSession();
      
      // Start automatic token refresh
      _startTokenRefreshTimer();
    } catch (e) {
      ErrorHandler.logError('Session manager initialization', e);
    }
  }

  /// Handle authentication state changes
  static void _handleAuthStateChange(AuthState authState) async {
    try {
      switch (authState.event) {
        case AuthChangeEvent.signedIn:
          if (authState.session != null) {
            await _storeSession(authState.session!);
            _startTokenRefreshTimer();
          }
          break;
        case AuthChangeEvent.signedOut:
          await _clearSession();
          _stopTokenRefreshTimer();
          break;
        case AuthChangeEvent.tokenRefreshed:
          if (authState.session != null) {
            await _storeSession(authState.session!);
          }
          break;
        case AuthChangeEvent.userUpdated:
          if (authState.session != null) {
            await _storeSession(authState.session!);
          }
          break;
        default:
          break;
      }
    } catch (e) {
      ErrorHandler.logError('Auth state change handling', e);
    }
  }

  /// Store session data securely
  static Future<void> _storeSession(Session session) async {
    try {
      await Future.wait([
        _secureStorage.write(key: _accessTokenKey, value: session.accessToken),
        _secureStorage.write(key: _refreshTokenKey, value: session.refreshToken),
        _secureStorage.write(key: _sessionExpiryKey, value: session.expiresAt.toString()),
        if (session.user.id.isNotEmpty)
          _secureStorage.write(key: _userIdKey, value: session.user.id),
        if (session.user.email != null)
          _secureStorage.write(key: _userEmailKey, value: session.user.email!),
      ]);
    } catch (e) {
      ErrorHandler.logError('Store session', e);
    }
  }

  /// Restore session from secure storage
  static Future<void> _restoreSession() async {
    try {
      final accessToken = await _secureStorage.read(key: _accessTokenKey);
      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);
      
      if (accessToken != null && refreshToken != null) {
        // Check if session is still valid
        final expiryString = await _secureStorage.read(key: _sessionExpiryKey);
        if (expiryString != null) {
          final expiry = DateTime.fromMillisecondsSinceEpoch(int.parse(expiryString) * 1000);
          
          if (DateTime.now().isBefore(expiry)) {
            // Session is still valid, restore it
            await SupabaseConfig.client.auth.setSession(accessToken, refreshToken);
          } else {
            // Session expired, try to refresh
            await _refreshToken();
          }
        }
      }
    } catch (e) {
      ErrorHandler.logError('Restore session', e);
      // Clear invalid session data
      await _clearSession();
    }
  }

  /// Clear session data
  static Future<void> _clearSession() async {
    try {
      await Future.wait([
        _secureStorage.delete(key: _accessTokenKey),
        _secureStorage.delete(key: _refreshTokenKey),
        _secureStorage.delete(key: _sessionExpiryKey),
        _secureStorage.delete(key: _userIdKey),
        _secureStorage.delete(key: _userEmailKey),
      ]);
    } catch (e) {
      ErrorHandler.logError('Clear session', e);
    }
  }

  /// Start automatic token refresh timer
  static void _startTokenRefreshTimer() {
    _stopTokenRefreshTimer(); // Clear any existing timer
    
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      try {
        if (await _shouldRefreshToken()) {
          await _refreshToken();
        }
      } catch (e) {
        ErrorHandler.logError('Token refresh timer', e);
      }
    });
  }

  /// Stop token refresh timer
  static void _stopTokenRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Check if token should be refreshed
  static Future<bool> _shouldRefreshToken() async {
    try {
      final expiryString = await _secureStorage.read(key: _sessionExpiryKey);
      if (expiryString == null) return false;
      
      final expiry = DateTime.fromMillisecondsSinceEpoch(int.parse(expiryString) * 1000);
      final now = DateTime.now();
      
      // Refresh if token expires within 10 minutes
      return expiry.difference(now).inMinutes <= 10;
    } catch (e) {
      ErrorHandler.logError('Check token refresh', e);
      return false;
    }
  }

  /// Refresh authentication token
  static Future<void> _refreshToken() async {
    try {
      final response = await SupabaseConfig.client.auth.refreshSession();
      if (response.session != null) {
        await _storeSession(response.session!);
      }
    } catch (e) {
      ErrorHandler.logError('Token refresh', e);
      // If refresh fails, clear session
      await _clearSession();
    }
  }

  /// Get current session information
  static Future<SessionInfo?> getCurrentSession() async {
    try {
      final accessToken = await _secureStorage.read(key: _accessTokenKey);
      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);
      final expiryString = await _secureStorage.read(key: _sessionExpiryKey);
      final userId = await _secureStorage.read(key: _userIdKey);
      final userEmail = await _secureStorage.read(key: _userEmailKey);
      
      if (accessToken == null || refreshToken == null || expiryString == null) {
        return null;
      }
      
      final expiry = DateTime.fromMillisecondsSinceEpoch(int.parse(expiryString) * 1000);
      
      return SessionInfo(
        accessToken: accessToken,
        refreshToken: refreshToken,
        expiresAt: expiry,
        userId: userId,
        userEmail: userEmail,
        isValid: DateTime.now().isBefore(expiry),
      );
    } catch (e) {
      ErrorHandler.logError('Get current session', e);
      return null;
    }
  }

  /// Check if user is authenticated
  static Future<bool> isAuthenticated() async {
    try {
      final session = await getCurrentSession();
      return session?.isValid ?? false;
    } catch (e) {
      ErrorHandler.logError('Check authentication', e);
      return false;
    }
  }

  /// Get time until session expires
  static Future<Duration?> getTimeUntilExpiry() async {
    try {
      final session = await getCurrentSession();
      if (session == null || !session.isValid) return null;
      
      return session.expiresAt.difference(DateTime.now());
    } catch (e) {
      ErrorHandler.logError('Get time until expiry', e);
      return null;
    }
  }

  /// Force logout and clear all session data
  static Future<void> logout() async {
    try {
      // Sign out from Supabase
      await SupabaseConfig.client.auth.signOut();
      
      // Clear local session data
      await _clearSession();
      
      // Stop refresh timer
      _stopTokenRefreshTimer();
    } catch (e) {
      ErrorHandler.logError('Logout', e);
      // Even if Supabase signout fails, clear local data
      await _clearSession();
      _stopTokenRefreshTimer();
    }
  }

  /// Get session statistics
  static Future<SessionStats> getSessionStats() async {
    try {
      final session = await getCurrentSession();
      if (session == null) {
        return SessionStats(
          isAuthenticated: false,
          timeUntilExpiry: null,
          userId: null,
          userEmail: null,
          sessionAge: null,
        );
      }
      
      final timeUntilExpiry = session.isValid 
          ? session.expiresAt.difference(DateTime.now())
          : null;
      
      // Calculate session age (approximate)
      final sessionAge = Duration(hours: 24) - (timeUntilExpiry ?? Duration.zero);
      
      return SessionStats(
        isAuthenticated: session.isValid,
        timeUntilExpiry: timeUntilExpiry,
        userId: session.userId,
        userEmail: session.userEmail,
        sessionAge: sessionAge,
      );
    } catch (e) {
      ErrorHandler.logError('Get session stats', e);
      return SessionStats(
        isAuthenticated: false,
        timeUntilExpiry: null,
        userId: null,
        userEmail: null,
        sessionAge: null,
      );
    }
  }

  /// Dispose session manager
  static void dispose() {
    _stopTokenRefreshTimer();
    _authSubscription?.cancel();
    _authSubscription = null;
  }

  /// Clear all stored data (for testing or reset)
  static Future<void> clearAllData() async {
    try {
      await _secureStorage.deleteAll();
      _stopTokenRefreshTimer();
    } catch (e) {
      ErrorHandler.logError('Clear all data', e);
    }
  }

  /// Validate session integrity
  static Future<bool> validateSession() async {
    try {
      final session = await getCurrentSession();
      if (session == null || !session.isValid) {
        return false;
      }
      
      // Try to make a simple authenticated request to validate
      final user = SupabaseConfig.client.auth.currentUser;
      return user != null && user.id == session.userId;
    } catch (e) {
      ErrorHandler.logError('Validate session', e);
      return false;
    }
  }
}

/// Session information model
class SessionInfo {
  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;
  final String? userId;
  final String? userEmail;
  final bool isValid;

  const SessionInfo({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
    this.userId,
    this.userEmail,
    required this.isValid,
  });

  /// Get time until expiry
  Duration get timeUntilExpiry => expiresAt.difference(DateTime.now());

  /// Check if session will expire soon (within 10 minutes)
  bool get willExpireSoon => timeUntilExpiry.inMinutes <= 10;

  /// Get formatted expiry time
  String get formattedExpiryTime {
    if (!isValid) return 'Expired';
    
    final duration = timeUntilExpiry;
    if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else {
      return '${duration.inMinutes}m';
    }
  }
}

/// Session statistics model
class SessionStats {
  final bool isAuthenticated;
  final Duration? timeUntilExpiry;
  final String? userId;
  final String? userEmail;
  final Duration? sessionAge;

  const SessionStats({
    required this.isAuthenticated,
    this.timeUntilExpiry,
    this.userId,
    this.userEmail,
    this.sessionAge,
  });

  /// Get formatted session age
  String get formattedSessionAge {
    if (sessionAge == null) return 'Unknown';
    
    final duration = sessionAge!;
    if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else {
      return '${duration.inMinutes}m';
    }
  }

  /// Get formatted time until expiry
  String get formattedTimeUntilExpiry {
    if (timeUntilExpiry == null) return 'Unknown';
    
    final duration = timeUntilExpiry!;
    if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else {
      return '${duration.inMinutes}m';
    }
  }
}