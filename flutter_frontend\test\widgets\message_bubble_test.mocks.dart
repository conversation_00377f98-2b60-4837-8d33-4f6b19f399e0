// Mocks generated by Mockito 5.4.6 from annotations
// in deutschkorrekt_flutter/test/widgets/message_bubble_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:deutschkorrekt_flutter/data/services/tts_audio_service.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTTSPlaybackResult_0 extends _i1.SmartFake
    implements _i2.TTSPlaybackResult {
  _FakeTTSPlaybackResult_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [TTSAudioService].
///
/// See the documentation for Mockito's code generation for more information.
class MockTTSAudioService extends _i1.Mock implements _i2.TTSAudioService {
  MockTTSAudioService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isPlaying => (super.noSuchMethod(
        Invocation.getter(#isPlaying),
        returnValue: false,
      ) as bool);

  @override
  _i3.Future<_i2.TTSPlaybackResult> playTTS(
    String? text,
    String? messageId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #playTTS,
          [
            text,
            messageId,
          ],
        ),
        returnValue:
            _i3.Future<_i2.TTSPlaybackResult>.value(_FakeTTSPlaybackResult_0(
          this,
          Invocation.method(
            #playTTS,
            [
              text,
              messageId,
            ],
          ),
        )),
      ) as _i3.Future<_i2.TTSPlaybackResult>);

  @override
  _i3.Future<void> stopPlayback() => (super.noSuchMethod(
        Invocation.method(
          #stopPlayback,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> clearCachedAudio(String? messageId) => (super.noSuchMethod(
        Invocation.method(
          #clearCachedAudio,
          [messageId],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> clearAllCachedAudio() => (super.noSuchMethod(
        Invocation.method(
          #clearAllCachedAudio,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<Map<String, dynamic>> onNewGroqResponse() => (super.noSuchMethod(
        Invocation.method(
          #onNewGroqResponse,
          [],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getServiceStats() => (super.noSuchMethod(
        Invocation.method(
          #getServiceStats,
          [],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
