"""
Deepgram Streaming Service
Implements the official Deepgram WebSocket API according to:
- https://developers.deepgram.com/reference/streaming-api
- https://github.com/deepgram/deepgram-python-sdk
- https://github.com/deepgram-starters/flask-live-transcription

This is the main STT service for the application.
"""

import logging
import asyncio
import time
import json
import uuid
from typing import Dict, Optional, Any
from fastapi import WebSocket, WebSocketDisconnect
from deepgram import DeepgramClient, LiveTranscriptionEvents, LiveOptions

logger = logging.getLogger(__name__)

class DeepgramService:
    """
    Deepgram Streaming Service using official Python SDK.
    Follows the official API specification exactly.

    Audio Requirements (Official Spec):
    - PCM16 encoding
    - Sample rate: 16000 Hz
    - Single-channel
    - Real-time streaming chunks
    """

    def __init__(self, api_key: str, max_duration: int = 15, enable_multilingual: bool = False):  # Match settings
        self.api_key = api_key
        self.max_duration = max_duration
        self.enable_multilingual = enable_multilingual
        self.sessions: Dict[str, Dict[str, Any]] = {}
        self.deepgram_client = DeepgramClient(api_key)

        # Audio format constants (official spec)
        self.SAMPLE_RATE = 16000
        self.ENCODING = "linear16"  # PCM16 for Deepgram
        self.CHANNELS = 1  # Single-channel

        # Language configuration for German conversation practice
        if enable_multilingual:
            # Multilingual mode: Handles German + English code-switching
            self.LANGUAGE = "multi"  # Nova-3 multilingual mode
            self.MODEL = "nova-3"    # Required for multilingual
        else:
            # German-only mode: Best accuracy for pure German with Nova-3
            self.LANGUAGE = "de"     # German language
            self.MODEL = "nova-3"    # Nova-3 has the best German support
        
    async def create_session(self, client_websocket: WebSocket, client_id: str = None) -> str:
        """
        Create a new streaming session with Deepgram.
        Uses the official Python SDK following the documented pattern.
        """
        session_id = str(uuid.uuid4())
        logger.info(f"🚀 Creating Deepgram session: {session_id}")

        try:
            # Create Deepgram WebSocket connection using the correct API
            logger.info(f"🔧 Configuring Deepgram with model={self.MODEL}, language={self.LANGUAGE}")
            logger.info(f"🔑 API key length: {len(self.api_key) if self.api_key else 0}")

            # Configure Nova-3 with disabled endpointing for manual control
            options = LiveOptions(
                model="nova-3",
                language="multi",
                encoding=self.ENCODING,
                sample_rate=self.SAMPLE_RATE,
                channels=self.CHANNELS,
                interim_results=True,
                utterance_end_ms=3000,        # 3 seconds between words (cost-effective)
                endpointing=False,            # Disable automatic endpointing - manual control
            )
            logger.info(f"📋 LiveOptions configured: model=nova-3, language=multi")

            logger.info("🚀 Creating Deepgram websocket connection...")
            # Use the exact same pattern that worked for Nova-2
            connection = self.deepgram_client.listen.websocket.v("1")
            logger.info("✅ Deepgram websocket connection created successfully")

            # Store session information first
            self.sessions[session_id] = {
                "client_ws": client_websocket,
                "deepgram_connection": connection,
                "client_id": client_id,  # Store client_id for Groq processing
                "start_time": time.time(),
                "is_active": True,
                "final_transcripts": [],      # Array of final transcript chunks
                "current_partial": "",        # Current partial transcript
                "last_sent_text": ""          # Last text sent to client
            }

            # Set up event handlers using the correct pattern
            self._setup_deepgram_handlers(session_id, connection)

            # Start the connection (same pattern that worked for Nova-2)
            logger.info("🔌 Starting Deepgram connection...")

            if connection.start(options) is False:
                raise Exception("Failed to start Deepgram connection - likely API key issue")
            logger.info("✅ Deepgram connection started successfully")

            # Timeout is handled by WebSocket endpoint, not here

            # Send session started message to client
            await client_websocket.send_json({
                "message_type": "session_started",
                "data": {"session_id": session_id}
            })

            logger.info("✅ Connected to Deepgram WebSocket")
            return session_id

        except Exception as e:
            logger.error(f"❌ Failed to create Deepgram session: {str(e)}")
            # Clean up session if it was created
            if session_id in self.sessions:
                del self.sessions[session_id]
            raise
    
    def _setup_deepgram_handlers(self, session_id: str, connection):
        """
        Set up event handlers for Deepgram WebSocket connection.
        Following the official SDK pattern from the documentation.
        """
        session = self.sessions.get(session_id)
        if not session:
            return

        client_ws = session["client_ws"]



        # Get the current event loop to use in handlers
        loop = asyncio.get_event_loop()

        # Define event handlers with correct signatures and thread-safe async calls
        def on_open_handler(self_param, open, **kwargs):
            logger.info(f"🎯 Deepgram connection opened for session {session_id}")
            # Use call_soon_threadsafe to schedule coroutine from another thread
            asyncio.run_coroutine_threadsafe(
                client_ws.send_json({
                    "message_type": "info",
                    "data": {"message": "Connected to Deepgram streaming service"}
                }), loop
            )

        def on_transcript_handler(self_param, result, **kwargs):
            logger.info(f"📨 Deepgram transcript received for session {session_id}")
            # Use call_soon_threadsafe to schedule coroutine from another thread
            asyncio.run_coroutine_threadsafe(
                self._handle_deepgram_transcript(session_id, result), loop
            )

        def on_metadata_handler(self_param, metadata, **kwargs):
            logger.info(f"📊 Deepgram metadata for session {session_id}: {metadata}")

        def on_error_handler(self_param, error, **kwargs):
            logger.error(f"❌ Deepgram error for session {session_id}: {error}")
            # Use call_soon_threadsafe to schedule coroutine from another thread
            asyncio.run_coroutine_threadsafe(
                client_ws.send_json({
                    "message_type": "error",
                    "data": {"message": f"Deepgram error: {error}"}
                }), loop
            )

        def on_close_handler(self_param, close, **kwargs):
            logger.info(f"🔌 Deepgram connection closed for session {session_id}")
            if session_id in self.sessions:
                self.sessions[session_id]["is_active"] = False

        # Register event handlers using the .on() method
        connection.on(LiveTranscriptionEvents.Open, on_open_handler)
        connection.on(LiveTranscriptionEvents.Transcript, on_transcript_handler)
        connection.on(LiveTranscriptionEvents.Metadata, on_metadata_handler)
        connection.on(LiveTranscriptionEvents.Error, on_error_handler)
        connection.on(LiveTranscriptionEvents.Close, on_close_handler)
    
    async def _handle_deepgram_transcript(self, session_id: str, result):
        """
        Handle transcript results from Deepgram with enterprise-grade deduplication.
        """
        session = self.sessions.get(session_id)
        if not session or not session["is_active"]:
            return

        client_ws = session["client_ws"]

        try:
            # Extract transcript data from Deepgram result
            if hasattr(result, 'channel') and result.channel.alternatives:
                alternative = result.channel.alternatives[0]
                transcript = alternative.transcript.strip()
                confidence = alternative.confidence
                is_final = result.is_final

                logger.info(f"🎯 Deepgram transcript: '{transcript}' (final: {is_final}, confidence: {confidence})")

                if not transcript:
                    return

                if is_final:
                    # Add to final transcripts array - Deepgram tells us this chunk is done!
                    session["final_transcripts"].append(transcript)
                    logger.info(f"✅ Final transcript chunk added: '{transcript}'")
                    logger.info(f"📝 Total final chunks: {len(session['final_transcripts'])}")
                    
                    # Clear current partial since this chunk is now final
                    session["current_partial"] = ""
                    return

                # Handle partial transcript - just store it, don't try to be clever
                session["current_partial"] = transcript
                
                # Build complete text: final_transcripts + current_partial
                final_parts = session["final_transcripts"]
                current_partial = session["current_partial"]
                
                if final_parts and current_partial:
                    complete_text = " ".join(final_parts) + " " + current_partial
                elif final_parts:
                    complete_text = " ".join(final_parts)
                elif current_partial:
                    complete_text = current_partial
                else:
                    complete_text = ""
                
                complete_text = complete_text.strip()
                
                # Only send if there's new content
                if complete_text != session["last_sent_text"]:
                    session["last_sent_text"] = complete_text
                    
                    # Convert words to serializable format
                    words_data = []
                    if hasattr(alternative, 'words') and alternative.words:
                        for word in alternative.words:
                            words_data.append({
                                "word": word.word,
                                "start": word.start,
                                "end": word.end,
                                "confidence": word.confidence
                            })

                    await client_ws.send_json({
                        "message_type": "partial_transcript",
                        "data": {
                            "text": complete_text,
                            "confidence": confidence,
                            "words": words_data
                        }
                    })
                    
                    logger.info(f"📤 Sent complete text: '{complete_text}'")

        except Exception as e:
            logger.error(f"❌ Error handling Deepgram transcript: {e}")
    

    
    async def send_audio_data(self, session_id: str, audio_data: bytes) -> bool:
        """
        Send audio data to Deepgram.
        Audio should be PCM16, single-channel, 16kHz.
        """
        session = self.sessions.get(session_id)
        if not session or not session["is_active"]:
            return False

        try:
            connection = session["deepgram_connection"]
            if len(audio_data) > 0:
                # Validate that we have proper PCM16 data
                if len(audio_data) % 2 != 0:
                    logger.error(f"❌ Invalid PCM16 data: {len(audio_data)} bytes (must be even for 16-bit samples)")
                    return False

                # Send audio data to Deepgram
                connection.send(audio_data)
                logger.debug(f"📤 Sent {len(audio_data)} bytes to Deepgram")
                return True
            return True
        except Exception as e:
            logger.error(f"❌ Error sending audio data to Deepgram: {e}")
            return False

    async def manual_stop_session(self, session_id: str) -> str:
        """
        Manually stop session and process current partial transcript with Groq.
        """
        session = self.sessions.get(session_id)
        if not session:
            return ""

        try:
            # Get complete transcript using the simple approach: final_transcripts + current_partial
            final_parts = session.get("final_transcripts", [])
            current_partial = session.get("current_partial", "").strip()
            
            # Build complete transcript
            if final_parts and current_partial:
                current_transcript = " ".join(final_parts) + " " + current_partial
            elif final_parts:
                current_transcript = " ".join(final_parts)
            elif current_partial:
                current_transcript = current_partial
            else:
                current_transcript = ""
            
            current_transcript = current_transcript.strip()

            logger.info(f"🛑 Manual stop - final transcript: '{current_transcript}'")

            if current_transcript:
                # Send processing message to client
                client_ws = session["client_ws"]
                logger.info("📤 Sending processing message to client...")
                await client_ws.send_json({
                    "message_type": "processing",
                    "data": {"message": "Processing with Groq..."}
                })
                logger.info("✅ Processing message sent")

                # Process with Groq via language service
                logger.info(f"🤖 Starting Groq processing for: '{current_transcript[:50]}...'")
                from .language_service import language_service
                groq_result = await language_service.process_text(current_transcript)
                logger.info(f"✅ Groq processing completed, response length: {len(str(groq_result))}")

                # Send Groq response to client
                logger.info("📤 Sending Groq response to client...")
                await client_ws.send_json({
                    "message_type": "groq_response",
                    "data": {
                        "original_text": groq_result["original_text"],
                        "response_text": groq_result["processed_content"]["response_text"],
                        "processing_time": groq_result["processing_time"]
                    }
                })
                logger.info("✅ Groq response sent successfully")

                logger.info(f"✅ Manual stop processed: '{current_transcript}' -> Groq response sent")

            # Clean up session
            await self.finalize_session(session_id)
            return current_transcript

        except Exception as e:
            logger.error(f"❌ Error in manual stop for session {session_id}: {e}")
            logger.error(f"❌ Exception details: {type(e).__name__}: {str(e)}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")

            # Try to send error message to client
            try:
                client_ws = session.get("client_ws")
                if client_ws:
                    await client_ws.send_json({
                        "message_type": "error",
                        "data": {"message": f"Error processing transcript: {str(e)}"}
                    })
            except Exception as send_error:
                logger.error(f"❌ Failed to send error message: {send_error}")

            await self.finalize_session(session_id)
            return session.get("running_transcript", "")

    async def finalize_session(self, session_id: str) -> str:
        """
        Clean up session resources (auto-stop only, no manual processing).
        """
        session = self.sessions.get(session_id)
        if not session:
            return ""

        try:
            # Close Deepgram connection
            connection = session["deepgram_connection"]
            if connection:
                connection.finish()
                logger.info("📤 Closed Deepgram connection")

            # Mark session as inactive
            session["is_active"] = False

            # Get final text for return (but don't process - auto-stop already handled it)
            complete_text = session.get("running_transcript", "")
            logger.info(f"🎯 Session {session_id} cleaned up (auto-stop only)")

            return complete_text

        except Exception as e:
            logger.error(f"❌ Error finalizing session {session_id}: {e}")
            session["is_active"] = False
            return session.get("running_transcript", "")

    async def _session_timeout(self, session_id: str):
        """
        Handle session timeout (cleanup only - auto-stop handles processing).
        """
        await asyncio.sleep(self.max_duration)

        session = self.sessions.get(session_id)
        if session and session["is_active"]:
            logger.info(f"⏰ Session {session_id} timed out after {self.max_duration} seconds")
            
            # Just clean up session (auto-stop already handled Groq processing if needed)
            complete_text = await self.finalize_session(session_id)

            # Send timeout message to client
            client_ws = session["client_ws"]
            try:
                await client_ws.send_json({
                    "message_type": "timeout",
                    "data": {
                        "message": f"Session limit of {self.max_duration} seconds reached",
                        "complete_text": complete_text
                    }
                })
            except Exception as e:
                logger.error(f"❌ Error sending timeout message: {e}")

    # Backward compatibility methods
    async def start_streaming_session(self, client_websocket: WebSocket) -> str:
        """
        Backward compatibility method for start_streaming_session.
        Calls create_session internally.
        """
        return await self.create_session(client_websocket)

    async def process_audio_chunk(self, session_id: str, audio_data: bytes) -> bool:
        """
        Backward compatibility method for process_audio_chunk.
        Calls send_audio_data internally.
        """
        return await self.send_audio_data(session_id, audio_data)

    async def finalize_transcription(self, session_id: str) -> str:
        """
        Backward compatibility method for finalize_transcription.
        Calls finalize_session internally.
        """
        return await self.finalize_session(session_id)
