import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/gradients.dart';
import '../../../data/models/correction_result.dart';
import 'correction_result_widget.dart';

/// Animated wrapper for correction result widgets
class AnimatedCorrectionResult extends StatefulWidget {
  final CorrectionResult correctionResult;
  final bool showAgentBadge;
  final bool showProcessingTime;
  final Duration animationDuration;
  final Curve animationCurve;
  final VoidCallback? onTap;
  
  const AnimatedCorrectionResult({
    super.key,
    required this.correctionResult,
    this.showAgentBadge = true,
    this.showProcessingTime = false,
    this.animationDuration = const Duration(milliseconds: 500),
    this.animationCurve = Curves.easeInOut,
    this.onTap,
  });

  @override
  State<AnimatedCorrectionResult> createState() => _AnimatedCorrectionResultState();
}

class _AnimatedCorrectionResultState extends State<AnimatedCorrectionResult>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _slideController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    // Initialize animations
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: widget.animationCurve,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: widget.animationCurve,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
    
    // Start entrance animation
    _startEntranceAnimation();
  }
  
  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }
  
  void _startEntranceAnimation() async {
    await Future.delayed(const Duration(milliseconds: 100));
    if (mounted) {
      _slideController.forward();
      _fadeController.forward();
    }
  }
  
  void _onTapDown(TapDownDetails details) {
    _scaleController.forward();
  }
  
  void _onTapUp(TapUpDetails details) {
    _scaleController.reverse();
    widget.onTap?.call();
  }
  
  void _onTapCancel() {
    _scaleController.reverse();
  }
  
  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            child: CorrectionResultWidget(
              correctionResult: widget.correctionResult,
              showAgentBadge: widget.showAgentBadge,
              showProcessingTime: widget.showProcessingTime,
            ),
          ),
        ),
      ),
    );
  }
}

/// Staggered animation for multiple correction results
class StaggeredCorrectionResults extends StatefulWidget {
  final List<CorrectionResult> corrections;
  final Duration staggerDelay;
  final Duration itemDuration;
  final bool showAgentBadges;
  final bool showProcessingTimes;
  final Function(CorrectionResult)? onCorrectionTap;
  
  const StaggeredCorrectionResults({
    super.key,
    required this.corrections,
    this.staggerDelay = const Duration(milliseconds: 150),
    this.itemDuration = const Duration(milliseconds: 500),
    this.showAgentBadges = true,
    this.showProcessingTimes = false,
    this.onCorrectionTap,
  });

  @override
  State<StaggeredCorrectionResults> createState() => _StaggeredCorrectionResultsState();
}

class _StaggeredCorrectionResultsState extends State<StaggeredCorrectionResults>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<Offset>> _slideAnimations;
  late List<Animation<double>> _fadeAnimations;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startStaggeredAnimation();
  }
  
  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }
  
  void _initializeAnimations() {
    _controllers = List.generate(
      widget.corrections.length,
      (index) => AnimationController(
        duration: widget.itemDuration,
        vsync: this,
      ),
    );
    
    _slideAnimations = _controllers.map((controller) {
      return Tween<Offset>(
        begin: const Offset(0.0, 1.0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeOutBack,
      ));
    }).toList();
    
    _fadeAnimations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeIn,
      ));
    }).toList();
  }
  
  void _startStaggeredAnimation() async {
    for (int i = 0; i < _controllers.length; i++) {
      if (mounted) {
        _controllers[i].forward();
        await Future.delayed(widget.staggerDelay);
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: widget.corrections.asMap().entries.map((entry) {
        final index = entry.key;
        final correction = entry.value;
        
        return SlideTransition(
          position: _slideAnimations[index],
          child: FadeTransition(
            opacity: _fadeAnimations[index],
            child: Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: CorrectionResultWidget(
                correctionResult: correction,
                showAgentBadge: widget.showAgentBadges,
                showProcessingTime: widget.showProcessingTimes,
                onTap: widget.onCorrectionTap != null
                    ? () => widget.onCorrectionTap!(correction)
                    : null,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}

/// Pulsing animation for processing corrections
class PulsingCorrectionIndicator extends StatefulWidget {
  final String text;
  final Color color;
  final double size;
  
  const PulsingCorrectionIndicator({
    super.key,
    this.text = '⚡ Processing with AI agents...',
    this.color = AppColors.infoBlue,
    this.size = 16.0,
  });

  @override
  State<PulsingCorrectionIndicator> createState() => _PulsingCorrectionIndicatorState();
}

class _PulsingCorrectionIndicatorState extends State<PulsingCorrectionIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _controller.repeat(reverse: true);
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: widget.color.withOpacity(_animation.value * 0.2),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: widget.color.withOpacity(_animation.value),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.psychology,
                size: widget.size,
                color: widget.color.withOpacity(_animation.value),
              ),
              const SizedBox(width: 8),
              Text(
                widget.text,
                style: TextStyle(
                  color: widget.color.withOpacity(_animation.value),
                  fontSize: widget.size - 2,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Shimmer loading effect for correction results
class CorrectionResultShimmer extends StatefulWidget {
  final int itemCount;
  final double height;
  
  const CorrectionResultShimmer({
    super.key,
    this.itemCount = 3,
    this.height = 120.0,
  });

  @override
  State<CorrectionResultShimmer> createState() => _CorrectionResultShimmerState();
}

class _CorrectionResultShimmerState extends State<CorrectionResultShimmer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _controller.repeat();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(widget.itemCount, (index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Container(
                height: widget.height,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    stops: [
                      (_animation.value - 1).clamp(0.0, 1.0),
                      _animation.value.clamp(0.0, 1.0),
                      (_animation.value + 1).clamp(0.0, 1.0),
                    ],
                    colors: [
                      AppColors.slate800.withOpacity(0.3),
                      AppColors.slate700.withOpacity(0.5),
                      AppColors.slate800.withOpacity(0.3),
                    ],
                  ),
                ),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Shimmer badge
                      Container(
                        width: 120,
                        height: 20,
                        decoration: BoxDecoration(
                          color: AppColors.slate600.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      const SizedBox(height: 12),
                      // Shimmer text lines
                      Container(
                        width: double.infinity,
                        height: 16,
                        decoration: BoxDecoration(
                          color: AppColors.slate600.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: MediaQuery.of(context).size.width * 0.7,
                        height: 16,
                        decoration: BoxDecoration(
                          color: AppColors.slate600.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: MediaQuery.of(context).size.width * 0.5,
                        height: 16,
                        decoration: BoxDecoration(
                          color: AppColors.slate600.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      }),
    );
  }
}

/// Bouncing animation for correction success
class BouncingCorrectionSuccess extends StatefulWidget {
  final Widget child;
  final Duration duration;
  
  const BouncingCorrectionSuccess({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 600),
  });

  @override
  State<BouncingCorrectionSuccess> createState() => _BouncingCorrectionSuccessState();
}

class _BouncingCorrectionSuccessState extends State<BouncingCorrectionSuccess>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));
    
    _controller.forward();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _animation,
      child: widget.child,
    );
  }
}